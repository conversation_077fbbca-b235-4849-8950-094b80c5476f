<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيق الوقت</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .input-time {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            color: #666;
        }
        .output-time {
            font-weight: bold;
            color: #17a2b8;
            font-size: 16px;
        }
        .test-button {
            background: #ff9500;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #e68400;
        }
    </style>
</head>
<body>
    <h1>اختبار تنسيق الوقت الجديد</h1>
    
    <div class="test-container">
        <h3>اختبار أوقات مختلفة:</h3>
        <button class="test-button" onclick="runTests()">تشغيل الاختبار</button>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h3>اختبار مخصص:</h3>
        <div style="margin-bottom: 15px;">
            <input type="text" id="custom-time" placeholder="أدخل وقت للاختبار" style="padding: 8px; width: 200px; margin-left: 10px;">
            <button class="test-button" onclick="testCustomTime()">اختبار</button>
        </div>
        <div id="custom-result"></div>
    </div>

    <script>
        // نسخة من دالة formatTime المحدثة
        function formatTime(timeString) {
            // التحقق من وجود النص أولاً
            if (!timeString) {
                return "غير محدد";
            }
            
            // إذا كان النص يحتوي على كلمات عربية، اعرضه كما هو
            if (timeString.includes("ظهراً") || timeString.includes("صباحاً") || 
                timeString.includes("مساءً") || timeString.includes("عصراً") || 
                timeString.includes("منتصف الليل") || timeString.includes("الفجر") ||
                timeString.includes("بعد") || timeString.includes("صلاة")) {
                return timeString;
            }
            
            // التحقق من صيغة TIME القديمة (HH:MM:SS أو HH:MM)
            const timePattern = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
            if (timePattern.test(timeString)) {
                try {
                    const time = new Date("2000-01-01 " + timeString);
                    if (!isNaN(time.getTime())) {
                        return time.toLocaleTimeString("ar-SA", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true
                        });
                    }
                } catch (e) {
                    // في حالة الخطأ، اعرض النص كما هو
                }
            }
            
            // إذا لم يكن أي من الصيغ المعروفة، اعرض النص كما هو
            return timeString;
        }

        function runTests() {
            const testCases = [
                // أوقات نصية عربية
                "1.5 ظهراً",
                "2:30 عصراً", 
                "8 مساءً",
                "10:00 صباحاً",
                "منتصف الليل",
                "الفجر",
                "بعد صلاة العصر",
                "بعد صلاة المغرب",
                
                // أوقات بصيغة TIME القديمة
                "13:30:00",
                "14:00:00",
                "20:00:00",
                "10:30:00",
                "00:00:00",
                "05:00:00",
                
                // أوقات بصيغة مختصرة
                "13:30",
                "14:00",
                "20:00",
                "10:30",
                
                // حالات خاصة
                "",
                null,
                undefined,
                "نص غير صحيح",
                "25:00:00", // وقت غير صحيح
            ];

            let resultsHTML = "";
            
            testCases.forEach((testCase, index) => {
                const result = formatTime(testCase);
                const displayInput = testCase === null ? "null" : 
                                   testCase === undefined ? "undefined" : 
                                   testCase === "" ? "(فارغ)" : testCase;
                
                resultsHTML += `
                    <div class="test-item">
                        <span class="input-time">المدخل: ${displayInput}</span>
                        <span class="output-time">النتيجة: ${result}</span>
                    </div>
                `;
            });
            
            document.getElementById('test-results').innerHTML = resultsHTML;
        }

        function testCustomTime() {
            const customTime = document.getElementById('custom-time').value;
            const result = formatTime(customTime);
            
            document.getElementById('custom-result').innerHTML = `
                <div class="test-item">
                    <span class="input-time">المدخل: ${customTime || "(فارغ)"}</span>
                    <span class="output-time">النتيجة: ${result}</span>
                </div>
            `;
        }

        // تشغيل الاختبار تلقائياً عند تحميل الصفحة
        window.onload = function() {
            runTests();
        };
    </script>
</body>
</html>
