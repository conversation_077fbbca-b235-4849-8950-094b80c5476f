
<?php
$Title_page = 'تفاصيل الحجز';



if(!isset($_SESSION['Omra'])){
  header("Location:".$Site_URL); exit();
}
include('header.php');
include('navbar.php');

$data = $_SESSION['Omra'];

$hamla_data = getAllFrom('*' , 'offers' , 'WHERE id= "'.$data['id'].'" ', '');
if(count($hamla_data) <= 0){
    unset($_SESSION['Omra']);
    header("Location:".$Site_URL); exit();
}

/*
echo '<pre>';
print_r($data);
echo '</pre>';

echo '<pre>';
print_r($hamla_data);
echo '</pre>';
*/

echo '
<div class="wrapper ovh">
    <div class="body_content">
        <div class="hader-invo">
    <div class="row">
            <div class="col-12">  <h2>تم تاكيد الحجز بنجاح</h2></div>
           <div class="col-12">   <p>سوف يتم التواصل معك على رقم الواتساب لتاكيد بيانات الرحلة </p> </div>
          
    </div>
</div>


        <section class="our-compare pt5 pb60">
            <img src="images/icon/login-page-icon.svg" alt="" class="login-bg-icon wow fadeInLeft" data-wow-delay="300ms" style="visibility: visible; animation-delay: 300ms; animation-name: fadeInLeft;">
            <div class="container">




<table>
  <tr>
<th scope="row" colspan="2" style="font-size: 16px;color: #247a48;">بيانات الحجز</th>    </tr>
    </tr>
  <tbody>
    <tr>
      <th scope="row"><i class="fas fa-user" style="color: orange;"></i> الاسم</th>
      <td>'.$data['bt_name'].'</td>
    </tr>
    <tr>
      <th scope="row"> <i class="fas fa-phone" style="color: orange;"></i> رقم الواتساب</th>
      <td>'.$data['bt_phone'].'</td>
    </tr>
  </tbody>
  <tfoot>
 </tfoot>
</table>

<table>
  <tr>
<th scope="row" colspan="2" style="font-size: 16px;color: #247a48;">تفاصيل الرحلة</th>    </tr>
  <tbody>
    <tr>
      <th scope="row"> <i class="fa-solid fa-van-shuttle" style="color: orange;"></i>  الباص</th>
      <td>'.($hamla_data[0]['vip'] == 1 ? "باص VIP 3 صفوف" : "باص اساسي 4 صفوف").'</td>
    </tr>
    <tr>
      <th scope="row"> <i class="fas fa-plane" style="color: orange;"></i> الرحلة</th>
      <td>'.$data['hamla'].'</td>
    </tr>
       <tr>
      <th scope="row"><i class="fas fa-bed" style="color: orange;"></i> الغرفه</th>
      <td>'.$data['room_title'].'</td>
    </tr>
     <tr>
      <th scope="row"><i class="fas fa-calendar-alt" style="color: orange;"></i> تاريخ السفر</th>
      <td>'.$data['date_timepicker_end'].'</td>
    </tr>
  </tbody>
  <tfoot>
   </tfoot>
</table>

<table>
  <tr>
<th scope="row" colspan="2" style="font-size: 16px;color: #247a48;">تفاصيل التكلفة</th>    </tr>
      
    </tr>
  <tbody>
  <tr>
      <th scope="row"> السعر</th>
      <td></td>
    </tr>
     <tr>
      <th scope="row">الخصم</th>
      <td>'.$data['discount_display'].'</td>
    </tr>
    <tr>
      <th scope="row">أجمالى السعر</th>
      <td>'.$data['total_price'].'</td>
    </tr>
   
  </tbody>
  <tfoot>
 </tfoot>
</table>






            
                <div class="row wow fadeInRight" data-wow-delay="300ms" style="visibility: visible; animation-delay: 300ms; animation-name: fadeInRight;">
                    <div class="col-lg-6 offset-lg-3">
                            <div class="text-center mb40">
                                <img class="mb25" src="images/header-logo2.svg" alt="">
                          

                                <!-- الأزرار -->
                                <div class="button-container mt-4">
                                    <button class="btn btn-orange" onclick="contactUs()">تواصل معنا</button>
                                    <button class="btn btn-secondary" onclick="printInvoice()">اطبع الفاتورة</button>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>           


                
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
</div>












';

include('footer.php');



