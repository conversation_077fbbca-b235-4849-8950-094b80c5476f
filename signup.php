
<?php
$Title_page = 'تسجيل حساب جديد';


if(isset($_SESSION['OUserData'])){
  header("Location:".$Site_URL); exit();
}

include('header.php');
include('navbar.php');
$msg = '';
if(isset($_POST['submit'])){ 
  $password = trim($_POST['password']);
  $password2 = trim($_POST['password2']);
  $name = trim($_POST['name']);
  $email = trim($_POST['email']);
  $phone = trim($_POST['phone']);



  $check1 = getAllFrom('*' , 'users' , 'WHERE status = 1 AND phone = "'.$phone.'" ', '');
  $check2 = getAllFrom('*' , 'users' , 'WHERE status = 1 AND email = "'.$email.'" ', '');

  if(count($check1) > 0){
    $msg = Show_Alert('danger' , 'رقم الجوال مسجل من قبل.');
  }else if(count($check2) > 0){
    $msg = Show_Alert('danger' , 'البريد الإلكتروني مسجل من قبل.');
  }else if($password != $password2){
    $msg = Show_Alert('danger' , 'كلمات المرور غير متطابقة');
  }else{


    $stmt = $db->prepare("INSERT INTO users ( username , password , phone , email , datee) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 )");  

    $stmt->execute(array( 
        'user_1' => $name  , 
        'user_2' => $password , 
        'user_3' => $phone , 
        'user_4' => $email , 
        'user_5' => time() )) ;

    $check = getAllFrom('*' , 'users' , 'WHERE status = 1 AND phone = "'.$phone.'" ', '');
    if(count($check) > 0){
        $_SESSION['OUserData'] = $check[0];
        header("Location:".$Site_URL); exit();
    }
  }
 
}

echo '
<div class="wrapper ovh">
    <div class="body_content">
      <!-- Our Compare Area -->
      <section class="our-compare pt60 pb60">
        <img src="images/icon/login-page-icon.svg" alt="" class="login-bg-icon wow fadeInLeft" data-wow-delay="300ms" style="visibility: visible; animation-delay: 300ms; animation-name: fadeInLeft;">
        <div class="container">
          <div class="row wow fadeInRight" data-wow-delay="300ms" style="visibility: visible; animation-delay: 300ms; animation-name: fadeInRight;">
            <div class="col-lg-6 offset-lg-3">
              <div class="log-reg-form signup-modal form-style1 bgc-white p50 p30-sm default-box-shadow2 bdrs12">
              <form method="post">
                <div class="text-center mb40">
                  <img class="mb25" src="images/header-logo2.svg" alt="">
                  <h2>انشاء حساب جديد</h2>
                  <p class="text">يمكنك انشاء حساب جديد لمتابعه وتعديل حجوزاتك</p>
                </div>
                <div class="mb25">
                  <label class="form-label fw600 dark-color">الإسم بالكامل</label>
                  <input type="text" name="name" class="form-control" placeholder="الإسم بالكامل">
                </div>

                <div class="mb25">
                  <label class="form-label fw600 dark-color">رقم الجوال</label>
                  <input type="text" name="phone" class="form-control" placeholder="رقم الجوال">
                </div>

                <div class="mb25">
                  <label class="form-label fw600 dark-color">البريد الإلكتروني</label>
                  <input type="text" name="email" class="form-control" placeholder="البريد الإلكتروني">
                </div>
                 
                <div class="mb15">
                  <label class="form-label fw600 dark-color">كلمة المرور</label>
                  <input type="text" name="password" class="form-control" placeholder="كملة المرور">
                </div>
                <div class="mb15">
                  <label class="form-label fw600 dark-color">تأكيد كلمة المرور</label>
                  <input type="text" name="password2" class="form-control" placeholder="كملة المرور">
                </div>
                <div class="checkbox-style1 d-block d-sm-flex align-items-center justify-content-between mb10">
                  <label class="custom_checkbox fz14 ff-heading">تذكرني
                    <input type="checkbox" checked="checked">
                    <span class="checkmark"></span>
                  </label>
                  <a class="fz14 ff-heading" href="#">هل نسيت كلمة المرور؟</a>
                </div>
                <div class="d-grid mb20">
                  <button class="ud-btn btn-thm" name="submit" type="submit">تسجيل دخول <i class="fal fa-arrow-right-long"></i></button>
                </div>
                '.$msg.'
              </form>
                
                <p class="dark-color text-center mb0 mt10">هل لست مسجل ؟ <a class="dark-color fw600" href="page-register.html">أنشاء حساب الان</a></p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <a class="scrollToHome" href="#"><i class="fas fa-angle-up"></i></a>
    </div>
</div>
';

include('footer.php');



