# تحديث حقل ساعة المغادرة - دليل التنفيذ

## المشكلة
كان حقل `departure_time` في جدول `bus_trips` من نوع `TIME` مما يجعله محدود بصيغة معينة. المطلوب جعله قابل للكتابة يدوياً بأي نص مثل "1.5 ظهراً".

## الحل المطبق

### 1. الملفات المنشأة
- `update_departure_time_field.sql` - ملف SQL للتحديث
- `update_departure_time.php` - واجهة تنفيذ التحديث
- `insert_sample_trips_with_text_time.php` - إضافة بيانات تجريبية
- `departure_time_instructions.md` - تعليمات مفصلة
- `README_departure_time_update.md` - هذا الملف

### 2. الملفات المحدثة
- `bus-api.php` - تحديث استعلامات الترتيب
- `bus-booking.php` - تحديث دالة عرض الوقت

## خطوات التنفيذ

### الخطوة 1: تنفيذ تحديث قاعدة البيانات
```bash
# افتح المتصفح واذهب إلى:
http://yoursite.com/update_departure_time.php

# اتبع التعليمات واضغط "تأكيد التحديث"
```

### الخطوة 2: إضافة بيانات تجريبية (اختياري)
```bash
# افتح المتصفح واذهب إلى:
http://yoursite.com/insert_sample_trips_with_text_time.php

# اضغط "إضافة الرحلات" أو "حذف القديم وإضافة الجديد"
```

### الخطوة 3: اختبار النظام
```bash
# افتح صفحة حجز الباصات:
http://yoursite.com/bus-booking.php

# جرب البحث عن رحلات وتأكد من ظهور الأوقات النصية
```

## التغييرات في قاعدة البيانات

### قبل التحديث
```sql
departure_time TIME NOT NULL
```

### بعد التحديث
```sql
departure_time VARCHAR(50) NOT NULL        -- النص القابل للتحرير
departure_sort_time TIME                   -- للترتيب الصحيح
```

## أمثلة على الاستخدام

### إضافة رحلة جديدة
```sql
INSERT INTO bus_trips (
    from_city, from_station, to_city, to_station,
    departure_time, departure_sort_time, arrival_time,
    seat_price, total_seats, trip_type
) VALUES (
    'الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل',
    '1.5 ظهراً', '13:30:00', '17:30:00',
    100.00, 50, 'to_mecca'
);
```

### تحديث وقت رحلة موجودة
```sql
UPDATE bus_trips 
SET departure_time = 'بعد صلاة العصر',
    departure_sort_time = '15:30:00'
WHERE id = 1;
```

### أمثلة على الأوقات المدعومة
- `1.5 ظهراً`
- `2:30 عصراً`
- `8 مساءً`
- `10:00 صباحاً`
- `منتصف الليل`
- `الفجر`
- `بعد صلاة المغرب`
- `بعد صلاة العصر`

## الميزات الجديدة

### 1. مرونة في كتابة الأوقات
- يمكن كتابة أي نص تريده
- دعم للتعبيرات العربية
- دعم للأوقات الدينية

### 2. ترتيب صحيح للرحلات
- حقل `departure_sort_time` يضمن الترتيب الصحيح
- الاستعلامات تستخدم هذا الحقل للترتيب

### 3. عرض محسن في الواجهة
- دالة `formatTime()` محدثة
- عرض النص العربي مباشرة
- دعم للصيغ القديمة تلقائياً

## نصائح مهمة

### 1. حقل الترتيب
- **مهم جداً**: احرص على تحديث `departure_sort_time` عند تغيير `departure_time`
- استخدم صيغة TIME المعيارية: `HH:MM:SS`
- هذا يضمن ترتيب الرحلات بشكل صحيح

### 2. النسخ الاحتياطية
- تم إنشاء نسخة احتياطية تلقائياً أثناء التحديث
- يُنصح بعمل نسخة احتياطية إضافية قبل التنفيذ

### 3. الاختبار
- اختبر النظام بعد التحديث
- تأكد من ظهور الأوقات بشكل صحيح
- تأكد من ترتيب الرحلات

## استكشاف الأخطاء

### المشكلة: لا تظهر الأوقات بشكل صحيح
**الحل**: تأكد من ترميز UTF-8 في قاعدة البيانات والملفات

### المشكلة: الرحلات غير مرتبة بشكل صحيح
**الحل**: تأكد من تحديث حقل `departure_sort_time` بأوقات صحيحة

### المشكلة: أخطاء في JavaScript
**الحل**: تأكد من استخدام علامات اقتباس مزدوجة للنصوص العربية

## الدعم والصيانة

### تحديث الأوقات بشكل جماعي
```sql
-- تحديث جميع الرحلات الصباحية
UPDATE bus_trips 
SET departure_time = CONCAT(HOUR(departure_sort_time), ':00 صباحاً')
WHERE HOUR(departure_sort_time) BETWEEN 6 AND 11;
```

### البحث والفلترة
```sql
-- البحث بالنص
SELECT * FROM bus_trips WHERE departure_time LIKE '%ظهراً%';

-- البحث بالوقت للترتيب
SELECT * FROM bus_trips 
WHERE departure_sort_time BETWEEN '12:00:00' AND '15:00:00'
ORDER BY departure_sort_time;
```

## الخلاصة

تم تحديث النظام بنجاح ليدعم:
- ✅ كتابة أوقات المغادرة بأي نص عربي
- ✅ ترتيب صحيح للرحلات
- ✅ عرض محسن في الواجهة
- ✅ دعم للصيغ القديمة والجديدة
- ✅ مرونة كاملة في التحرير

الآن يمكنك كتابة "1.5 ظهراً" أو أي نص آخر في حقل ساعة المغادرة وسيظهر كما هو في كارت الرحلة!
