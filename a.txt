<?php

$hotel = json_decode($UmrahData[0]['hotelid']); 
$k = [] ; $d = []; $k_photos = []; $d_photos = [];
if(isset($hotel->k)){
    $k = getAllFrom('*' , 'hotels' , 'WHERE id= "'.$hotel->k.'" ', '');
    if(count($k) <= 0){
        http_response_code(404);
		header("Location:".$Site_URL."/error404"); exit();
    }
    $k_photos = json_decode($k[0]['photos']);
    for ($i=count($k_photos); $i <= 5 ; $i++) { 
        array_push($k_photos  , $default_image);
    } 

}

if(isset($hotel->d) && $hotel->d != 0){
    $d = getAllFrom('*' , 'hotels' , 'WHERE id= "'.$hotel->d.'" ', '');
    if(count($d) <= 0){
        http_response_code(404);
		header("Location:".$Site_URL."/error404"); exit();
    }

    $d_photos = json_decode($d[0]['photos']);
    for ($i=count($d_photos); $i <= 5 ; $i++) { 
        array_push($d_photos  , $default_image);
    } 
}

$mvist = $hotel->d == 0 ? 'd-none' : '';

$Title_page = $UmrahData[0]['title'] != '' ? $UmrahData[0]['title'] : 'حملة عمرة '.$k[0]['name'].' من '.$UmrahData[0]['from_to'];
$Page_Description = 'حملة عمرة '.$k[0]['name'].' من '.$UmrahData[0]['from_to'].' بتاريخ '.date("Y-m-d" , $UmrahData[0]['dateCreated']) . ' لمدة '.$UmrahData[0]['duration'].' أيام';


$stmt = $db->prepare("UPDATE offers SET views  = :var1 WHERE  id = :var0 ");  
$stmt->execute(array(
    'var1'  => $UmrahData[0]['views'] +1 ,
    'var0'  => $UmrahData[0]['id']
));

$vip = $UmrahData[0]['vip'] ? 'VIP' : 'إقتصادية';
 
include('header.php');
include('navbar.php');
 
echo '
<section class="pt20 pb90 bgc-f7">
    <div class="container">
    <div class="row wow fadeInUp" data-wow-delay="100ms">
        <div class="col-lg-12">
            <div class="single-property-content mb0-md">
                <h2 class="sp-lg-title">'.$Title_page.'</h2>
                <div class="pd-meta mb15 d-md-flex align-items-center">
                    <a class="text fz15 mb-0 bdrr1 pl10 bdrrn-sm" href="javascript:void(0)"><span class="fa fa-map-marker-alt"></span>من '.$UmrahData[0]['from_to'].'</a>
                    <a class="text fz15 mb-0 bdrr1 pr10 pl10 bdrrn-sm" href="javascript:void(0)"><span class="fa fa-calendar-alt"></span>'.$UmrahData[0]['duration'].' أيام</a>
                    <a class="text fz15 mb-0 pr10 pl10 bdrrn-sm" href="javascript:void(0)"><span class="fa fa-certificate"></span>رحلة '.$vip.'</a>
                </div>
            </div>
        </div> 
    </div>
    <div class="row mb30 mt5 wow fadeInUp" data-wow-delay="300ms">
        <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p15 mb30 overflow-hidden position-relative">
            <h4 class="title fz17 mb30">معلومات الرحلة</h4>
            <div class="row">
            ';
            $offerInfo = json_decode($UmrahData[0]['info']);
            for ($i=0; $i < count($offerInfo) ; $i++) { 
                echo '
                <div class="col-sm-6 col-lg-4">
                    <div class="iconshotel overview-element mb10 d-flex align-items-center">
                    <span class="icon '.$offerInfo[$i]->ico.'"></span>
                    <div class="mr15">
                        <h6 class="mb-0">'.$offerInfo[$i]->key.'</h6>
                        <p class="text mb-0 fz15">'.$offerInfo[$i]->val.'</p>
                    </div>
                    </div>
                </div>
                ';
            }
            echo ' 
            </div>
        </div>
        <div class="col-sm-12">
            <h4 class="title fz17 mb30 text-center">'.$k[0]['name'].'</h4>
        </div>
        <div class="col-sm-6">
            <div class="sp-img-content mb15-md">
                <a class="popup-img preview-img-1 sp-img" href="'.$Site_URL.'/'.$k_photos[0].'">
                <img class="w-100" src="'.$Site_URL.'/'.$k_photos[0].'" alt="'.$k[0]['name'].'_1">
                </a>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="row">
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-2 sp-img mb10" href="'.$Site_URL.'/'.$k_photos[1].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[1].'" alt="'.$k[0]['name'].'_2">
                    </a>
                </div>
                </div>
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-3 sp-img mb10" href="'.$Site_URL.'/'.$k_photos[2].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[2].'" alt="'.$k[0]['name'].'_3">
                    </a>
                </div>
                </div>
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-4 sp-img" href="'.$Site_URL.'/'.$k_photos[3].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[3].'" alt="'.$k[0]['name'].'_4">
                    </a>
                </div>
                </div>
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-5 sp-img" href="'.$Site_URL.'/'.$k_photos[4].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[4].'" alt="'.$k[0]['name'].'_5">
                    </a>
                    <a href="'.$Site_URL.'/'.$k_photos[4].'" class="all-tag popup-img">عرض جميع الصور</a>
                </div>
                </div>
            </div>
        </div>
        ';
        $k_Info = $k[0]['info'] != '' ? json_decode($k[0]['info']) : [];
        if(count($k_Info) > 0){
        echo '
        <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p15 mt30 overflow-hidden position-relative">
            <h4 class="title fz17 mb30">معلومات عن '.$k[0]['name'].'</h4>
            <div class="row">
        ';
            for ($i=0; $i < count($k_Info) ; $i++) { 
                echo '
                <div class="col-sm-6 col-lg-4">
                    <div class="overview-element mb25 d-flex align-items-center">
                    <span class="icon '.$k_Info[$i]->ico.'"></span>
                    <div class="mr15">
                        <h6 class="mb-0">'.$k_Info[$i]->key.'</h6>
                        <p class="text mb-0 fz15">'.$k_Info[$i]->val.'</p>
                    </div>
                    </div>
                </div>
                ';
            }
        echo '
            </div>
        </div>
        ';
        }

        if(count($d) > 0){
        echo '
        <div class="col-sm-12">
            <h4 class="title fz17 mb30 mt30 text-center">'.$d[0]['name'].'</h4>
        </div>
        <div class="col-sm-6">
            <div class="sp-img-content mb15-md">
                <a class="popup-img preview-img-1 sp-img" href="'.$Site_URL.'/'.$d_photos[0].'">
                <img class="w-100" src="'.$Site_URL.'/'.$d_photos[0].'" alt="'.$d[0]['name'].'_1">
                </a>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="row">
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-2 sp-img mb10" href="'.$Site_URL.'/'.$d_photos[1].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$d_photos[1].'" alt="'.$d[0]['name'].'_2">
                    </a>
                </div>
                </div>
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-3 sp-img mb10" href="'.$Site_URL.'/'.$d_photos[2].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$d_photos[2].'" alt="'.$d[0]['name'].'_3">
                    </a>
                </div>
                </div>
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-4 sp-img" href="'.$Site_URL.'/'.$d_photos[3].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$d_photos[3].'" alt="'.$d[0]['name'].'_4">
                    </a>
                </div>
                </div>
                <div class="col-6 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-5 sp-img" href="'.$Site_URL.'/'.$d_photos[4].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$d_photos[4].'" alt="'.$d[0]['name'].'_5">
                    </a>
                    <a href="'.$Site_URL.'/'.$d_photos[4].'" class="all-tag popup-img">عرض جميع الصور</a>
                </div>
                </div>
            </div>
        </div>
        ';
            $d_Info = $d[0]['info'] != '' ? json_decode($d[0]['info']) : [];
            if(count($d_Info) > 0){
            echo '
            <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p15 mt30 overflow-hidden position-relative">
                <h4 class="title fz17 mb30">معلومات عن '.$d[0]['name'].'</h4>
                <div class="row">
            ';
                for ($i=0; $i < count($d_Info) ; $i++) { 
                    echo '
                    <div class="col-sm-6 col-lg-4">
                        <div class="overview-element mb25 d-flex align-items-center">
                        <span class="icon '.$d_Info[$i]->ico.'"></span>
                        <div class="mr15">
                            <h6 class="mb-0">'.$d_Info[$i]->key.'</h6>
                            <p class="text mb-0 fz15">'.$d_Info[$i]->val.'</p>
                        </div>
                        </div>
                    </div>
                    ';
                }
            echo '
                </div>
            </div>
            ';
            }
        }
        echo ' 
    </div>
    <div class="row mb30 mt0 wow fadeInUp ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 mt0 overflow-hidden position-relative" data-wow-delay="300ms">
        <div class="col-md-12">
            <h4 class="title fz17 mb30">الحجز</h4>
        </div>
        <div class="col-md-12">
            ';
             
            $price = $UmrahData[0]['aprice'] != '' ? json_decode($UmrahData[0]['aprice']) : [];
            for ($i=0; $i < count($price) ; $i++) { 
                $borb = $i < count($price)-1 ? 'bdrb1' : '';
                echo '
                <div class="walkscore d-sm-flex align-items-center mb20 '.$borb.'" id="offer-'.$i.'">
                     
                            <span class="icon ml15 mb10-sm '.$price[$i]->icon.'"></span>
                            <div class="details">
                            
                                <p class="dark-color fw600 mb-1 title">'.$price[$i]->title.'</p>
                                <p class="text mb-1">'.$price[$i]->desc.'</p>
                                
                                <div class="pd-meta mb20 d-md-flex align-items-center">
                                ';
                                for ($x=0; $x < count($price[$i]->info) ; $x++) { 
                                    $pad = $x == 0 ? '' : 'pr10';
                                    $por = $x < count($price[$i]->info)-1 ? 'bdrr1' : '';
                                    echo '<a class="text fz15 mb-0 '.$por.' '.$pad.' pl10 bdrrn-sm" href="javascript:void(0)"><span class="fa '.$price[$i]->info[$x]->icon.'"></span>'.$price[$i]->info[$x]->name.'</a>';
                                }
                                echo ' 
                                </div>
                           
                            </div>
                    <div class="text-center conf">
                        <a href="'.$Site_URL.'/confirm/'.base64_encode(json_encode(['id'=> $UmrahData[0]['id'] ] , JSON_UNESCAPED_UNICODE )).'" class="ud-btn udx-btn btn-thm-border"> <i class="far fa-check-circle"></i> احجز الآن</a>
                    </div>
                </div>
                ';
            }
            echo ' 
        </div>
    </div>
    </div>
</section>

<div class="modal fade" id="book-trip" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staticBackdropLabel">'.$Title_page.'</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
            <input id="bt_hamla" type="hidden" class="form-control" value="'.$Title_page.'">
            <div class="row">
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">الإسم</label>
                            <input id="bt_name" type="text" class="form-control" placeholder="الإسم">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">رقم الجوال</label>
                            <input id="bt_phone" type="text" class="form-control" placeholder="رقم الجوال">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">عدد الأفراد</label>
                            <input id="bt_people" type="number" class="form-control" value="1" min="1" max="20">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">تاريخ الرحلة</label>
                            <input type="text" id="date_timepicker_end" value="'.date("Y-m-d").'" class="form-control" placeholder="تاريخ الرحلة">
                        </div>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">الحجز</label>
                            <div class="bootselect-multiselect">
                                <select id="bt_book" class="form-control">
                                ';
                                for ($i=0; $i < count($price) ; $i++) { 
                                    echo '<option value="'.$i.'">'.$price[$i]->title.'</option>';
                                }
                                echo ' 
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="checkbox-style1">
                        <label class="custom_checkbox">زيارة المدينة +15 ريال
                            <input id="bt_mvist" type="checkbox">
                            <span class="checkmark"></span>
                        </label> 
                    </div>
                </div>
            </div>
      </div>
      <div class="modal-footer">
        <a onclick="ConfirmBookTrip('.$UmrahData[0]['id'].' , '.$i.')" href="javascript:void(0)" class="ud-btn udx-btn btn-thm-border"> <i class="far fa-check-circle"></i> تأكيد الحجز</a>
      </div>
    </div>
  </div>
</div> 

<div class="modal fade" id="res-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staticBackdropLabel">تأكيد الحجز</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
        <div class="modal-body" id="res">

        </div>
      </div>
    </div>
</div> 
';
include('footer.php');