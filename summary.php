<?php
$Title_page = 'ملخص الحجز';
include('header.php');
include('navbar.php');

// التقاط البيانات من الرابط
$title = isset($_GET['title']) ? urldecode($_GET['title']) : '';
$hotel_name = isset($_GET['hotel_name']) ? urldecode($_GET['hotel_name']) : '';
$departure_date = isset($_GET['departure_date']) ? $_GET['departure_date'] : '';
$return_date = isset($_GET['return_date']) ? $_GET['return_date'] : '';
$total_price = isset($_GET['total_price']) ? urldecode($_GET['total_price']) : '';

echo '
<section id="main-container" class="main-content home-page-default container inner">
    <div class="row responsive-medium">
        <div id="main-content" class="col-12">
            <div class="site-main layout-blog" role="main">
                <h3>ملخص الحجز</h3>
                <div class="summary-details">
                    <ul>
                        <li><strong>عنوان الرحلة: </strong>' . htmlspecialchars($title) . '</li>
                        <li><strong>اسم الفندق: </strong>' . htmlspecialchars($hotel_name) . '</li>
                        <li><strong>تاريخ السفر: </strong>' . htmlspecialchars($departure_date) . '</li>
                        <li><strong>تاريخ العودة: </strong>' . htmlspecialchars($return_date) . '</li>
                        <li><strong>إجمالي السعر: </strong>' . htmlspecialchars($total_price) . '</li>
                    </ul>
                </div>
                <a href="index.php" class="ud-btn udx-btn btn-thm-border"> العودة إلى الصفحة الرئيسية </a>
            </div>
        </div>
    </div>
</section>
';

include('footer.php');
?>
