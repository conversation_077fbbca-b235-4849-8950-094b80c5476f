<?php
echo "<h2>اختبار API الباصات</h2>";

// اختبار 1: جلب المدن
echo "<h3>1. اختبار جلب المدن:</h3>";

// استخدام cURL بدلاً من file_get_contents
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/moo/bus-api.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'action' => 'get_cities',
    'trip_type' => 'to_mecca'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
$result = curl_exec($ch);
curl_close($ch);

echo "Response: " . $result . "<br><br>";

// اختبار 2: جلب المحطات
echo "<h3>2. اختبار جلب المحطات:</h3>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/moo/bus-api.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'action' => 'get_stations',
    'trip_type' => 'to_mecca',
    'city' => 'Riyadh'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
$result = curl_exec($ch);
curl_close($ch);

echo "Response: " . $result . "<br><br>";

// اختبار 3: البحث عن الرحلات
echo "<h3>3. اختبار البحث عن الرحلات:</h3>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/moo/bus-api.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'action' => 'search_trips',
    'trip_type' => 'to_mecca',
    'from_city' => 'Riyadh',
    'from_station' => 'Al-Batha Station',
    'travel_date' => date('Y-m-d', strtotime('+1 day')),
    'passenger_count' => 2
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
$result = curl_exec($ch);
curl_close($ch);

echo "Response: " . $result . "<br><br>";
?>
