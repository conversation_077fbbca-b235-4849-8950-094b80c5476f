<?php
ob_start();
$Title_page = 'الفنادق' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
//---------------------------------------------------
$info = '[]';
$photos = '[]'; 
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])){
    $ch = getAllFrom('*' , 'hotels' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){ 
        $info = $ch[0]['info'] != '' ? $ch[0]['info'] : '[]'; 
        $photos = $ch[0]['photos'] != '' ? $ch[0]['photos'] : '[]'; 
    ?>
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
                    <h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على <b>"<?php echo $ch[0]['name'];?>"</b></h4>
                    <a style="color:#fff" href="hotels.php" class="btn btn-sm btn-gradient-danger waves-effect waves-light mrb_10 padrl20">رجوع</a>
				</div>
            </div>
        </div> 
    </div>

    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <input value="<?php echo $ch[0]['id'];?>" class="form-control" type="hidden" id="var0">
                        <div class="col-md-8">
                            <div class="form-group">
                            <label class="form-control-label">إسم الفندق</label>
                                <input type="text" value="<?php echo $ch[0]['name'];?>" id="var1" class="form-control">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                            <label class="form-control-label">التقييم</label>
                                <input type="number" min="1" max="10" value="<?php echo $ch[0]['rate'];?>" id="var2" class="form-control">
                            </div>
                        </div> 
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
                    <h6 class="mb-20">المعلومات <a onclick="AddInput()"><span class="text-end-flout"><i class="icoz mdi mdi-plus-box"></i></span></a></h6>	
                    <div class="info-res"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
                    <h6 class="mb-20">الصور <a onclick="AddPhoto()"><span class="text-end-flout"><i class="icoz mdi mdi-plus-box"></i></span></a></h6>	
                    <div class="photo-res"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <button onclick="SaveEdit()" id="saveBtn" class="btn btn-gradient-primary btn-round  waves-effect waves-light"><i class="icoz mdi mdi-file-document-box"></i> حفظ التعديلات </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <?php
    }else{
		header('Location: hotels.php'); exit();
    } 
//---------------------------------------------------	
}else{ 
    ?>
    <div class="row">
        <div class="col-md-12 col-sm-12">
            <div class="card">
                <div class="card-body">
                    <a onclick="AddNewHotel()" style="color:#fff" class="btn btn-md btn-gradient-primary waves-effect waves-light">إضافة فندق جديد</a>
                </div>
            </div><hr>
        </div>			
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
            <div class="card-body">
            
                <table id="datatables" style="display:none" class="table table-striped  table-hover " cellspacing="0" width="100%" style="width:100%">
                <thead>
                    <tr>
                    <th class="all padding30">رقم </th>
                    <th class="mobile-p desktop padding30">الأسم</th>
                    <th class="mobile-p desktop padding30">التقييم</th> 
                    <th class="mobile-p desktop padding30">تاريخ الإضافة </th>
                    <th class="mobile-p desktop padding30">الإجراء </th>
                    </tr>
                </thead>
                <tbody id="DataTableAll"></tbody>
                </table>
            </div>
            </div>
        </div>
        <!-- end col -->
    </div>
<?php
}
  
include('footer.php'); 
ob_end_flush();
?>
<script>
    var infoJson = JSON.parse(`<?php echo $info;?>`);
    var photosJson = JSON.parse(`<?php echo $photos;?>`);
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'hotels'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "simple",
                "lengthMenu": [
                  [50, 100, 200, 500, 1000, -1],
                  [50, 100, 200, 500, 1000, "كل  الفنادق"]
                ],
                'destroy': true,
                responsive:true,
                //"order": [[ 2, "asc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن فندق",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
    });

    function AddNewHotel(){
        Swal({
        title: 'هل انت متأكد؟',
        text: 'هل انت متاكد من انك تريد اضافة فندق جديد؟',
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
            $.post("ajax.php", { action : 'AddNewHotel' } ,function(datax){ 
                Swal({
                    title: 'تم الإجراء بنجاح',
                    text: "",
                    type: 'success',
                    confirmButtonColor: '#3085d6',
                    confirmButtonText: 'إنهاء',
                });
                if(datax != '' && datax > 0){
                    setTimeout(() => {
                        window.location.href = site_url + '/admincp/hotels.php?action=edit&id='+datax;
                    }, 1000);
                }
            }); 
        }
      })
    }


    function DelHotelAction(id){
        Swal({
        title: 'هل أنت متأكد انك تريد حذف هذا الفندق؟',
        text: "برجاء العلم انه سيتم الحذف بشكل نهائي",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'DelHotelAction',id : id} ,function(data){ 
            Swal({
              title: 'تم الحذف',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetData();
          });  
          
        }
      })
    }

    function getInfoInput(){
        if(infoJson.length <= 0){
            $('.info-res').html('<div class="alert alert-info role="alert">لا يوجد اى معلومات عن الفندق</div></div>');
        }else{
            var html = '';
            for (let i = 0; i < infoJson.length; i++) {
                html += `
                <div class="row pr" elm="${infoJson[i].key}">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-control-label">العنوان</label>
                            <input onchange="SetInputValue()" type="text" value="${infoJson[i].key}" name="key" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-control-label">القيمه</label>
                            <input onchange="SetInputValue()" type="text" value="${infoJson[i].val}" name="val" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-control-label">الايقونة</label>
                            <input onchange="SetInputValue()" type="text" value="${infoJson[i].ico}" name="ico" class="form-control ltr">
                        </div>
                    </div>
                     
                    <a onclick="RemoveInput(this)" class="remibtn"><span class="text-end-flout"><i class="icoz mdi mdi-delete"></i></span></a> 
                </div>
                `;
            }
            $('.info-res').html(html);
        } 
    }
    getInfoInput();

    function RemoveInput(elm){
        infoJson = $.grep(infoJson, function(item) {
            return item.key !== $(elm).closest('.pr').attr('elm');
        });
		getInfoInput();
	}

    function AddInput(){
        infoJson.push({key:'' , val:'' , ico: 'fa fa-kaaba'});
        getInfoInput();
    }

    function getPhotoInput(){
        if(photosJson.length <= 0){
            $('.photo-res').html('<div class="alert alert-info role="alert">لا يوجد اى صور</div></div>');
        }else{
            var html = '';
            for (let i = 0; i < photosJson.length; i++) {
                html += `
                <div class="row pr" elm="${photosJson[i]}">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="form-control-label">رابط الصورة</label>
                            <input onchange="SetInputValue()" type="text" value="${photosJson[i]}" name="photo" class="form-control ltr">
                        </div>
                    </div> 
                     
                    <a onclick="RemovePhotoInput(this)" class="remibtn"><span class="text-end-flout"><i class="icoz mdi mdi-delete"></i></span></a> 
                </div>
                `;
            }
            $('.photo-res').html(html);
        } 
    }
    getPhotoInput();

    function RemovePhotoInput(elm){
        photosJson = $.grep(photosJson, function(item) {
            return item !== $(elm).closest('.pr').attr('elm');
        });
		getPhotoInput();
	}

    function AddPhoto(){
        photosJson.push('');
        getPhotoInput();
    }

    function SetInputValue(){
        var tmp_i = []; 
        var tmp_p = [];
        $('.info-res').find('.pr').each(function(i,elm){
            tmp_i.push({
                key : $(elm).find('input[name="key"]').val(),
                val : $(elm).find('input[name="val"]').val(),
                ico : $(elm).find('input[name="ico"]').val(),
            })
        });
        $('.photo-res').find('.pr').each(function(i,elm){
            tmp_p.push($(elm).find('input[name="photo"]').val())
        });
        infoJson = tmp_i;
        photosJson = tmp_p;
    }
    function SaveEdit(){
        SetInputValue();
        $('#saveBtn').hide(100);
        var var0 = $('#var0').val();
        var var1 = $('#var1').val();
        var var2 = $('#var2').val();
        var var3 = infoJson;
        var var4 = photosJson;
        
        
        $.post("ajax.php", { action : 'SaveEditHotel' , var0 : var0 , var1 : var1 , var2 : var2 , var3 : infoJson , var4 : photosJson} ,function(data){
            location.reload();
        });
        
    }
</script>