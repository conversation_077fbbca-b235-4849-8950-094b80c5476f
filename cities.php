<?php

$Title_page = $CitiesData[0]['title-s'];
$Title_pa = $CitiesData[0]['title'];
$Page_Description = strip_tags($CitiesData[0]['descr']);
$Page_KeyWords = $CitiesData[0]['tags']; 
$Page_images = $Site_URL.'/'.$CitiesData[0]['photo'];
$Title_city = $CitiesData[0]['name'];







// استرجاع قيم مدة الرحلة من قاعدة البيانات
$durations = [];
foreach ($Hamalt as $offer) {
    $offer_duration = trim($offer['duration']); // تأكد من عدم وجود مسافات
    if (!in_array($offer_duration, $durations) && !empty($offer_duration)) {
        $durations[] = $offer_duration; // إضافة القيمة إلى المصفوفة إذا لم تكن موجودة
    }
}

// استرجاع قيم مسار الرحلة من قاعدة البيانات
$trip_routes = [];
foreach ($Hamalt as $offer) {
    $madinahValue = trim($offer['madinah']); // تأكد من عدم وجود مسافات
    if (!in_array($madinahValue, $trip_routes)) {
        $trip_routes[] = $madinahValue; // إضافة القيمة إلى المصفوفة إذا لم تكن موجودة
    }
}

$bus_types = [];
foreach ($Hamalt as $offer) {
    $offer_bus_type = trim($offer['vip']); // تأكد من عدم وجود مسافات
    if (!in_array($offer_bus_type, $bus_types) && $offer_bus_type !== '') { // تأكد من أنه ليس فارغًا
        $bus_types[] = $offer_bus_type; // إضافة القيمة إلى المصفوفة إذا لم تكن موجودة
    }
}

// إذا كان هناك خيار واحد فقط، لا تعرض "جميع الباصات"
$show_all_buses = count($bus_types) > 1;



// معالجة الطلبات AJAX فقط (عند البحث)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  // استرجاع الفلاتر من الطلب
  $filter_duration = isset($_POST['duration']) && $_POST['duration'] != '0' ? trim($_POST['duration']) : '';
  $filter_bus_type = isset($_POST['bus_type']) ? trim($_POST['bus_type']) : '';
  $filter_trip_route = isset($_POST['trip_route']) ? trim($_POST['trip_route']) : '';

  // فلترة العروض بناءً على المدخلات
  $filtered_offers = [];
  if (count($Hamalt) > 0) {
      foreach ($Hamalt as $offer) {
          $offer_duration = trim($offer['duration']);
          $offer_bus_type = $offer['vip'];
          $offer_trip_route = $offer['madinah'];

          // تحقق من مطابقة مدة الرحلة
          $is_duration_match = ($filter_duration === '' || $filter_duration == $offer_duration);
          // تحقق من مطابقة نوع الباص
          $is_bus_type_match = ($filter_bus_type === '' || $filter_bus_type == $offer_bus_type);
          // تحقق من مطابقة مسار الرحلة
          $is_trip_route_match = ($filter_trip_route === '' || $filter_trip_route == $offer_trip_route);

          // إضافة العرض إذا كانت الفلاتر متطابقة
          if ($is_duration_match && $is_bus_type_match && $is_trip_route_match) {
              $filtered_offers[] = $offer;
          }
      }

      // عرض النتائج
      if (count($filtered_offers) > 0) {
          foreach ($filtered_offers as $filtered_offer) {
              echo '<div class="col-md-3">'.GetOfferBox($filtered_offer).'</div>';
          }
      } else {
          // رسالة توضح عدم وجود نتائج
          echo '<div class="alert alert-danger">لا توجد رحلات متاحة بهذه الفلاتر.</div>';
      }
  }
  exit;  // إنهاء العملية بعد عرض النتائج
}


include('header.php');
include('navbar.php');

echo '

<div class="col-lg-12">
    <div class="d-flex align-items-center mb-3 p-2 custom-background-box2" 
         style=" cursor: pointer;">
        <div class="me-2 icon-container">
            <img src="img/location.png" alt="location" class="search-icon"> 


        </div>
        <div class="flex-grow-1 text-container">
            <p class="location-text"> '.$Title_city.' - رحلات عمرة متاح الان من '.$Title_city.' </p> 
        </div>
        <div class="ms-2">
    <i class="fas fa-chevron-down"></i> 
</div>

    </div>
</div>

<section>
    <div class="container mt10 mb20">

    <div class="row">
        <div class="col-lg-12 mb0">
            <div class="slider-3-grid owl-carousel owl-theme">
               <div class="item">
                    <a href="#">
                        <img src="img/slider2.jpg" alt="Image 2" class="img-fluid">
                    </a>
                </div>
                <div class="item">
                    <a href="#">
                        <img src="img/slider3.png" alt="Image 1" class="img-fluid">
                    </a>
                </div>
                   <div class="item">
                    <a href="#">
                        <img src="img/slider3.jpg" alt="Image 3" class="img-fluid">
                    </a>
                </div>
            </div>
        </div>
    </div>

<div class="col-lg-12">
    <div class="d-flex align-items-center mt15 mb-3 p-2 custom-background-box2" 
         style="border: 1px solid #ddd; border-radius: 5px; cursor: pointer;">
        <div class="me-2 icon-container">
            <img src="img/search.png" alt="رمز البحث" class="search-icon"> 
        </div>
        <div class="flex-grow-1 text-container">
            <span class="highlight-text"> لديك كوبون خصم 30 ريال </span>
        </div>
        <div class="ms-2">
            <button class="search-button">
              MO30
            </button>
        </div>
    </div>
</div>









     </div>
     
</section>



<section class="home-banner-style1 p0 mt-20">
    <div class="home-style3">
        <div class="container">
                    <div class="row">

                    
                <div class="col-xl-12 mx-auto">
                    <div class="inner-banner-style1 text-center">
                        <div class="adv0ance-search-tab mt0 mt0-md mx-auto animate-up-3">
                            <div class="home-text-slider">
                            
                                <div class="search-filters2 d-md-block mt-4">
<p style="font-size: 16px;font-weight: 600;">ابحث عن رحلة العمرة المناسبة لك </p>
    <form id="searchForm" method="POST" action="">
        <div class="row">
        
            <div class="col-12 col-lg-4 mb-3">
            <div class="d-flex align-items-center">
                    <div class="icon-filter"><img src="img/time-city.png" alt="Bus Icon" class="img-fluid" /></div>   
                    <select class="selectincity" data-live-search="true" data-width="100%" id="duration" name="duration">
                        <option value="">مدة الرحلة </option> 
                        <option value="0">جميع الرحلات</option>
                        ';
                        foreach ($durations as $duration) {
                            echo '<option value="'.htmlspecialchars($duration).'">'.htmlspecialchars($duration).' أيام</option>';
                        }
                        echo '
                    </select>
                </div>
            </div>
            <div class="col-12 col-lg-4 mb-3">
<div class="d-flex align-items-center">
    <div class="icon-filter"><img src="img/trippath.png" alt="Path Icon" class="img-fluid" /></div>         
    <select class="selectincity" data-live-search="true" data-width="100%" id="trip_route" name="trip_route">
        <option value="">مسار الرحلة</option>
         ';
        // إضافة خيارات مسار الرحلة
        foreach ($trip_routes as $madinahValue) {
            if ($madinahValue === '0') {
                $madinahText = 'مكة فقط';
            } elseif ($madinahValue === '1') {
                $madinahText = 'مكة و زيارة مدينة';
            } elseif ($madinahValue === '2') {
                $madinahText = 'مكة واقامة مدينة';
            } else {
                $madinahText = 'غير متوفر';
            }
            echo '<option value="' . htmlspecialchars($madinahValue) . '">' . htmlspecialchars($madinahText) . '</option>';
        }
          echo '
    </select>
</div>

</div>
<div class="col-12 col-lg-4 mb-3">
    <div class="d-flex align-items-center">
        <div class="icon-filter"><img src="img/bus-filter.png" alt="Bus Icon" class="img-fluid" /></div>         
        <select class="selectincity" data-live-search="true" data-width="100%" id="bus_type" name="bus_type">
            <option value="">فئة الباص</option>
              ';
            // إضافة خيار "جميع الباصات" إذا كان هناك أكثر من نوع
            if (count($bus_types) > 1) {
                echo '<option value="">جميع الباصات</option>';
            }
            // إضافة خيارات نوع الباص
            if (in_array(0, $bus_types)) {
                echo '<option value="0">باص أساسي</option>';
            }
            if (in_array(1, $bus_types)) {
                echo '<option value="1">باص VIP</option>';
            }
         echo '
        </select>
    </div>
</div>

            <div class="col-8 col-lg-8">
                <button type="button" id="searchButton" class="btnselectincity"> 
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
                      <div class="col-4 col-lg-4">
                <button type="button" id="clearButton" class="removefilter" onclick="clearFilters()"> 
                    <i class="fas fa-times"></i> مسح 
                </button>
              </div>
        </div>
    </form>
</div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<section class="city-banner">
    <div class="container">
 
        <div class="tab-content" id="myTabContent">
        
            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                <div class="advance-content-style1">
                    <div class="row">
                                               
                        <div class="col-lg-12 col-md-12">
                            <div id="resultsContainer" class="row">
                            ';
                            if (count($Hamalt) <= 0) {
                                echo '<div class="trip-header"><h4><img src="img/time-city.png" alt="Bus Icon" class="img-fluid"> رحلات '.$Title_city.' </h4></div>';
                                echo '
                                <div class="slider-4-grid owl-carousel owl-theme">
                                ';
                                if (count($Hamalt) > 0) {
                                    foreach ($Hamalt as $offer) {
                                         
                                        echo '<div class="item">'.GetOfferBox($offer).'</div>';
                                         
                                    }
                                }
                                echo '
                                </div>';
                            }else{
                                $dyx = getAllFrom('duration' , 'offers' , 'WHERE status = 1 AND catid = "'.$CitiesData[0]['id'].'" ', 'GROUP BY duration ORDER BY duration ASC');
                                for ($d=0; $d < count($dyx) ; $d++) { 

                                    if($d == 1){
                                        echo '
                                        <div class="about-box2 ">
                                                <h4 class="title">'.tr('HOME_ABOUT_01').'</h4>
                                            <p>الحين حجز العمرة الداخلية صار أسهل</p>
                                            <div class="home-bus col-sm-6 col-lg-12">
                                                <div class="position-relative mb0 mb0-md d-flex align-items-center">
                                                    <div class="city-img flex-shrink-0">
                                                        <img src="img/t1.png" alt="">
                                                    </div>
                                                    <div class="flex-shrink-1 ms-3">
                                                        <h6 class="mb-1" style="color: #283647;">أختار الرحلة المناسبة</h6>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="home-bus col-sm-6 col-lg-12">
                                                <div class="position-relative mb0 mb0-md d-flex align-items-center">
                                                    <div class="city-img flex-shrink-0">
                                                        <img src="img/t2.png" alt="">
                                                    </div>
                                                    <div class="flex-shrink-1 ms-3">
                                                        <h6 class="mb-1" style="color: #283647;">أختار الغرفه وعدد الافراد</h6>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="home-bus col-sm-6 col-lg-12">
                                                <div class="position-relative mb0 mb0-md d-flex align-items-center">
                                                    <div class="city-img flex-shrink-0">
                                                        <img src="img/t3.png" alt="">
                                                    </div>
                                                    <div class="flex-shrink-1 ms-3">
                                                        <h6 class="mb-1" style="color: #283647;">تاكيد الحجز</h6>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        ';
                                    }

                                    echo '<div class="trip-header"><h4><img src="img/time-city.png" alt="Bus Icon" class="img-fluid"> رحلات '.$dyx[$d]['duration'].' ايام  من '.$Title_city.' </h4></div>';
                                    echo '
                                    <div class="slider-4-grid owl-carousel owl-theme">
                                    ';
                                    if (count($Hamalt) > 0) {
                                        foreach ($Hamalt as $offer) {
                                            if($offer['duration'] == $dyx[$d]['duration']){
                                                echo '<div class="item">'.GetOfferBox($offer).'</div>';
                                            }
                                        }
                                    }
                                    echo '
                                    </div>';
                                } 
                            }
                            echo '
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>




    </div>
</section>
';

if($CitiesData[0]['descr'] != ''){
  $tags = '';
  if($Page_KeyWords != ''){
      $t = explode(',' , $Page_KeyWords);
      for ($i=0; $i < count($t) ; $i++) { 
          if(trim($t[$i]) != '' ){
              $tags .= '<a href="'.$actual_link.'">'.trim($t[$i]).'</a>';
          }
      }
  }
  echo '
<section class="pt0 pb10 pt30 bgc-f7">
  <div class="container">
    <div class="city-end">';

          if($CitiesData[0]['photo'] != ''){
              echo '
                      <div class="col-12">
                          <div class="row">
                              <div class="col-md-6 mb30">
                                  <img class="w100" src="'.$Site_URL.'/'.$CitiesData[0]['photo'].'" alt="'. $CitiesData[0]['title'].'">

                              </div>
                              
                              
   <div class="col-sm-6">
    <div class="accordion-style1">
        <div class="accordion" id="accordionExample1">
            <div class="accordion-item border-none bgc-transparent">
                <h5 class="accordion-header" id="headingOne1">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne1" aria-expanded="false" aria-controls="collapseOne1">
                        <i class="fas fa-bus pe-2"></i>ما هي الفرق بين انواع الباصات
                    </button>
                </h5>
                <div id="collapseOne1" class="accordion-collapse collapse" aria-labelledby="headingOne1" data-bs-parent="#accordionExample1">
                    <div class="accordion-body">
                        <div class="row">
                            <div class="col-sm-6 col-md-6">
                                <div class="pd-list mb10-sm">
                                    <h4>باصات أساسية</h4>
                                    <p class="text mb10"><i class="fas fa-circle fz6 align-middle pe-2"></i>باصات حديثة ومكيفة تتكون من 4 صفوف</p>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-6">
                                <div class="pd-list">
                                    <h4>باصات VIP</h4>
                                    <p class="text mb10"><i class="fas fa-circle fz6 align-middle pe-2"></i>باصات حديقة ومكيفة تتكون من 3 صفوف</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="accordion-style1">
        <div class="accordion" id="accordionExample2">
            <div class="accordion-item border-none bgc-transparent">
                <h5 class="accordion-header" id="headingOne2">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne2" aria-expanded="false" aria-controls="collapseOne2">
                        <i class="fas fa-calendar-alt pe-2"></i>ما هى مواعيد الرحلات
                    </button>
                </h5>
                <div id="collapseOne2" class="accordion-collapse collapse" aria-labelledby="headingOne2" data-bs-parent="#accordionExample2">
                    <div class="accordion-body">
                        <p>مواعيد الرحلات تعتمد على وجهة الانطلاق. بعض الوجهات توفر رحلات يوميًا، بينما تتوفر وجهات أخرى في أيام محددة من الأسبوع. لذا، يُفضل تحديد وجهة الانطلاق للحصول على تفاصيل دقيقة حول المواعيد المتاحة.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="accordion-style1">
        <div class="accordion" id="accordionExample3">
            <div class="accordion-item border-none bgc-transparent">
                <h5 class="accordion-header" id="headingOne3">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne3" aria-expanded="false" aria-controls="collapseOne3">
                        <i class="fas fa-credit-card pe-2"></i>ما هي طرق الدفع المتاحة
                    </button>
                </h5>
                <div id="collapseOne3" class="accordion-collapse collapse" aria-labelledby="headingOne3" data-bs-parent="#accordionExample3">
                    <div class="accordion-body">
                        <p>نحن نقدم لك خيارات دفع مريحة ومرنة، حيث يمكنك اختيار <strong>الدفع عند الوصول</strong>. هذه الطريقة تضمن لك الحصول على الخدمة التي طلبتها قبل إتمام عملية الدفع، مما يوفر لك راحة البال والثقة في تجربتك.</p>
                        <p>ندرك أهمية هذه الخدمة بالنسبة لك، لذا نحرص على توفير خيارات تدعم احتياجاتك وتضمن لك تجربة سهلة وميسرة. اختر الطريقة التي تناسبك واستمتع برحلتك إلى العمرة بكل يسر وسهولة!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="accordion-style1">
        <div class="accordion" id="accordionExample4">
            <div class="accordion-item border-none bgc-transparent">
                <h5 class="accordion-header" id="headingOne4">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne4" aria-expanded="false" aria-controls="collapseOne4">
                        <i class="fas fa-times pe-2"></i>ما هي رسوم الإلغاء؟
                    </button>
                </h5>
                <div id="collapseOne4" class="accordion-collapse collapse" aria-labelledby="headingOne4" data-bs-parent="#accordionExample4">
                    <div class="accordion-body">
                        <p><strong>لم نقوم بفرض أي رسوم على الإلغاء.</strong> يمكنك إلغاء حجزك دون أي تكاليف إضافية، مما يضمن لك المرونة والراحة في اتخاذ القرار.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="accordion-style1">
        <div class="accordion" id="accordionExample5">
            <div class="accordion-item border-none bgc-transparent">
                <h5 class="accordion-header" id="headingOne5">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne5" aria-expanded="false" aria-controls="collapseOne5">
                        <i class="fas fa-shield-alt pe-2"></i>ما هي ضمان الخدمة في معتمر؟
                    </button>
                </h5>
                <div id="collapseOne5" class="accordion-collapse collapse" aria-labelledby="headingOne5" data-bs-parent="#accordionExample5">
                    <div class="accordion-body">
                        <p>نحن نضمن لك تجربة سفر مميزة ومريحة من خلال تقديم خدمات عالية الجودة ودعم متواصل على مدار الساعة. تلتزم معتمر بتوفير أعلى معايير الأمان والراحة، مما يضمن لك تجربة عمرة سلسة وناجحة.</p>
                        <p class="text mb10"><i class="fas fa-circle fz6 align-middle pe-2"></i>تغيير الفندق فوراً عند وجود أي إزعاج</p>
                        <p class="text mb10"><i class="fas fa-circle fz6 align-middle pe-2"></i>خدمة عملاء متاحة 24/7 لضمان راحتك</p>
                        <p class="text mb10"><i class="fas fa-circle fz6 align-middle pe-2"></i>مرونة كاملة لتعديل خططك أثناء الرحلة</p>
                        <p class="text mb-0"><i class="fas fa-circle fz6 align-middle pe-2"></i>إلغاء مجاني دون رسوم</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
</div>
                                 <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative">
        <div class="autohigh">
                              ';
          }

    echo $CitiesData[0]['descr'];

    if($tags != ''){
        echo '
                <div class="bsp_tags">
                    <hr>
                    <p>الكلمات الدلالية :</p>
                    '.$tags.'
                </div>';
    }

    echo '
         </div>
        <a class="btn-tap" onclick="ShowMore(this)"> قراءة المزيد </a>
        </div>
    </div>
    </div> <!-- نهاية div.row -->
  </div>. <!-- نهاية div.ps-widget -->
</section>'; // تأكد من إغلاق القسم
}




include('footer.php');
?>

