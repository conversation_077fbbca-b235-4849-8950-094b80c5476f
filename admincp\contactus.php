<?php
ob_start();
$Title_page = 'رسائل اتصل بنا' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
$stmt = $db->prepare("UPDATE contactus SET readed = :var1 ");  
$stmt->execute(array( 'var1' =>1 )); 
//---------------------------------------------------
?>
<div class="row">
    <div class="col-12">
        <div class="card">
        <div class="card-body">
           
            <table id="datatables" style="display:none" class="table table-striped  table-hover " cellspacing="0" width="100%" style="width:100%">
              <thead>
                <tr>
                  <th class="all padding30">رقم </th>
                  <th class="mobile-p desktop padding30">الأسم</th>
                  <th class="mobile-p desktop padding30">رقم الموبايل </th>
                  <th class="disabled-sorting none">البريد الالكتروني </th>
                  <th class="disabled-sorting none">الرسالة </th>
                  <th class="disabled-sorting none">رقم الاى بى </th>
                  <th class="mobile-p desktop padding30">تاريخ الإضافة </th>
                  <th class="mobile-p desktop padding30">الإجراء </th>
                </tr>
              </thead>
              <tbody id="DataTableAll"></tbody>
            </table>
        </div>
        </div>
    </div>
    <!-- end col -->
</div>

 
<?php
include('footer.php'); 
ob_end_flush();
?>
<script>
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'contactus'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "simple",
                "lengthMenu": [
                  [50, 100, 200, 500, 1000, -1],
                  [50, 100, 200, 500, 1000, "كل  الرسائل"]
                ],
                'destroy': true,
                responsive:true,
                //"order": [[ 2, "asc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن رسالة",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
    });


    function DelcontactusAction(id){
        Swal({
        title: 'هل أنت متأكد انك تريد حذف هذه الرسالة؟',
        text: "برجاء العلم انه سيتم الحذف بشكل نهائي",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'DelcontactusAction',id : id} ,function(data){ 
            Swal({
              title: 'تم الحذف',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetData();
          });  
          
        }
      })
    }
</script>