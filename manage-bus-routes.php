<?php
$Title_page = 'إدارة الصفحات المخصصة للرحلات';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// معالجة الإجراءات
if ($_POST) {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $db->prepare("INSERT INTO bus_cities (name, link, title, title_s, descr, tags, trip_type, from_city, to_city, from_station, to_station, meta_description, meta_keywords, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['name'], $_POST['link'], $_POST['title'], $_POST['title_s'], 
                        $_POST['descr'], $_POST['tags'], $_POST['trip_type'], $_POST['from_city'], 
                        $_POST['to_city'], $_POST['from_station'], $_POST['to_station'], 
                        $_POST['meta_description'], $_POST['meta_keywords'], $_POST['status']
                    ]);
                    $success_message = "تم إضافة الصفحة بنجاح";
                    break;
                    
                case 'edit':
                    $stmt = $db->prepare("UPDATE bus_cities SET name=?, link=?, title=?, title_s=?, descr=?, tags=?, trip_type=?, from_city=?, to_city=?, from_station=?, to_station=?, meta_description=?, meta_keywords=?, status=? WHERE id=?");
                    $stmt->execute([
                        $_POST['name'], $_POST['link'], $_POST['title'], $_POST['title_s'], 
                        $_POST['descr'], $_POST['tags'], $_POST['trip_type'], $_POST['from_city'], 
                        $_POST['to_city'], $_POST['from_station'], $_POST['to_station'], 
                        $_POST['meta_description'], $_POST['meta_keywords'], $_POST['status'], $_POST['id']
                    ]);
                    $success_message = "تم تحديث الصفحة بنجاح";
                    break;
                    
                case 'delete':
                    $stmt = $db->prepare("DELETE FROM bus_cities WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $success_message = "تم حذف الصفحة بنجاح";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = "خطأ: " . $e->getMessage();
    }
}

// جلب جميع الصفحات
$stmt = $db->query("SELECT * FROM bus_cities ORDER BY trip_type, id");
$routes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب المدن المتاحة من جدول الرحلات
$stmt = $db->query("SELECT DISTINCT from_city FROM bus_trips WHERE trip_type = 'to_mecca' UNION SELECT DISTINCT to_city FROM bus_trips WHERE trip_type = 'from_mecca' ORDER BY from_city");
$available_cities = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo '
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إدارة الصفحات المخصصة للرحلات</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRouteModal">
                    <i class="fa fa-plus"></i> إضافة صفحة جديدة
                </button>
            </div>';

if (isset($success_message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            ' . $success_message . '
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>';
}

if (isset($error_message)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            ' . $error_message . '
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>';
}

echo '
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>اسم الصفحة</th>
                                    <th>الرابط</th>
                                    <th>نوع الرحلة</th>
                                    <th>المسار</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>';

foreach ($routes as $route) {
    $trip_type_text = $route['trip_type'] == 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة';
    $status_badge = $route['status'] == 1 ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
    
    echo '<tr>
            <td>' . $route['id'] . '</td>
            <td>' . htmlspecialchars($route['name']) . '</td>
            <td><code>' . htmlspecialchars($route['link']) . '</code></td>
            <td>' . $trip_type_text . '</td>
            <td>' . htmlspecialchars($route['from_city']) . ' → ' . htmlspecialchars($route['to_city']) . '</td>
            <td>' . $status_badge . '</td>
            <td>
                <a href="bus-route.php?route=' . $route['link'] . '" target="_blank" class="btn btn-sm btn-info">
                    <i class="fa fa-eye"></i> معاينة
                </a>
                <button class="btn btn-sm btn-warning" onclick="editRoute(' . $route['id'] . ')">
                    <i class="fa fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteRoute(' . $route['id'] . ')">
                    <i class="fa fa-trash"></i> حذف
                </button>
            </td>
          </tr>';
}

echo '
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة صفحة جديدة -->
<div class="modal fade" id="addRouteModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة صفحة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الصفحة *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرابط المخصص *</label>
                                <input type="text" class="form-control" name="link" required placeholder="riyadh-to-mecca">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">العنوان الرئيسي *</label>
                        <input type="text" class="form-control" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">العنوان المختصر *</label>
                        <input type="text" class="form-control" name="title_s" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف الصفحة</label>
                        <textarea class="form-control" name="descr" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">نوع الرحلة *</label>
                                <select class="form-select" name="trip_type" required>
                                    <option value="to_mecca">ذهاب إلى مكة</option>
                                    <option value="from_mecca">عودة من مكة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">من المدينة *</label>
                                <select class="form-select" name="from_city" required>';

foreach ($available_cities as $city) {
    echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
}

echo '
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">إلى المدينة *</label>
                                <select class="form-select" name="to_city" required>';

foreach ($available_cities as $city) {
    echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
}

echo '
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">من المحطة</label>
                                <input type="text" class="form-control" name="from_station">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">إلى المحطة</label>
                                <input type="text" class="form-control" name="to_station">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الكلمات المفتاحية</label>
                        <input type="text" class="form-control" name="tags" placeholder="رحلات الرياض مكة، باصات الرياض">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف الميتا</label>
                        <textarea class="form-control" name="meta_description" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">كلمات الميتا المفتاحية</label>
                        <input type="text" class="form-control" name="meta_keywords">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="1">نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editRoute(id) {
    // يمكن تطوير هذه الدالة لاحقاً لفتح modal التعديل
    alert("سيتم تطوير نافذة التعديل قريباً");
}

function deleteRoute(id) {
    if (confirm("هل أنت متأكد من حذف هذه الصفحة؟")) {
        const form = document.createElement("form");
        form.method = "POST";
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>';

include('footer.php');
?>
