<?php
$Title_page = 'اختبار حجز الباص';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

echo '
<div class="container mt-5">
    <h2>اختبار نظام حجز الباصات</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>نموذج البحث</h4>
                </div>
                <div class="card-body">
                    <form id="testSearchForm">
                        <div class="mb-3">
                            <label class="form-label">نوع الرحلة</label>
                            <select class="form-select" id="tripType" name="trip_type">
                                <option value="to_mecca">ذهاب إلى مكة</option>
                                <option value="from_mecca">عودة من مكة</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">المدينة</label>
                            <select class="form-select" id="fromCity" name="from_city">
                                <option value="">اختر المدينة</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">المحطة</label>
                            <select class="form-select" id="fromStation" name="from_station">
                                <option value="">اختر المحطة</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">تاريخ السفر</label>
                            <input type="date" class="form-control" id="travelDate" name="travel_date" value="'.date('Y-m-d', strtotime('+1 day')).'">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">عدد المسافرين</label>
                            <input type="number" class="form-control" id="passengerCount" name="passenger_count" value="1" min="1" max="10">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">البحث عن الرحلات</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>النتائج</h4>
                </div>
                <div class="card-body">
                    <div id="searchResults">
                        <p class="text-muted">قم بالبحث لعرض النتائج</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>سجل الاختبارات</h4>
                </div>
                <div class="card-body">
                    <div id="testLog"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const tripTypeSelect = document.getElementById("tripType");
    const fromCitySelect = document.getElementById("fromCity");
    const fromStationSelect = document.getElementById("fromStation");
    const testSearchForm = document.getElementById("testSearchForm");
    const searchResults = document.getElementById("searchResults");
    const testLog = document.getElementById("testLog");

    function logTest(message, type = "info") {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `<div class="alert alert-${type} alert-sm">[${timestamp}] ${message}</div>`;
        testLog.innerHTML = logEntry + testLog.innerHTML;
    }

    // تحميل المدن عند تغيير نوع الرحلة
    tripTypeSelect.addEventListener("change", loadCities);
    
    function loadCities() {
        const tripType = tripTypeSelect.value;
        logTest(`جاري تحميل المدن لنوع الرحلة: ${tripType}`, "info");
        
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                action: "get_cities",
                trip_type: tripType
            })
        })
        .then(response => response.json())
        .then(data => {
            fromCitySelect.innerHTML = "<option value=\\"\\">اختر المدينة</option>";
            fromStationSelect.innerHTML = "<option value=\\"\\">اختر المحطة</option>";
            
            if (data.success && data.cities) {
                logTest(`تم تحميل ${data.cities.length} مدينة بنجاح`, "success");
                data.cities.forEach(city => {
                    const option = document.createElement("option");
                    option.value = city;
                    option.textContent = city;
                    fromCitySelect.appendChild(option);
                });
            } else {
                logTest("فشل في تحميل المدن: " + (data.message || "خطأ غير معروف"), "danger");
            }
        })
        .catch(error => {
            logTest("خطأ في تحميل المدن: " + error.message, "danger");
        });
    }

    // تحميل المحطات عند اختيار المدينة
    fromCitySelect.addEventListener("change", function() {
        const selectedCity = this.value;
        const tripType = tripTypeSelect.value;
        
        if (selectedCity) {
            logTest(`جاري تحميل محطات المدينة: ${selectedCity}`, "info");
            
            fetch("bus-api.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    action: "get_stations",
                    city: selectedCity,
                    trip_type: tripType
                })
            })
            .then(response => response.json())
            .then(data => {
                fromStationSelect.innerHTML = "<option value=\\"\\">اختر المحطة</option>";
                
                if (data.success && data.stations) {
                    logTest(`تم تحميل ${data.stations.length} محطة بنجاح`, "success");
                    data.stations.forEach(station => {
                        const option = document.createElement("option");
                        option.value = station;
                        option.textContent = station;
                        fromStationSelect.appendChild(option);
                    });
                } else {
                    logTest("فشل في تحميل المحطات: " + (data.message || "خطأ غير معروف"), "danger");
                }
            })
            .catch(error => {
                logTest("خطأ في تحميل المحطات: " + error.message, "danger");
            });
        }
    });

    // معالج البحث
    testSearchForm.addEventListener("submit", function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const searchData = {
            action: "search_trips",
            trip_type: formData.get("trip_type"),
            from_city: formData.get("from_city"),
            from_station: formData.get("from_station"),
            travel_date: formData.get("travel_date"),
            passenger_count: formData.get("passenger_count")
        };

        logTest("جاري البحث عن الرحلات...", "info");
        
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(searchData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logTest(`تم العثور على ${data.trips.length} رحلة`, "success");
                displayTrips(data.trips, searchData);
            } else {
                logTest("فشل البحث: " + (data.message || "لم يتم العثور على رحلات"), "warning");
                searchResults.innerHTML = "<p class=\\"text-warning\\">لم يتم العثور على رحلات متاحة</p>";
            }
        })
        .catch(error => {
            logTest("خطأ في البحث: " + error.message, "danger");
            searchResults.innerHTML = "<p class=\\"text-danger\\">حدث خطأ أثناء البحث</p>";
        });
    });

    function displayTrips(trips, searchData) {
        let html = "";
        trips.forEach(trip => {
            const totalPrice = (parseFloat(trip.seat_price) * parseInt(searchData.passenger_count)).toFixed(2);
            
            html += `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6>${trip.from_city} → ${trip.to_city}</h6>
                        <p class="mb-1"><strong>المحطة:</strong> ${trip.from_station}</p>
                        <p class="mb-1"><strong>الوقت:</strong> ${trip.departure_time} - ${trip.arrival_time}</p>
                        <p class="mb-1"><strong>السعر:</strong> ${totalPrice} ريال</p>
                        <button class="btn btn-sm btn-success" onclick="testBooking(${trip.id}, '${searchData.travel_date}', ${searchData.passenger_count}, ${totalPrice})">
                            اختبار الحجز
                        </button>
                    </div>
                </div>
            `;
        });
        
        searchResults.innerHTML = html;
    }

    // تحميل المدن الأولية
    loadCities();
});

function testBooking(tripId, travelDate, passengerCount, totalPrice) {
    const bookingData = {
        trip_id: tripId,
        travel_date: travelDate,
        passenger_count: passengerCount,
        total_price: totalPrice
    };
    
    const encodedData = encodeURIComponent(JSON.stringify(bookingData));
    window.open(`bus-booking-form.php?data=${encodedData}`, "_blank");
}
</script>
';

include('footer.php');
?>
