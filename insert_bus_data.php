<?php
include('webset.php');

try {
    // تعيين الترميز
    $db->exec("SET NAMES utf8mb4");

    // حذف البيانات القديمة
    $db->exec("DELETE FROM bus_trips");
    
    // إدراج بيانات جديدة
    $trips = [
        // رحلات الذهاب إلى مكة
        ['الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '13:30:00', '17:30:00', 100.00, 50, 'to_mecca', '0', 1],
        ['الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '20:00:00', '00:00:00', 100.00, 50, 'to_mecca', '0', 1],
        ['الرياض', 'محطة الملك فهد', 'مكة المكرمة', 'شارع ابراهيم الخليل', '15:00:00', '19:00:00', 120.00, 45, 'to_mecca', '0', 1],
        ['جدة', 'محطة جدة المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '14:00:00', '15:30:00', 50.00, 45, 'to_mecca', '0', 1],
        ['جدة', 'محطة الحرمين', 'مكة المكرمة', 'شارع ابراهيم الخليل', '16:00:00', '17:30:00', 50.00, 45, 'to_mecca', '0', 1],
        ['الدمام', 'محطة الدمام المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '10:00:00', '16:00:00', 150.00, 40, 'to_mecca', '["1","3","5"]', 1],
        ['الطائف', 'محطة الطائف', 'مكة المكرمة', 'شارع ابراهيم الخليل', '12:00:00', '13:30:00', 40.00, 35, 'to_mecca', '0', 1],
        ['المدينة المنورة', 'محطة المدينة', 'مكة المكرمة', 'شارع ابراهيم الخليل', '11:00:00', '15:00:00', 80.00, 50, 'to_mecca', '0', 1],
        
        // رحلات العودة من مكة
        ['مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'الرياض', 'محطة البطحاء', '13:30:00', '17:30:00', 100.00, 50, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'الرياض', 'محطة البطحاء', '21:00:00', '01:00:00', 100.00, 50, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الرياض', 'محطة الملك فهد', '14:00:00', '18:00:00', 120.00, 45, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'جدة', 'محطة جدة المركزية', '15:00:00', '16:30:00', 50.00, 45, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'جدة', 'محطة الحرمين', '17:00:00', '18:30:00', 50.00, 45, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الدمام', 'محطة الدمام المركزية', '16:00:00', '22:00:00', 150.00, 40, 'from_mecca', '["1","3","5"]', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الطائف', 'محطة الطائف', '18:00:00', '19:30:00', 40.00, 35, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'المدينة المنورة', 'محطة المدينة', '16:00:00', '20:00:00', 80.00, 50, 'from_mecca', '0', 1]
    ];
    
    $stmt = $db->prepare("INSERT INTO bus_trips (from_city, from_station, to_city, to_station, departure_time, arrival_time, seat_price, total_seats, trip_type, available_days, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($trips as $trip) {
        $stmt->execute($trip);
    }
    
    echo "تم إدراج البيانات بنجاح!<br>";
    echo "عدد الرحلات المدرجة: " . count($trips) . "<br>";
    
    // التحقق من البيانات
    $stmt = $db->query("SELECT COUNT(*) as total FROM bus_trips");
    $total = $stmt->fetchColumn();
    echo "إجمالي الرحلات في قاعدة البيانات: " . $total . "<br>";
    
    // عرض بعض البيانات للتأكد
    $stmt = $db->query("SELECT id, from_city, from_station, to_city, trip_type, status FROM bus_trips LIMIT 5");
    echo "<h3>عينة من البيانات:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>من المدينة</th><th>المحطة</th><th>إلى المدينة</th><th>نوع الرحلة</th><th>الحالة</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['from_city'] . "</td>";
        echo "<td>" . $row['from_station'] . "</td>";
        echo "<td>" . $row['to_city'] . "</td>";
        echo "<td>" . $row['trip_type'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
