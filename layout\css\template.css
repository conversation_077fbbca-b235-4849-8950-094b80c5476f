/*------------------------------------------------------------------
[Table of contents]
1. base
2. elements
3. form
4. layout
5. menu
6. pages 
7. post
8. effect 
9. utilities
10. widgets layout
11. widgets 
12. responsive
-------------------------------------------------------------------*/
/**
* Web Application Prefix Apply For Making Owner Styles
*/
/**
 *   Blocks Layout Selectors
 */
/********* LAYOUT **************/
/* carousel-controls-v1 */
/* carousel-controls-v2 */
/* carousel-controls-v3 */
@keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate_icon {
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

/* 1. base */
body {
  overflow-x: hidden;
  font-size: var(--homez-main-font-size);
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  margin-bottom: 25px;
}

.wp-block-image,
.wp-block-gallery {
  margin-bottom: 28px;
}

figcaption {
  margin-top: 15px;
}

[class^="btn"],
.elementor-button,
.btn {
  white-space: nowrap;
  text-decoration: none !important;
}

[class^="btn"] i,
.elementor-button i,
.btn i {
  vertical-align: middle;
}

input {
  text-align: left;
}

button, .btn,
a {
  outline: none !important;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
}

a:hover, a:focus {
  text-decoration: underline;
}

i {
  display: inline-block;
  line-height: 1;
}

.filter-in-sidebar {
  cursor: pointer;
}

.filter-in-sidebar i {
  vertical-align: middle;
}

.badge {
  border-radius: 6px;
  padding: 11px 12px;
  font-size: 13px;
  font-weight: 500;
}

.bg-success {
  background: #F1FAFF !important;
  color: #00A3FF !important;
}

.bg-pending {
  background: #FFF4DE !important;
  color: #FFA800 !important;
}

.bg-cancelled {
  background: #FFF5F8 !important;
  color: #F1416C !important;
}

.alert p:last-child {
  margin-bottom: 0;
}

[dir="rtl"] .flaticon-left-arrow:before {
  content: "\f104";
}

[dir="rtl"] .flaticon-next:before {
  content: "\f105";
}

.clearfix::after {
  display: table;
}

.mfp-container {
  overflow-x: hidden;
}

fieldset {
  clear: both;
  overflow: hidden;
}

textarea {
  resize: none;
}

.bs-tooltip-top {
  margin-bottom: 8px !important;
}

.hidden {
  display: none !important;
  visibility: hidden !important;
}

.form-control {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.form-control:focus {
  outline: 1px solid var(--homez-link-color);
}

ol ol, ol ul,
ul ol,
ul ul {
  padding-left: 20px;
}

.post-password-form input {
  height: 43px;
  padding: 5px 10px;
  outline: none;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 1px solid #E9E9E9;
}

.post-password-form input:focus {
  border-color: var(--homez-link-color);
}

.post-password-form input[type="submit"] {
  background: var(--homez-theme-color);
  color: #fff;
  border: 0;
  padding: 5px 25px;
}

.post-password-form input[type="submit"]:hover, .post-password-form input[type="submit"]:focus {
  color: #fff;
  background: var(--homez-theme-hover-color);
}

a:focus, .btn:focus {
  outline: none !important;
}

.list,
.list-no {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pswp__item {
  cursor: move;
}

.no-border {
  border: none !important;
}

.no-border:before {
  display: none !important;
}

.wpb_widgetised_column {
  margin: 0;
}

.topmenu-menu-line {
  list-style: none;
  padding: 0;
  margin: 0;
}

.topmenu-menu-line li {
  display: inline-block;
  vertical-align: middle;
}

.topmenu-menu-line li .space {
  margin: 0 3px;
}

.top-menu-mobile .title {
  font-size: 20px;
  padding: 0 15px;
  margin: 0 0 15px;
}

.top-menu-mobile .navbar-nav > li > a {
  padding: 2px 15px;
}

.box-account {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  max-width: 690px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1200px) {
  .box-account {
    padding: 50px;
  }
}

.box-account .nav-tabs {
  border: 0;
  margin: 0 0 0.9375rem;
  flex-wrap: nowrap;
  justify-content: center;
}

@media (min-width: 1200px) {
  .box-account .nav-tabs {
    margin-bottom: 1.875rem;
  }
}

.box-account .nav-tabs > li {
  margin: 0 5px;
}

.box-account .nav-tabs > li > a {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-weight: 500;
  font-size: 1rem;
  display: inline-block;
  padding: 8px 30px;
  border: 1px solid transparent;
  border-radius: 40px;
  color: var(--homez-link-color);
}

.box-account .nav-tabs > li > a i {
  margin-right: 7px;
  vertical-align: text-top;
  line-height: 1;
  font-size: 18px;
  display: inline-block;
}

.box-account .nav-tabs > li > a.active {
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  background: #fff;
  color: var(--homez-theme-color);
  border-color: #E9E9E9;
}

.action .caret {
  width: 8px;
  height: 8px;
  position: relative;
}

.action[aria-expanded="true"] b {
  border: none;
}

.action[aria-expanded="true"] b:before {
  font-family: FontAwesome;
  content: "\f00d";
  position: absolute;
  top: 0;
  left: 0;
  font-size: 12px;
}

ins {
  text-decoration: none;
}

img {
  border: 0;
  max-width: 100%;
  height: auto;
}

.video-responsive {
  height: 0;
  padding-top: 0;
  padding-bottom: 56.25%;
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
}

.video-responsive embed, .video-responsive iframe, .video-responsive object, .video-responsive video {
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
}

.audio-responsive iframe {
  width: 100%;
  height: 126px;
}

ul.list-square {
  padding: 0;
  margin: 0;
  list-style: none;
}

ul.list-square > li {
  line-height: 35px;
  font-size: 14px;
  margin: 0;
}

ul.list-square > li.active > a, ul.list-square > li:hover > a {
  color: var(--homez-theme-color);
}

ul.list-square > li.active > a:before, ul.list-square > li:hover > a:before {
  background: var(--homez-theme-color);
}

ul.list-square > li > a {
  display: block;
  padding-left: 20px;
  position: relative;
}

ul.list-square > li > a:before {
  content: '';
  background: var(--homez-link-color);
  width: 8px;
  height: 8px;
  left: 0;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.breadcrumb > a + li:before,
.breadcrumb > li + a:before,
.breadcrumb > li + li:before {
  display: inline-block;
  content: "/";
  color: var(--homez-text-color);
  font-size: 0.875rem;
  line-height: 1;
  margin: 0 5px;
}

.apus-breadscrumb {
  font-size: 0.875rem;
  background-color: transparent;
}

.apus-breadscrumb .breadcrumb {
  background: transparent;
  margin: 0;
  padding: 0;
}

.apus-breadscrumb .active {
  color: var(--homez-link-color);
}

.apus-breadscrumb a {
  color: var(--homez-text-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-breadscrumb a:hover, .apus-breadscrumb a:focus {
  color: var(--homez-link-color);
}

.apus-breadscrumb .wrapper-breads {
  padding: 20px 0;
}

@media (min-width: 1200px) {
  .apus-breadscrumb .wrapper-breads {
    padding: 55px 0;
  }
}

.apus-breadscrumb .bread-title {
  text-transform: capitalize;
  font-size: 22px;
  margin: 0 0 8px;
}

@media (min-width: 1200px) {
  .apus-breadscrumb .bread-title {
    font-size: 30px;
  }
}

.apus-breadscrumb.has_bg {
  background-color: rgba(24, 26, 32, 0.9);
  margin: 0 0 1.875rem;
}

@media (min-width: 1200px) {
  .apus-breadscrumb.has_bg {
    margin-bottom: 60px;
  }
}

.apus-breadscrumb.has_bg .wrapper-breads {
  padding: 1.875rem 0;
}

@media (min-width: 1200px) {
  .apus-breadscrumb.has_bg .wrapper-breads {
    padding: 180px 0;
  }
}

.apus-breadscrumb.has_bg li::before,
.apus-breadscrumb.has_bg .active,
.apus-breadscrumb.has_bg a,
.apus-breadscrumb.has_bg .bread-title {
  color: inherit;
}

.breadcrumbs-simple {
  padding: 15px 0;
}

@media (min-width: 1200px) {
  .breadcrumbs-simple {
    padding: 25px 0;
  }
}

.breadcrumbs-simple .breadcrumb {
  margin: 0;
  padding: 0;
  background-color: transparent;
}

.breadcrumbs-simple .breadcrumb a {
  color: var(--homez-text-color);
}

.breadcrumbs-simple .breadcrumb a:hover, .breadcrumbs-simple .breadcrumb a:focus {
  color: var(--homez-link-color);
}

.breadcrumbs-simple .breadcrumb .active {
  color: var(--homez-link-color);
}

.search-form input,
.search-form .btn {
  background: #ebedee;
  border-color: #ebedee;
  color: var(--homez-link-color);
}

.search-form .btn {
  padding: 1.875rem 15px;
}

.ui-autocomplete.ui-widget-content {
  padding: 15px;
  margin: 0;
  list-style: none;
  background: #fff;
  width: 300px !important;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  -webkit-box-shadow: 0 10px 30px 0 rgba(13, 38, 59, 0.05);
  box-shadow: 0 10px 30px 0 rgba(13, 38, 59, 0.05);
}

.ui-autocomplete.ui-widget-content li {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #E9E9E9;
  cursor: pointer;
}

.ui-autocomplete.ui-widget-content li:hover .team-agent-list-label, .ui-autocomplete.ui-widget-content li:focus .team-agent-list-label {
  color: var(--homez-theme-color);
}

.ui-autocomplete.ui-widget-content li:last-child {
  border: none;
  margin: 0;
  padding: 0;
}

.add-fix-top {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: fixed;
  z-index: 1;
  bottom: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  -webkit-transform: translateY(20px);
  -ms-transform: translateY(20px);
  -o-transform: translateY(20px);
  transform: translateY(20px);
  border-radius: 50%;
  text-align: center;
  opacity: 0;
  filter: alpha(opacity=0);
  background-color: transparent;
  color: var(--homez-theme-color);
  border: 2px solid var(--homez-theme-color);
}

@media (min-width: 1200px) {
  .add-fix-top {
    right: 1.875rem;
    bottom: 1.875rem;
    width: 46px;
    height: 46px;
  }
}

.add-fix-top.active {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  opacity: 0.7;
  filter: alpha(opacity=70);
}

@media (min-width: 1200px) {
  .add-fix-top.active {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.add-fix-top.active:focus, .add-fix-top.active:hover {
  color: #fff;
  opacity: 1;
  filter: alpha(opacity=100);
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

.menu {
  padding: 0;
  margin: 0;
}

.menu li {
  list-style: none;
  margin-bottom: 8px;
}

.menu li:last-child {
  margin-bottom: 0;
}

.menu ul {
  padding-left: 20px;
  margin: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes -webkit-spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

.apus-page-loading {
  top: 0;
  left: 0;
  position: fixed;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 9999;
}

.apus-loader-inner {
  margin: 0 auto;
  width: 80px;
  height: 80px;
  text-align: center;
  font-size: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  background-size: cover;
  background-repeat: no-repeat;
}

.apus-loader-inner > div {
  width: 8px;
  height: 100%;
  display: inline-block;
  float: left;
  margin-left: 2px;
  -webkit-animation: delay 0.8s infinite ease-in-out;
  animation: delay 0.8s infinite ease-in-out;
}

.apus-loader-inner .loader1 {
  background-color: #e39505;
}

.apus-loader-inner .loader2 {
  background-color: #ff5395;
  -webkit-animation-delay: -0.7s;
  animation-delay: -0.7s;
}

.apus-loader-inner .loader3 {
  background-color: #84b813;
  -webkit-animation-delay: -0.6s;
  animation-delay: -0.6s;
}

.apus-loader-inner .loader4 {
  background-color: #f38ca3;
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
}

.apus-loader-inner .loader5 {
  background-color: #da5800;
  -webkit-animation-delay: -0.4s;
  animation-delay: -0.4s;
}

@-webkit-keyframes delay {
  0%, 40%, 100% {
    -webkit-transform: scaleY(0.05);
    transform: scaleY(0.05);
  }
  20% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
}

@keyframes delay {
  0%, 40%, 100% {
    transform: scaleY(0.05);
    -webkit-transform: scaleY(0.05);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}

.page-links {
  overflow: hidden;
  margin: 0 0 30px;
}

.page-links .page-links-title {
  font-weight: normal;
  color: var(--homez-link-color);
}

.page-links > span:not(.page-links-title),
.page-links > a {
  display: inline-block;
  line-height: 1;
  margin: 0 3px;
  padding: 10px 13px;
  border-radius: 2px;
  border: 1px solid #E9E9E9;
  color: var(--homez-link-color);
}

.page-links > span:not(.page-links-title):hover, .page-links > span:not(.page-links-title):active,
.page-links > a:hover,
.page-links > a:active {
  color: #fff;
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

.page-links > span:not(.page-links-title) {
  color: #fff;
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

option {
  padding: 5px;
}

.woocommerce-MyAccount-navigation > ul {
  list-style: none;
  padding: 0;
  margin: 0;
  line-height: 35px;
}

.image-lazy-loading .image-wrapper {
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  max-height: 100%;
}

.image-lazy-loading .image-wrapper img {
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.image-lazy-loading .image-wrapper.image-loaded {
  background: none;
}

.image-lazy-loading .image-wrapper.image-loaded img {
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-header .wpml-ls-legacy-dropdown a.wpml-ls-item-toggle {
  border: none !important;
  padding: 4px 25px 6px 0;
  background: transparent !important;
  color: #707070 !important;
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  background: #fff;
  border: none;
  border: 1px solid #E9E9E9;
  min-width: 114px;
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li {
  border-bottom: 1px solid #E9E9E9;
  padding: 9px 10px;
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li a {
  border: none !important;
  background: transparent !important;
  padding: 0;
  color: var(--homez-link-color);
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li a:hover, .apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li a:focus {
  color: var(--homez-link_hover_color);
}

.apus-header .wpml-ls-legacy-dropdown .wpml-ls-sub-menu li:last-child {
  border: none;
}

.apus-header .wpml-ls-legacy-dropdown {
  width: auto;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency,
.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency {
  padding: 0;
  border: none;
  background: transparent;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency {
  padding: 5px 10px;
  border-bottom: 1px solid #E9E9E9;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency:last-child {
  border: none;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency {
  font-weight: 400;
  color: #707070;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency a:hover, .apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency.active a {
  font-weight: 400;
  color: var(--homez-theme-color);
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency {
  min-width: 80px;
  text-align: inherit;
  z-index: 99;
}

.apus-header .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency::after {
  font-size: 11px;
}

.apus-header .apus-topbar .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  min-width: 155px;
}

.apus_socials {
  list-style: none;
  padding: 0;
}

.apus_socials li {
  display: inline-block;
  margin-right: 10px;
}

.apus_socials li:last-child {
  margin: 0;
}

.apus_socials a {
  border: 1px solid #dce1e6;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: inline-block;
  text-indent: 2px;
}

.apus_socials a:hover, .apus_socials a:focus {
  color: #fff !important;
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

.apus_socials a.facebook {
  color: #3c66c4;
}

.apus_socials a.twitter {
  color: #00aced;
}

.apus_socials a.pinterest {
  color: #bd081c;
}

.apus_socials a.google-plus {
  color: #dc473a;
}

.apus_socials a.instagram {
  color: #bc44bd;
}

.apus_socials.style2 a {
  border-color: #47586d;
}

.select2-container {
  outline: none !important;
}

.select2-container.select2-container--default .select2-search--dropdown {
  padding: 0 20px 10px;
}

.select2-container.select2-container--default .select2-search--dropdown .select2-search__field {
  outline: none !important;
  border: 1px solid #E9E9E9;
  border-radius: 6px;
  height: 40px;
  font-size: 0.875rem;
  padding: 5px 10px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.select2-container.select2-container--default .select2-search--dropdown .select2-search__field:focus {
  border-color: var(--homez-link-color);
}

.select2-results__option {
  outline: none !important;
}

.select2-container--default .select2-dropdown {
  overflow: hidden;
  border: 0;
  min-width: 160px;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.07);
  border-radius: 8px !important;
}

@media (min-width: 1200px) {
  .select2-container--default .select2-dropdown {
    border-radius: 12px !important;
  }
}

.select2-container--default .select2-dropdown.select2-dropdown--below {
  margin-top: 10px;
}

.select2-container--default .select2-dropdown.select2-dropdown--above {
  margin-bottom: 10px;
}

.select2-container--default .select2-results > .select2-results__options {
  max-height: 215px !important;
  scrollbar-width: thin;
}

.select2-results .select2-results__option {
  color: var(--homez-link-color);
  padding: 5px 20px;
  background-color: transparent;
  position: relative;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.select2-container--default.select2-container .select2-selection--single {
  height: 48px;
  background: #fff;
  outline: none;
  border-radius: 8px !important;
  border: 1px solid #E9E9E9;
  margin: 0;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  padding: 5px 12px;
}

@media (min-width: 1200px) {
  .select2-container--default.select2-container .select2-selection--single {
    height: calc(1.9em + (1.625rem + 2px));
  }
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__rendered {
  padding-top: 4px;
  padding-bottom: 4px;
  color: var(--homez-link-color);
}

@media (min-width: 1200px) {
  .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered {
    padding-top: 7px;
    padding-bottom: 7px;
  }
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__placeholder {
  color: var(--homez-text-color);
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__arrow {
  top: 10px;
  right: 12px;
}

@media (min-width: 1200px) {
  .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow {
    top: 13px;
  }
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__arrow b {
  border-color: var(--homez-link-color) transparent transparent transparent;
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__clear {
  color: #dc3545;
}

.select2-container--default.select2-container .select2-selection--single .select2-selection__clear:hover, .select2-container--default.select2-container .select2-selection--single .select2-selection__clear:focus {
  color: #d32535;
}

.select2-container--default.select2-container .select2-selection--multiple {
  height: calc(1.9em + (1.625rem + 2px));
  outline: none;
  border-radius: 8px;
  border: 1px solid #E9E9E9;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.select2-container--default.select2-container .select2-selection--multiple .select2-selection__choice {
  border: 0;
  color: var(--homez-theme-color);
  background-color: var(--homez-theme-color-007);
  font-weight: 500;
}

.select2-container--default.select2-container.select2-container--open .select2-selection--multiple {
  border-color: var(--homez-link-color);
}

.select2-container--default.select2-container.select2-container--open .select2-selection--single {
  border-color: var(--homez-link-color);
  outline: 1px solid var(--homez-link-color);
}

.select2-container--default.select2-container.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent var(--homez-link-color) transparent;
}

.select2-container.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option--highlighted[data-selected] {
  background: #F0EFEC;
  color: var(--homez-link-color);
}

.select2-container.select2-container--default .select2-results__option[aria-selected="true"],
.select2-container--default .select2-results__option[data-selected="true"] {
  background: #F0EFEC;
  color: var(--homez-link-color);
}

.nav-tabs-custom {
  border: 1px solid #e8ebef;
  margin: 47px 0 0;
}

.nav-tabs-custom > .nav-tabs {
  margin-top: -47px;
  margin-left: -1px;
}

.nav-tabs-custom .tab-content > .tab-pane {
  padding: 0 1em;
}

.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eee;
}

.bs-glyphicons {
  padding: 0;
}

.bs-glyphicons li {
  width: 24.5%;
  height: 115px;
  padding: 10px;
  margin: 0 -1px -1px 0;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  border: 1px solid #e8edef;
  display: inline-block;
}

.bs-glyphicons .glyphicon {
  margin-top: 5px;
  margin-bottom: 10px;
  font-size: 24px;
}

.bs-glyphicons .glyphicon-class {
  display: block;
  text-align: center;
  word-wrap: break-word;
}

.apus-social-share > * {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
}

.apus-social-share strong {
  font-size: 15px;
  font-weight: 600;
  margin-right: 5px;
}

.apus-social-share a {
  display: inline-block;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 12px;
  border-radius: 50%;
}

.apus-social-share a:hover, .apus-social-share a:focus {
  background: rgba(34, 34, 34, 0.05);
  color: var(--homez-link-color);
}

.share-listing {
  display: inline-block;
  cursor: pointer;
}

.share-listing .icon-share {
  border-radius: 50%;
  border: 1px solid #E9E9E9;
  background: #fff;
  color: var(--homez-link-color);
  display: inline-block;
  line-height: 36px;
  font-size: 12px;
  text-align: center;
  width: 36px;
  height: 36px;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  flex-shrink: 0;
}

.share-listing .icon-share:hover, .share-listing .icon-share:focus {
  color: #fff;
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

.share-listing .share-title {
  font-weight: 500;
  margin: 0 0 0 10px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.share-listing .bo-social-icons {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  white-space: nowrap;
  padding: 4px 10px;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  position: absolute;
  z-index: 2;
  bottom: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  background: #fff;
  margin-bottom: 0px;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
}

.share-listing .bo-social-icons:before {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 16px;
  height: 16px;
  border-width: 8px;
  border-style: solid;
  border-color: #E9E9E9 transparent transparent;
}

.share-listing:hover .bo-social-icons {
  margin-bottom: 10px;
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.share-listing:hover .share-title {
  color: var(--homez-theme-color);
}

.share-listing:hover .icon-share {
  color: #fff;
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

.filter-scroll {
  height: 100%;
}

.tooltip {
  z-index: 4;
}

.flaticon-repeat:before {
  content: "\f129";
  font-family: "Flaticon";
}

.affix {
  position: fixed !important;
}

.tooltip.top .tooltip-arrow {
  border-top-color: #24324a;
}

.tooltip.top .tooltip-inner {
  padding: 5px 15px;
  background-color: #24324a;
  color: #fff;
  border-radius: 8px;
}

.apus-results {
  margin-top: 10px;
}

.apus-results .apus-results-reset {
  display: inline-block;
  padding: 6px 15px;
  background: #dc3545;
  color: #fff;
  white-space: nowrap;
  font-weight: 400;
  font-size: 15px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-results .apus-results-reset:hover, .apus-results .apus-results-reset:active {
  color: #fff;
  background: #bd2130;
}

.ajax-pagination {
  text-align: center;
  margin: 10px 0;
}

.ajax-pagination.apus-loader .apus-loadmore-btn {
  display: none;
}

.ajax-pagination.apus-loader:after {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  background-position: center center;
  background-repeat: no-repeat;
  content: "";
  width: 100%;
  height: 50px;
  display: block;
}

.ajax-pagination .apus-loadmore-btn + .apus-allproducts {
  display: none;
}

.ajax-pagination .apus-loadmore-btn.hidden + .apus-allproducts {
  display: block;
  color: #dc3545;
}

.ajax-listings-pagination {
  text-align: center;
}

.ajax-listings-pagination .apus-loadmore-btn {
  display: none;
}

.ajax-listings-pagination:not(.all-listings-loaded) .apus-loadmore-btn {
  display: inline-block;
}

.ajax-listings-pagination:not(.all-listings-loaded) .apus-allproducts {
  display: none;
}

.wp-block-button .wp-block-button__link {
  border-radius: 8px;
}

.wp-block-button.is-style-squared .wp-block-button__link {
  border-radius: 0;
}

.wp-block-button .wp-block-button__link:not(.has-background) {
  background-color: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
  color: #fff;
}

.wp-block-button .wp-block-button__link:not(.has-background):hover, .wp-block-button .wp-block-button__link:not(.has-background):focus {
  color: #fff;
  background-color: var(--homez-theme-hover-color);
  border-color: var(--homez-theme-hover-color);
}

.wp-block-button.is-style-outline .wp-block-button__link {
  background-color: transparent;
  border-color: var(--homez-theme-color);
  color: var(--homez-theme-color);
}

pre {
  display: block;
  padding: 11.5px;
  margin: 0 0 12px;
  line-height: 1.9;
  word-break: break-all;
  word-wrap: break-word;
  color: #333333;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
}

dl {
  margin-bottom: 24px;
}

table {
  width: 100%;
  border: 1px solid #E9E9E9;
  margin-bottom: 24px;
}

table th, table td {
  border: 1px solid #E9E9E9;
  padding: 8px 15px;
}

.row-20 {
  margin-right: -10px !important;
  margin-left: -10px !important;
}

.row-20 > * {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.row-10 {
  margin-right: -5px !important;
  margin-left: -5px !important;
}

.row-10 > * {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.space {
  margin: 0 2px;
}

/* 2. elements */
/* block */
.widget {
  margin-bottom: 1.875rem;
  position: relative;
  padding: 0px;
  background: transparent;
}

.widget .wc-blocks-filter-wrapper > h3,
.widget .wp-block-group__inner-container > h1,
.widget .wp-block-group__inner-container > h2,
.widget .wp-block-group__inner-container > h3,
.widget .wp-block-group__inner-container > h4,
.widget .wp-block-group__inner-container > h5,
.widget .wp-block-group__inner-container > h6 {
  margin: 0 0 15px;
  font-weight: 600;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .widget .wc-blocks-filter-wrapper > h3,
  .widget .wp-block-group__inner-container > h1,
  .widget .wp-block-group__inner-container > h2,
  .widget .wp-block-group__inner-container > h3,
  .widget .wp-block-group__inner-container > h4,
  .widget .wp-block-group__inner-container > h5,
  .widget .wp-block-group__inner-container > h6 {
    margin-bottom: 20px;
  }
}

.widget .wc-blocks-filter-wrapper > h3 + ul,
.widget .wp-block-group__inner-container > h1 + ul,
.widget .wp-block-group__inner-container > h2 + ul,
.widget .wp-block-group__inner-container > h3 + ul,
.widget .wp-block-group__inner-container > h4 + ul,
.widget .wp-block-group__inner-container > h5 + ul,
.widget .wp-block-group__inner-container > h6 + ul {
  margin: 0;
  padding-left: 0;
}

.widget .wc-blocks-filter-wrapper > h3 + ul li,
.widget .wp-block-group__inner-container > h1 + ul li,
.widget .wp-block-group__inner-container > h2 + ul li,
.widget .wp-block-group__inner-container > h3 + ul li,
.widget .wp-block-group__inner-container > h4 + ul li,
.widget .wp-block-group__inner-container > h5 + ul li,
.widget .wp-block-group__inner-container > h6 + ul li {
  list-style: none;
  margin-bottom: 10px;
}

.widget .wc-blocks-filter-wrapper > h3 + ul li:last-child,
.widget .wp-block-group__inner-container > h1 + ul li:last-child,
.widget .wp-block-group__inner-container > h2 + ul li:last-child,
.widget .wp-block-group__inner-container > h3 + ul li:last-child,
.widget .wp-block-group__inner-container > h4 + ul li:last-child,
.widget .wp-block-group__inner-container > h5 + ul li:last-child,
.widget .wp-block-group__inner-container > h6 + ul li:last-child {
  margin-bottom: 0;
}

.widget .wc-blocks-filter-wrapper > h3 + ul li:hover > a,
.widget .wc-blocks-filter-wrapper > h3 + ul li.current-cat-parent > a,
.widget .wc-blocks-filter-wrapper > h3 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h1 + ul li:hover > a,
.widget .wp-block-group__inner-container > h1 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h1 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h2 + ul li:hover > a,
.widget .wp-block-group__inner-container > h2 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h2 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h3 + ul li:hover > a,
.widget .wp-block-group__inner-container > h3 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h3 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h4 + ul li:hover > a,
.widget .wp-block-group__inner-container > h4 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h4 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h5 + ul li:hover > a,
.widget .wp-block-group__inner-container > h5 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h5 + ul li.current-cat > a,
.widget .wp-block-group__inner-container > h6 + ul li:hover > a,
.widget .wp-block-group__inner-container > h6 + ul li.current-cat-parent > a,
.widget .wp-block-group__inner-container > h6 + ul li.current-cat > a {
  font-weight: 600;
  text-decoration: none;
}

.widget .wc-blocks-filter-wrapper > h3,
.widget .wp-block-group__inner-container > h2 {
  font-size: 1rem;
}

.widget .widget-title, .widget .widgettitle, .widget .widget-heading {
  font-size: 1rem;
  margin: 0 0 15px;
  font-weight: 600;
  text-transform: capitalize;
}

@media (min-width: 1200px) {
  .widget .widget-title, .widget .widgettitle, .widget .widget-heading {
    margin-bottom: 20px;
  }
}

.widget .widget-title .urgent,
.widget .widget-title .featured, .widget .widgettitle .urgent,
.widget .widgettitle .featured, .widget .widget-heading .urgent,
.widget .widget-heading .featured {
  font-size: 0.75rem;
}

.sidebar .widget,
.apus-sidebar .widget {
  margin: 0 0 20px;
  padding: 0.9375rem;
  background-color: #fff;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .sidebar .widget,
  .apus-sidebar .widget {
    border-radius: 12px;
    padding: 1.875rem;
    margin-bottom: 1.875rem;
  }
}

.sidebar .widget_calendar,
.apus-sidebar .widget_calendar {
  -webkit-box-shadow: none;
  box-shadow: none;
  padding: 0;
  border: 0;
}

.sidebar .apus-products-list,
.apus-sidebar .apus-products-list {
  border-style: solid;
  border-color: #E9E9E9;
  border-width: 0 1px 1px;
}

.sidebar .apus-products-list .product-block,
.apus-sidebar .apus-products-list .product-block {
  padding: 20px;
  margin: 0;
}

.sidebar .row.instagram-pics,
.apus-sidebar .row.instagram-pics {
  margin-left: -6px;
  margin-right: -6px;
}

.sidebar .row.instagram-pics > [class*="col-md"],
.apus-sidebar .row.instagram-pics > [class*="col-md"] {
  padding-left: 6px;
  padding-right: 6px;
  margin-bottom: 12px;
}

.sidebar .widget_apus_single_image,
.apus-sidebar .widget_apus_single_image {
  margin-bottom: 30px;
}

@media (min-width: 1200px) {
  .sidebar-blog .sidebar-right {
    margin-left: 20px;
  }
  .sidebar-blog .sidebar-left {
    margin-right: 20px;
  }
}

.apus-footer .widget .widget-title, .apus-footer .widget .widgettitle, .apus-footer .widget .widget-heading {
  font-size: 15px;
}

@media (min-width: 1200px) {
  .apus-footer .widget .widget-title, .apus-footer .widget .widgettitle, .apus-footer .widget .widget-heading {
    margin-bottom: 25px;
  }
}

/* 3. form */
.btn-action-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  position: relative;
  width: 40px;
  height: 40px;
  font-size: 15px;
  text-align: center;
  color: var(--homez-link-color);
  background: #fff;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.btn-action-icon:hover, .btn-action-icon:focus {
  color: var(--homez-link-color);
  background: #F7F7F7;
}

.btn-action-icon:hover:before, .btn-action-icon:focus:before {
  color: #fff;
}

.btn-action-icon.rejec:hover, .btn-action-icon.rejec:focus {
  color: #fff;
  background: #ffc107;
}

.btn-action-icon.rejec.rejected {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.btn-action-icon.download:hover, .btn-action-icon.download:focus {
  color: #fff;
  background: #222;
}

.btn-action-icon[class*="remove"]:hover, .btn-action-icon[class*="remove"]:focus {
  color: #fff;
  background: #dc3545;
}

.btn-action-icon:before {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  -ms-align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  color: var(--homez-link-color);
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.btn-action-icon.loading {
  color: transparent !important;
}

.btn-action-icon.loading:before {
  opacity: 0.8;
  filter: alpha(opacity=80);
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.list-action [class*="btn"] i {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  background-color: #f4f4f4;
  color: #717171;
  font-size: 1.0625rem;
  height: 45px;
  line-height: 45px;
  width: 45px;
  text-align: center;
  border-radius: 50%;
  margin-right: .5rem;
}

.list-action [class*="btn"]:hover i {
  color: #fff;
  background-color: var(--homez-theme-color);
}

.list-action [class*="added"] i {
  color: #fff;
  background-color: var(--homez-theme-color);
}

.list-action [class*="added"]:hover i:before {
  content: "\e646";
  font-family: 'themify';
  font-weight: 400;
}

.list-action [class*="btn"].loading i:before {
  display: inline-block;
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

@media (min-width: 1200px) {
  .list-action .review {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
    min-width: 200px;
    text-align: center;
  }
}

.list-action > * {
  display: inline-block;
  margin-right: 0.625rem;
}

@media (min-width: 1200px) {
  .list-action > * {
    margin-right: 1.875rem;
  }
}

.list-action > *:last-child {
  margin-right: 0 !important;
}

.view_all {
  font-weight: 500;
  display: inline-block;
  font-size: 14px;
}

.view_all:focus, .view_all:hover {
  text-decoration: underline;
}

.pre {
  font-weight: normal;
  margin-right: 5px !important;
  display: inline-block;
}

@media (min-width: 1200px) {
  .pre {
    margin-right: 8px !important;
  }
}

.next {
  font-weight: normal;
  margin-left: 5px !important;
  display: inline-block;
}

@media (min-width: 1200px) {
  .next {
    margin-left: 8px !important;
  }
}

.btn-freelancer-alert:before,
.btn-job-alert:before {
  content: "\f142";
  font-family: "Flaticon";
  margin-right: 10px;
  font-size: 25px;
  line-height: 0;
  vertical-align: sub;
  display: inline-block;
}

.btn-outline.btn-primary {
  background: transparent;
  border-color: #0d6efd;
  color: #0d6efd;
}

.btn-outline.btn-primary:hover {
  color: #fff;
  border-color: #0d6efd;
  background: #0d6efd;
}

.btn-outline.btn-success {
  background: transparent;
  border-color: #198754;
  color: #198754;
}

.btn-outline.btn-success:hover {
  color: #FFFFFF;
  border-color: #198754;
  background: #198754;
}

.btn-outline.btn-info {
  background: transparent;
  border-color: #0dcaf0;
  color: #0dcaf0;
}

.btn-outline.btn-info:hover {
  color: #FFFFFF;
  border-color: #0dcaf0;
  background: #0dcaf0;
}

.btn-outline.btn-danger {
  background: transparent;
  border-color: #dc3545;
  color: #dc3545;
}

.btn-outline.btn-danger:hover {
  color: #FFFFFF;
  border-color: #dc3545;
  background: #dc3545;
}

.btn-outline.btn-warning {
  background: transparent;
  border-color: #ffc107;
  color: #ffc107;
}

.btn-outline.btn-warning:hover {
  color: #FFFFFF;
  border-color: #ffc107;
  background: #ffc107;
}

.btn-outline.btn-dark {
  background: transparent;
  border-color: var(--homez-second-color);
  color: var(--homez-second-color);
}

.btn-outline.btn-dark:hover {
  color: #FFFFFF;
  border-color: var(--homez-second-color);
  background: var(--homez-second-color);
}

.btn-outline.btn-green {
  background: transparent;
  border-color: #48C740;
  color: #48C740;
}

.btn-outline.btn-green:hover {
  color: #fff;
  border-color: #48C740;
  background: #48C740;
}

.btn-inverse.btn-primary:hover {
  color: #0d6efd;
  background: #FFFFFF;
}

.btn-inverse.btn-success:hover {
  color: #198754;
  background: #FFFFFF;
}

.btn-inverse.btn-info:hover {
  color: #0dcaf0;
  background: #FFFFFF;
}

.btn-inverse.btn-danger:hover {
  color: #dc3545;
  background: #FFFFFF;
}

.btn-inverse.btn-warning:hover {
  color: #ffc107;
  background: #FFFFFF;
}

.btn-inverse.btn-theme:hover {
  color: var(--homez-theme-color);
  background: #FFFFFF;
}

.view-more-btn i {
  margin-left: 12px;
}

.reamore {
  font-size: 14px;
  font-weight: 500;
  color: var(--homez-theme-color) !important;
  text-transform: uppercase;
  padding: 0 0 4px;
  border-bottom: 2px solid var(--homez-theme-color);
}

.reamore i {
  margin-left: 8px;
}

.btn-browse {
  text-transform: uppercase;
  font-size: 12px;
  padding: 10px 15px;
  border: 1px solid #eaeff5;
  border-radius: 50px;
  line-height: 1.42857143;
}

.btn-browse:hover, .btn-browse:focus {
  background: var(--homez-theme-color);
  color: #fff;
  border-color: var(--homez-theme-color);
}

.apus-loadmore-btn {
  display: inline-block;
  padding: 10px 30px;
  border: 1px solid #24324A;
  text-transform: capitalize;
  font-weight: 600;
  color: #24324A;
  background-color: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 8px;
  position: relative;
}

@media (min-width: 1200px) {
  .apus-loadmore-btn {
    padding: 10px 40px;
  }
}

.apus-loadmore-btn:hover, .apus-loadmore-btn:focus {
  color: #fff;
  border-color: #24324A;
  background-color: #24324A;
}

.apus-loadmore-btn.loading {
  border-color: transparent !important;
  background-color: transparent !important;
  color: transparent !important;
}

.apus-loadmore-btn.loading:after {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  background-position: center center;
  background-repeat: no-repeat;
  content: "";
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

button:focus,
.btn:focus {
  outline: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.radius-0 {
  border-radius: 0 !important;
}

.radius-circle {
  border-radius: 100px !important;
}

.btn-theme.btn-white {
  background: #fff;
  color: var(--homez-theme-color) !important;
  border-color: #fff;
}

.btn-theme.btn-white:active, .btn-theme.btn-white:hover {
  background-color: var(--homez-theme-color);
  color: #fff !important;
  border-color: var(--homez-theme-color);
}

.btn-readmore {
  font-weight: 600;
}

@media (min-width: 1200px) {
  .btn-readmore {
    font-size: 15px;
  }
}

.btn-readmore:hover, .btn-readmore:focus {
  color: var(--homez-theme-color);
  text-decoration: none;
}

.btn-readmore i {
  vertical-align: middle;
}

.btn-lighten {
  border-color: #fff;
  color: #fff;
  background: transparent;
}

.btn-lighten:hover {
  color: #fff;
  background: transparent;
  border-color: #fff;
}

.btn-outline.btn-white {
  background: transparent;
  color: #fff;
  border-color: #fff;
}

.btn-outline.btn-white:active, .btn-outline.btn-white:hover {
  color: #fff;
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

.btn-primary.btn-inverse:active, .btn-primary.btn-inverse:hover {
  background: #fff !important;
  color: #0d6efd !important;
  border-color: #0d6efd !important;
}

.btn-theme {
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
  color: #fff;
}

.btn-theme:active, .btn-theme:hover {
  background: var(--homez-theme-hover-color);
  border-color: var(--homez-theme-hover-color);
  color: #fff;
}

.btn-dark {
  background: var(--homez-second-color);
  border-color: var(--homez-second-color);
  color: #fff;
}

.btn-dark:active, .btn-dark:hover {
  background: var(--homez-second-hover-color);
  border-color: var(--homez-second-hover-color);
  color: #fff;
}

.btn-theme-rgba10 {
  background: var(--homez-theme-color-010);
  border-color: transparent;
  color: var(--homez-theme-color);
}

.btn-theme-rgba10:hover, .btn-theme-rgba10:focus {
  color: #fff;
  background: var(--homez-theme-color);
  border-color: transparent;
}

.btn-theme.btn-outline {
  color: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
  background: transparent;
}

.btn-theme.btn-outline:hover, .btn-theme.btn-outline:active {
  color: #fff;
  background: var(--homez-theme-color);
  border-color: var(--homez-theme-color);
}

/* Search
------------------------------------------------*/
.search-popup .dropdown-menu {
  padding: 10px;
}

.searchform .input-search {
  padding: 15px;
  border-right: 0;
  line-height: 1.5;
}

.searchform .btn-search {
  vertical-align: top;
  color: #adafac;
  padding: 12px 0.75rem;
}

.searchform .input-group-btn {
  line-height: 100%;
}

.search-category .btn {
  margin-left: 10px !important;
  border-radius: 6px !important;
}

.search-category .wpo-search-inner label.form-control {
  border: none;
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}

.search-category select {
  border: none;
  text-transform: capitalize;
  font-weight: 500;
}

/* comment form
------------------------------------------------*/
.chosen-container {
  width: 100% !important;
}

.input-group-form {
  border-radius: 3px;
  background: transparent;
  margin: 0 0 5px 0;
}

.input-group-form .form-control-reversed {
  border: 0px;
  background: #e5e5e5;
  color: #cccccc;
  font-size: 14px;
  height: 34px;
}

.input-group-form .form-control-reversed:hover, .input-group-form .form-control-reversed:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.input-group-form .input-group-addon {
  border: 0;
  background: #e5e5e5;
  border-radius-left: 4px;
}

.btn-compare,
.btn-favorites {
  font-size: 21px;
  line-height: 1;
}

.btn-compare .count,
.btn-favorites .count {
  font-size: 13px;
  display: inline-block;
  font-weight: 600;
  color: #fff;
  background-color: var(--homez-theme-color);
  border-radius: 50%;
  padding: 4px 7px;
  vertical-align: top;
  margin-top: -10px;
  margin-left: -14px;
}

.text-underline {
  text-decoration: underline;
}

.text-underline:hover {
  color: var(--homez-theme-color);
}

.btn-view-all-photos {
  background-color: #fff;
}

.btn-view-all-photos i {
  display: inline-block;
  margin-right: 8px;
}

@media (max-width: 991px) {
  .btn-view-all-photos {
    padding: 5px 10px;
  }
}

.btn-view i,
.view-my-listings i {
  display: inline-block;
  margin-left: 5px;
}

.btn-view {
  font-weight: 700;
  white-space: nowrap;
  padding: 0;
  border: 0;
  background: transparent;
  color: var(--homez-link-color);
}

.btn-view:hover, .btn-view:focus {
  color: var(--homez-theme-color);
  background: transparent;
}

.show-filter-sidebar {
  background: #181A20;
  border-radius: 8px;
  color: #fff;
  font-size: 0.875rem;
  font-weight: 600;
}

@media (min-width: 1200px) {
  .show-filter-sidebar {
    border-radius: 12px;
  }
}

.show-filter-sidebar .icon-filter {
  font-weight: 400;
  padding: 5px 20px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.show-filter-sidebar span {
  padding: 14px 20px;
}

.show-filter-sidebar:hover .icon-filter {
  background: rgba(255, 255, 255, 0.2);
}

.btn-app {
  line-height: 1;
  color: #fff;
  font-size: 13px;
  background-color: var(--homez-theme-color);
  padding: 10px 20px;
  border-radius: 8px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  min-width: 185px;
}

@media (min-width: 1200px) {
  .btn-app {
    padding: 15px 25px;
    border-radius: 12px;
    min-width: 200px;
  }
}

.btn-app:hover, .btn-app:focus {
  color: #fff;
  background-color: var(--homez-link-color);
}

.btn-app .app-icon {
  font-size: 25px;
  line-height: 1;
  padding-right: 15px;
  flex-shrink: 0;
}

@media (min-width: 1200px) {
  .btn-app .app-icon {
    font-size: 26px;
    padding-right: 20px;
  }
}

.btn-app .inner {
  flex-grow: 1;
}

.btn-app .name-app {
  display: block;
  font-weight: 600;
  margin-top: 8px;
}

@media (min-width: 1200px) {
  .btn-app .name-app {
    font-size: 0.875rem;
  }
}

.btn-app.st_dark {
  background: rgba(255, 255, 255, 0.04);
  color: #BEBDBD;
}

.btn-app.st_dark .app-icon,
.btn-app.st_dark .name-app {
  color: #fff;
}

.btn-app.st_dark:hover, .btn-app.st_dark:focus {
  background: rgba(255, 255, 255, 0.06);
}

.btn-app.st_black {
  background: #181A20;
  color: #fff;
}

.btn-app.st_black:hover, .btn-app.st_black:focus {
  background: var(--homez-theme-color);
}

.btn-app.st_blue {
  background: #0D1C39;
  color: #fff;
}

.btn-app.st_blue:hover, .btn-app.st_blue:focus {
  background: var(--homez-theme-color);
}

.btn-light-theme {
  border: 0;
  text-transform: uppercase;
  background-color: var(--homez-theme-color-010);
  color: var(--homez-theme-color);
  padding: 11px 35px;
}

.btn-light-theme:hover, .btn-light-theme:focus {
  color: #fff;
  background-color: var(--homez-theme-color);
}

.filter {
  padding: 8px 20px;
  border: 0;
  background-color: #F4F4F4;
  color: var(--homez-text-color);
}

@media (min-width: 1200px) {
  .filter {
    padding: 8px 1.875rem;
  }
}

.filter i {
  margin-right: 10px;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.filter:hover, .filter:focus {
  color: #fff;
  background-color: var(--homez-theme-color);
}

.save-search-btn,
.reset-search-btn {
  white-space: nowrap;
}

.save-search-btn i,
.reset-search-btn i {
  display: inline-block;
  margin-right: 5px;
}

.mobile-menu-icon {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 16px;
  line-height: 1;
  border-top: 2px solid #222222;
}

.mobile-menu-icon:after, .mobile-menu-icon:before {
  content: '';
  position: absolute;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  background-color: #222222;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 2px;
}

.mobile-menu-icon:after {
  width: 15px;
  height: 2px;
}

.mobile-menu-icon:before {
  width: 100%;
  height: 2px;
  bottom: 50%;
}

.mobile-menu-icon:hover:after {
  width: 100%;
}

/* 4. layout */
/*------------------------------------*\
    apus-topbar
\*------------------------------------*/
.mm-menu .wpml-ls-legacy-dropdown a,
.topbar-right-inner .wpml-ls-legacy-dropdown a {
  border: none !important;
  padding-top: 0;
  padding-bottom: 2px;
  background: transparent !important;
}

.mm-menu .wpml-ls-legacy-dropdown .wpml-ls-sub-menu,
.topbar-right-inner .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  background: #fff;
  border: none;
  padding: 5px 0;
  -webkit-box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.1);
}

.mm-menu .wpml-ls-legacy-dropdown .wpml-ls-sub-menu a,
.topbar-right-inner .wpml-ls-legacy-dropdown .wpml-ls-sub-menu a {
  padding: 5px 10px;
}

.mm-menu .wpml-ls-legacy-dropdown,
.topbar-right-inner .wpml-ls-legacy-dropdown {
  width: auto;
}

.mm-menu .wpml-ls-legacy-dropdown .wpml-ls-sub-menu,
.topbar-right-inner .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  min-width: 140px;
}

/*------------------------------------*\
    Header
\*------------------------------------*/
.apus-header {
  background: transparent;
  position: relative;
  z-index: 3;
  color: var(--homez-text-color);
  font-size: 0.875rem;
  font-weight: 400;
}

body.page-template-page-dashboard #apus-header,
body.fix-header #apus-header {
  position: fixed;
  z-index: 5;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
}

.together-sidebar-account {
  font-size: 20px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.together-sidebar-account:hover {
  color: var(--homez-theme-color);
}

.top-wrapper-menu {
  position: relative;
}

.top-wrapper-menu:before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 10px;
}

.top-wrapper-menu .balance-available {
  font-weight: 400;
  font-size: 13px;
}

.top-wrapper-menu .icon-down {
  font-size: 12px;
}

.top-wrapper-menu .inner-top-menu {
  margin-top: 10px;
  padding: 0.9375rem;
  border-radius: 8px;
  position: absolute;
  top: 100%;
  right: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  background: #fff;
  z-index: 3;
  min-width: 280px;
  max-height: 85vh;
  overflow-y: auto;
  scrollbar-width: thin;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

@media (min-width: 1200px) {
  .top-wrapper-menu .inner-top-menu {
    padding: 20px;
    border-radius: 12px;
  }
}

.top-wrapper-menu:hover .inner-top-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
}

.top-wrapper-menu .avatar-wrapper {
  width: 45px;
  height: 45px;
  overflow: hidden;
  background: #fff;
  border-radius: 50%;
  display: -webkit-flex;
  /* Safari */
  -webkit-align-items: center;
  /* Safari 7.0+ */
  display: flex;
  align-items: center;
}

.top-wrapper-menu .avatar-wrapper img {
  margin: 0;
}

.top-wrapper-menu .btn-login {
  font-weight: 600;
}

.top-wrapper-menu .btn-login .login-icon {
  margin-right: 10px;
  font-size: 18px;
}

.logged-in-wrapper {
  text-align: center;
  font-size: 17px;
  font-weight: 500;
}

.logged-in-wrapper .button {
  color: #dc3545;
}

.header-default {
  background: #fff;
  border-bottom: 1px solid #E9E9E9;
  padding: 25px 0;
}

.header-mobile {
  padding: 10px 0;
  -webkit-transition: all 0.1s ease 0s;
  -o-transition: all 0.1s ease 0s;
  transition: all 0.1s ease 0s;
  background: #fff;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

.header-mobile .btn-menu-account {
  vertical-align: middle;
  font-size: 18px;
  line-height: 1;
}

.sticky-header {
  position: fixed !important;
  z-index: 5;
  top: 0;
  left: 0;
  width: 100%;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  background: #fff;
}

.sticky-header.sticky-header-hidden {
  -webkit-transform: translateY(-110%);
  -ms-transform: translateY(-110%);
  -o-transform: translateY(-110%);
  transform: translateY(-110%);
}

.sticky-header .vertical-wrapper.show-always .content-vertical {
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.sticky-header .vertical-wrapper.show-always:hover .content-vertical {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.header_transparent .apus-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.header_transparent:not(.fix-header) .main-sticky-header:not(.sticky-header) section.elementor-element:not(.no-transparent) {
  background: transparent !important;
}

.header_transparent:not(.fix-header) .no_keep_header section.elementor-element:not(.no-transparent) {
  background: transparent !important;
}

.header_transparent .main-sticky-header:not(.sticky-header) .megamenu > li > a {
  color: #fff;
}

body.header_fixed .apus-header {
  position: fixed;
  width: 100%;
  background: #fff;
  top: 0;
  left: 0;
}

.main-sticky-header {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.header-button-woo > div {
  margin-left: 25px;
}

.header-button-woo > div:last-child {
  margin-left: 0;
}

.header-sidebar {
  position: fixed;
  width: 100px;
  z-index: 91;
  left: 0;
  top: 0;
  min-height: 100vh;
  background: #000;
  color: #fff;
}

.header-sidebar a {
  color: #fff;
}

.header-sidebar a:hover, .header-sidebar a:active {
  color: var(--homez-theme-color);
}

.header-sidebar .show-main-menu {
  width: 100px;
  height: 100px;
  line-height: 100px;
  border-width: 0 0 1px;
  border-color: #E9E9E9;
  color: #fff;
  background: #000000;
}

.header-sidebar .apus-topcart {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px 0;
  border-top: 1px solid #333333;
}

.header-sidebar .apus-topcart .count {
  color: #fff;
  font-size: 12px;
  text-transform: uppercase;
}

.header-sidebar .apus-topcart .dropdown-menu {
  bottom: 0;
  top: inherit;
  left: 100%;
}

.header-sidebar .service {
  color: #999999;
  white-space: nowrap;
  position: absolute;
  top: 50%;
  z-index: 9;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 14px;
  left: 50px;
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
  transform-origin: 0 11px;
}

.header-sidebar .service > * {
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}

.header-sidebar .service p {
  margin: 0;
}

.over-dark {
  cursor: not-allowed;
  display: block;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 6;
  width: 100%;
  height: 100%;
  background: rgba(34, 34, 34, 0.7);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.over-dark.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

/*------------------------------------*\
    Breadcrumb
\*------------------------------------*/
.apus-breadcrumb {
  margin: 0 0 1.875rem;
  text-align: left;
  font-size: 0.875rem;
}

.apus-breadcrumb .breadcrumb-title {
  margin: 0;
  font-weight: 300;
  font-size: 48px;
}

.apus-breadcrumb nav {
  text-align: left;
  line-height: 30px;
}

.apus-breadcrumb nav a {
  color: #000;
}

/*------------------------------------*\
    Content
\*------------------------------------*/
.apus-content {
  background: #fff;
}

/*------------------------------------*\
    Pagination
\*------------------------------------*/
.navigation {
  display: block;
  clear: both;
}

.pagination,
.pagination-links,
.apus-pagination {
  width: 100%;
  padding: 0.625rem 0;
  margin: 0;
  text-align: center;
  line-height: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
  align-items: center;
  -ms-align-items: center;
  -webkit-align-items: center;
}

@media (min-width: 1200px) {
  .pagination,
  .pagination-links,
  .apus-pagination {
    padding: 1.25rem 0;
  }
}

.pagination li,
.pagination-links li,
.apus-pagination li {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
}

.pagination li > span, .pagination li > a,
.pagination-links li > span,
.pagination-links li > a,
.apus-pagination li > span,
.apus-pagination li > a {
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
  margin: 0 5px;
  display: inline-block;
  float: none;
  color: var(--homez-link-color);
  border-radius: 50% !important;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  min-width: 40px;
  line-height: 40px;
  padding: 0 5px;
  border: 0;
  background: #fff;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.pagination li > span:focus, .pagination li > span:hover, .pagination li > span.current, .pagination li > a:focus, .pagination li > a:hover, .pagination li > a.current,
.pagination-links li > span:focus,
.pagination-links li > span:hover,
.pagination-links li > span.current,
.pagination-links li > a:focus,
.pagination-links li > a:hover,
.pagination-links li > a.current,
.apus-pagination li > span:focus,
.apus-pagination li > span:hover,
.apus-pagination li > span.current,
.apus-pagination li > a:focus,
.apus-pagination li > a:hover,
.apus-pagination li > a.current {
  color: #fff;
  background: var(--homez-theme-color);
}

.pagination > span, .pagination > a,
.pagination-links > span,
.pagination-links > a,
.apus-pagination > span,
.apus-pagination > a {
  text-decoration: none;
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
  margin: 0 5px;
  display: inline-block;
  float: none;
  color: var(--homez-link-color);
  border-radius: 50% !important;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  min-width: 40px;
  line-height: 40px;
  padding: 0 5px;
  border: 0;
  background: #fff;
  position: relative;
  overflow: hidden;
}

.pagination > span:focus, .pagination > span:hover, .pagination > span.current, .pagination > a:focus, .pagination > a:hover, .pagination > a.current,
.pagination-links > span:focus,
.pagination-links > span:hover,
.pagination-links > span.current,
.pagination-links > a:focus,
.pagination-links > a:hover,
.pagination-links > a.current,
.apus-pagination > span:focus,
.apus-pagination > span:hover,
.apus-pagination > span.current,
.apus-pagination > a:focus,
.apus-pagination > a:hover,
.apus-pagination > a.current {
  color: #fff;
  background: var(--homez-theme-color);
}

.pagination .prev,
.pagination .next,
.pagination-links .prev,
.pagination-links .next,
.apus-pagination .prev,
.apus-pagination .next {
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

.pagination ul.page-numbers,
.pagination-links ul.page-numbers,
.apus-pagination ul.page-numbers {
  margin: 0;
  padding: 0;
  list-style: none;
}

.pagination i,
.pagination-links i,
.apus-pagination i {
  font-weight: 400;
}

/*------------------------------------*\
    Footer
\*------------------------------------*/
.apus-footer-mobile {
  display: none;
}

.apus-footer {
  background: transparent;
  position: relative;
  color: var(--homez-text-color);
  font-size: 0.875rem;
  font-weight: 400;
}

.apus-footer a:not([class]) {
  color: var(--homez-link-color);
}

.apus-footer a:not([class]):hover, .apus-footer a:not([class]):focus, .apus-footer a:not([class]):active {
  color: var(--homez-link_hover_color);
}

/*------------------------------------*\
    Copyright
\*------------------------------------*/
.apus-copyright {
  color: var(--homez-text-color);
  font-size: 0.875rem;
  font-weight: 400;
  background: #ffffff;
  padding-top: 20px;
  padding-bottom: 20px;
  position: relative;
}

.apus-copyright a {
  color: var(--homez-link-color);
}

.apus-copyright a:hover, .apus-copyright a:focus, .apus-copyright a:active {
  color: var(--homez-theme-color);
}

/*------------------------------------*\
    Top bar
\*------------------------------------*/
.apus-offcanvas-header {
  padding: 10px 20px 10px 1.875rem;
  border-bottom: 1px solid #E9E9E9;
  margin-bottom: 1.875rem;
}

.apus-offcanvas-header .close-offcanvas {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  color: var(--homez-link-color);
  background: var(--homez-theme-color-010);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 12px;
  cursor: pointer;
}

.apus-offcanvas-header .close-offcanvas:hover, .apus-offcanvas-header .close-offcanvas:focus {
  color: #fff;
  background: var(--homez-theme-color);
}

.apus-offcanvas-header .title {
  color: var(--homez-link-color);
  font-weight: 500;
  font-size: 17px;
}

.apus-offcanvas {
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 400px;
  max-width: 100%;
  background: #fff;
  height: 100vh;
  overflow-x: auto;
  display: -webkit-flex;
  /* Safari */
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
}

.apus-offcanvas .apus-offcanvas-body {
  position: relative;
  height: calc(100% - 61px);
}

.apus-offcanvas .offcanvas-bottom,
.apus-offcanvas .offcanvas-top {
  height: 20%;
}

.apus-offcanvas .offcanvas-middle {
  height: 60%;
  padding: 20px 0;
  overflow-x: hidden;
}

.apus-offcanvas.active {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
  -webkit-box-shadow: 2px 0 5px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 2px 0 5px 0 rgba(0, 0, 0, 0.15);
}

.apus-offcanvas .elementor-column {
  width: 100% !important;
}

.apus-offcanvas .elementor-column .elementor-column-wrap, .apus-offcanvas .elementor-column .elementor-widget-wrap {
  padding: 0 !important;
}

.apus-offcanvas .elementor-column .widget {
  margin-bottom: 10px;
}

.apus-offcanvas .menu-account-mobile {
  padding: 10px 0 0;
  margin: 10px 0 0;
  border-top: 1px solid #E9E9E9;
  list-style: none;
}

.apus-offcanvas .menu-account-mobile > li > a {
  display: inline-block;
  width: 100%;
  padding: 15px 1.875rem;
  line-height: 1.4em;
  font-weight: 500;
}

@media (max-width: 991px) {
  .topbar-mobile {
    padding: 10px;
  }
  .topbar-mobile .btn {
    margin-right: 10px;
    padding: 6px 10px;
  }
  .topbar-mobile .top-cart .dropdown-menu {
    left: 0;
    right: inherit;
  }
  .topbar-mobile .top-cart .dropdown-menu:after, .topbar-mobile .top-cart .dropdown-menu:before {
    display: none;
  }
}

.open-text {
  color: #198754;
}

.close-text {
  color: #dc3545;
}

#mobile-offcanvas-sidebar {
  position: fixed;
  z-index: 999;
  top: 0px;
  width: 270px;
  height: 100%;
  max-width: 80%;
  background: #fff;
}

#mobile-offcanvas-sidebar.mobile-offcanvas-left {
  left: 0;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
}

#mobile-offcanvas-sidebar.mobile-offcanvas-left > .mobile-sidebar-btn {
  left: 100%;
}

#mobile-offcanvas-sidebar.mobile-offcanvas-right {
  right: 0;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
}

#mobile-offcanvas-sidebar.mobile-offcanvas-right > .mobile-sidebar-btn {
  right: 100%;
}

#mobile-offcanvas-sidebar .mobile-sidebar-wrapper {
  display: none;
  height: 100%;
  width: 100%;
  padding: 0 15px;
}

#mobile-offcanvas-sidebar.active > .mobile-sidebar-wrapper {
  display: block;
}

#mobile-offcanvas-sidebar > .mobile-sidebar-btn {
  position: absolute;
  top: 100px;
}

.mobile-sidebar-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -5;
  width: 100%;
  height: 100%;
  background: rgba(34, 34, 34, 0.6);
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
}

.mobile-sidebar-panel-overlay.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  z-index: 5;
  cursor: not-allowed;
}

.apus-footer-mobile {
  position: fixed;
  z-index: 999;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  bottom: 0;
  left: 0;
  width: 100%;
  -webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
}

.apus-footer-mobile .footer-search-mobile {
  position: absolute;
  z-index: 999;
  left: 0;
  top: -60px;
  width: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-footer-mobile .footer-search-mobile.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-footer-mobile > ul {
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.apus-footer-mobile > ul > li {
  text-transform: uppercase;
  display: inline-block;
  padding: 0 25px;
  text-align: center;
  position: relative;
}

.apus-footer-mobile > ul > li span {
  display: block;
  font-size: 10px;
  line-height: 1;
}

.apus-footer-mobile > ul > li .wishlist-icon, .apus-footer-mobile > ul > li .mini-cart {
  line-height: 1.9;
}

.apus-footer-mobile > ul > li .wrapper-morelink {
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  position: absolute;
  right: 0;
  bottom: 40px;
  padding: 20px;
  background: #fff;
  -webkit-box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
}

.apus-footer-mobile > ul > li .wrapper-morelink .footer-morelink {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 10px;
}

.apus-footer-mobile > ul > li .wrapper-morelink li {
  padding: 3px 0;
  white-space: nowrap;
  display: block;
  width: 100%;
  text-align: left;
}

.apus-footer-mobile > ul > li .wrapper-morelink.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-footer-mobile .mini-cart i {
  font-size: 15px;
}

.apus-footer-mobile .mini-cart .count {
  top: 2px;
}

.apus-footer-mobile .apus-search-form {
  min-width: 300px;
  padding: 10px 30px;
  background: rgba(255, 255, 255, 0.9);
}

.apus-footer-mobile .apus-search-form .select-category {
  display: none;
}

.apus-footer-mobile .apus-search-form form {
  border: none;
  margin: 0;
}

.apus-footer-mobile .apus-search-form form .form-control {
  border: 1px solid #E9E9E9;
}

.mobile-sidebar-btn {
  cursor: pointer;
  font-weight: 500;
  display: inline-block;
  padding: 8px;
  color: #fff !important;
  background: var(--homez-theme-color);
  position: fixed;
  top: 30%;
  z-index: 6;
  font-size: 18px;
  line-height: 1;
  opacity: 0.7;
  filter: alpha(opacity=70);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.mobile-sidebar-btn:hover, .mobile-sidebar-btn:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.mobile-sidebar-btn.btn-left {
  right: 0;
  border-radius: 4px 0 0 4px;
}

.mobile-sidebar-btn.btn-right {
  border-radius: 0 4px 4px 0;
  left: 0;
}

.close-sidebar-btn {
  cursor: pointer;
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-bottom: 1px solid #E9E9E9;
  width: 100%;
  text-align: center;
  color: #dc3545;
  font-weight: 500;
}

.close-sidebar-btn i {
  margin-right: 3px;
  font-size: 12px;
}

.close-sidebar-btn:active, .close-sidebar-btn:hover {
  color: #dc3545;
}

@media (max-width: 991px) {
  .sidebar:not(.sidebar-property) {
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    z-index: 8;
    top: 0px;
    width: 330px;
    height: 100vh;
    max-width: 90%;
    position: fixed;
    padding: 15px;
    background: #fff;
    overflow-y: auto;
  }
  .sidebar:not(.sidebar-property).sidebar-left {
    left: 0;
    -webkit-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    -o-transform: translateX(-100%);
    transform: translateX(-100%);
  }
  .sidebar:not(.sidebar-property).sidebar-left.active {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }
  .sidebar:not(.sidebar-property).sidebar-right {
    right: 0;
    -webkit-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%);
  }
  .sidebar:not(.sidebar-property).sidebar-right.active {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }
}

.apus-header .wrapper-topmenu:before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 10px;
  background: transparent;
  z-index: 9;
}

.apus-header .wrapper-topmenu .dropdown-menu-right {
  top: calc(100% + 10px);
}

.apus-topbar .wrapper-topmenu:hover > a {
  color: #fff;
}

.wrapper-top-cart .overlay-dropdown-menu-right {
  position: fixed;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  top: 0;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  z-index: 98;
}

.wrapper-top-cart .overlay-dropdown-menu-right.active {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}

.wrapper-top-cart > .dropdown-menu-right {
  max-width: 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
  position: fixed;
  z-index: 999;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  top: 0;
  right: 0;
  background: #fff;
  width: 420px;
  height: 100%;
  padding: 1.875rem;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
}

.wrapper-top-cart > .dropdown-menu-right .widget_shopping_cart_heading {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex: 0 0 auto;
  -webkit-flex: 0 0 auto;
}

.wrapper-top-cart > .dropdown-menu-right .widget_shopping_cart_heading > h3 {
  margin: 0 0 20px;
  font-size: 22px;
  padding: 0 0 20px;
  border-bottom: 1px solid #E9E9E9;
  width: 100%;
  cursor: pointer;
  color: #dc3545;
}

.wrapper-top-cart > .dropdown-menu-right .widget_shopping_cart_content_wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex: 1 1 auto;
  -webkit-flex: 1 1 auto;
  overflow-x: hidden;
  overflow-y: auto;
}

.wrapper-top-cart > .dropdown-menu-right .shopping_cart_content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
  height: 100%;
}

.wrapper-top-cart > .dropdown-menu-right .shopping_cart_content .cart_list {
  flex: 1 1 auto;
  -webkit-flex: 1 1 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-flex-direction: column;
}

.wrapper-top-cart > .dropdown-menu-right .shopping_cart_content .cart-bottom {
  flex-direction: column;
  -webkit-flex-direction: column;
  flex: 0 0 auto;
  -webkit-flex: 0 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}

.wrapper-top-cart > .dropdown-menu-right.active {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.wrapper-top-cart .cart_list .variation {
  margin: 0;
}

.wrapper-top-cart .cart_list .variation > * {
  display: inline-block;
  vertical-align: middle;
}

.wrapper-top-cart .cart_list .variation > * p {
  margin: 0;
}

.wrapper-top-cart .buttons .btn-block {
  margin-bottom: 10px;
}

/* 5. menu */
.megamenu {
  padding: 0;
  float: none;
}

.megamenu .menu-item-description {
  font-size: 14px;
  text-transform: capitalize;
}

.megamenu > li {
  display: inline-block;
  padding: 0;
  margin: 0;
  vertical-align: top;
  float: none;
  position: relative;
  margin-right: 2px;
}

.megamenu > li:after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 10px;
  top: 100%;
  left: 0;
}

.megamenu > li:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  display: block;
  position: absolute;
  width: 0;
  height: 2px;
  top: 100%;
  left: 0;
  background: transparent;
}

.megamenu > li > a {
  display: inline-block;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 600;
  background: transparent;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: relative;
  text-transform: capitalize;
  padding: 9px 25px;
  border-radius: 45px;
}

.megamenu > li > a .fa, .megamenu > li > a img {
  max-width: 50px;
  margin-left: 3px;
}

.megamenu > li > a:hover, .megamenu > li > a:focus {
  background: rgba(255, 255, 255, 0.07);
  color: var(--homez-link_hover_color);
}

.megamenu > li:hover:before, .megamenu > li.active:before {
  width: 100%;
}

.megamenu > li:hover > a, .megamenu > li.active > a {
  color: var(--homez-link_hover_color);
  background: rgba(255, 255, 255, 0.07);
}

.megamenu > li.aligned-left > .dropdown-menu {
  left: 0;
}

.megamenu > li.aligned-right > .dropdown-menu {
  left: auto;
  right: 0;
}

.megamenu > li > .dropdown-menu {
  min-width: 240px;
  margin-top: 10px;
}

.megamenu .dropdown-menu {
  border-radius: 8px;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  padding: 0px;
  border: 0;
}

@media (min-width: 1200px) {
  .megamenu .dropdown-menu {
    border-radius: 12px;
  }
}

.megamenu .dropdown-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--homez-theme-color);
}

.megamenu .dropdown-menu .text-label.label-hot {
  color: #dc3545;
}

.megamenu .dropdown-menu .text-label.label-new {
  color: #198754;
}

.megamenu .dropdown-menu a:hover, .megamenu .dropdown-menu a:focus,
.megamenu .dropdown-menu .current-menu-item > a {
  text-decoration: underline;
}

.megamenu .dropdown-menu > li > a {
  background: transparent;
  position: relative;
  text-transform: capitalize;
  padding: 7px 1.875rem;
  width: 100%;
  display: inline-flex;
  align-items: center;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
  color: var(--homez-link-color);
  white-space: nowrap;
  text-decoration: none !important;
}

.megamenu .dropdown-menu > li > a:hover, .megamenu .dropdown-menu > li > a:active {
  color: var(--homez-link-color);
  background: var(--homez-theme-color-007);
}

.megamenu .dropdown-menu > li > a b {
  display: none;
}

.megamenu .dropdown-menu > li > a:after {
  margin-left: auto;
}

.megamenu .dropdown-menu > li:hover > a, .megamenu .dropdown-menu > li.current-menu-item > a, .megamenu .dropdown-menu > li.open > a, .megamenu .dropdown-menu > li.active > a {
  color: var(--homez-link-color);
  background: var(--homez-theme-color-007);
}

.megamenu .dropdown-menu .dropdown-menu {
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  transform-origin: 0 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: rotateX(-90deg);
  -ms-transform: rotateX(-90deg);
  -o-transform: rotateX(-90deg);
  transform: rotateX(-90deg);
  -webkit-box-shadow: 0 0 20px 0 rgba(62, 28, 131, 0.1);
  box-shadow: 0 0 20px 0 rgba(62, 28, 131, 0.1);
  position: absolute;
  display: block;
  left: 100%;
  top: -1px;
  background: #fff;
  min-width: 225px;
  margin: 0;
}

.megamenu .dropdown-menu li:hover > .dropdown-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  -o-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

.megamenu .widget {
  margin-bottom: 0;
}

.megamenu .widget .widgettitle,
.megamenu .widget .widget-title {
  margin-bottom: 15px;
}

.megamenu .widget-nav-menu .menu li {
  margin-bottom: 8px;
}

.megamenu > li > a > .text-label {
  font-size: 11px;
  padding: 0px 5px;
  background: #0dcaf0;
  color: #fff;
  position: absolute;
  right: -15px;
  top: -10px;
  line-height: 2;
  display: inline-block;
  text-transform: capitalize;
  border-radius: 2px;
}

.megamenu > li > a > .text-label.label-hot {
  background: #dc3545;
}

.megamenu > li > a > .text-label.label-hot:before {
  border-color: #dc3545 transparent transparent #dc3545;
}

.megamenu > li > a > .text-label.label-new {
  background: #198754;
}

.megamenu > li > a > .text-label.label-new:before {
  border-color: #198754 transparent transparent #198754;
}

.megamenu > li > a > .text-label:before {
  content: '';
  position: absolute;
  z-index: 9;
  top: 100%;
  letter-spacing: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: #0dcaf0 transparent transparent #0dcaf0;
}

.megamenu.effect1 > li > .dropdown-menu {
  display: block;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  transform-origin: 0 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: rotateX(-90deg);
  -ms-transform: rotateX(-90deg);
  -o-transform: rotateX(-90deg);
  transform: rotateX(-90deg);
  position: absolute;
  top: 100%;
}

.megamenu.effect1 > li:hover > .dropdown-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  -o-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

.megamenu.effect2 > li > .dropdown-menu {
  display: block;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  transform-origin: 0 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: rotateX(-90deg);
  -ms-transform: rotateX(-90deg);
  -o-transform: rotateX(-90deg);
  transform: rotateX(-90deg);
  position: absolute;
  top: 100%;
}

.megamenu.effect2 > li > .dropdown-menu > li {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(5px);
  -ms-transform: translateY(5px);
  -o-transform: translateY(5px);
  transform: translateY(5px);
}

.megamenu.effect2 > li:hover > .dropdown-menu {
  margin-top: 0;
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  -o-transform: rotateX(0deg);
  transform: rotateX(0deg);
}

.megamenu.effect2 > li:hover > .dropdown-menu > li {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
}

.megamenu.effect3 > li > .dropdown-menu {
  display: block;
  background: #fff;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-box-shadow: none;
  box-shadow: none;
  position: absolute;
  top: 100%;
  -webkit-animation: fadeleft 0.3s ease-in-out 0s;
  animation: fadeleft 0.3s ease-in-out 0s;
}

.megamenu.effect3 > li:hover > .dropdown-menu {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
  -webkit-animation: faderight 0.3s ease-in-out 0s;
  animation: faderight 0.3s ease-in-out 0s;
}

.navbar-offcanvas {
  padding: 0;
  font-size: 0.875rem;
  display: block;
}

.navbar-offcanvas .sliding-menu__panel {
  padding: 0;
  margin: 0;
}

.navbar-offcanvas .sliding-menu__panel.sliding-menu__panel-root {
  margin-top: -20px;
}

.navbar-offcanvas .sliding-menu li a, .navbar-offcanvas .sliding-menu li .sliding-menu__nav {
  font-size: 0.875rem;
  font-weight: 500;
  background-color: #fff;
  color: var(--homez-link-color);
  position: relative;
  padding: 15px 30px;
}

.navbar-offcanvas .sliding-menu li a:after, .navbar-offcanvas .sliding-menu li .sliding-menu__nav:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: var(--homez-theme-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
}

.navbar-offcanvas .sliding-menu .sliding-menu__nav::before {
  font-family: 'themify';
  content: "\e649";
  font-size: 0.8125rem;
}

.navbar-offcanvas .sliding-menu .active > .sliding-menu__nav,
.navbar-offcanvas .sliding-menu .active > a {
  color: var(--homez-link-color);
  background: #F0EFEC;
}

.navbar-offcanvas .sliding-menu .active > .sliding-menu__nav:after,
.navbar-offcanvas .sliding-menu .active > a:after {
  opacity: 1;
  filter: alpha(opacity=100);
}

.navbar-offcanvas .sliding-menu .sliding-menu__back {
  background-color: #222222 !important;
  color: #fff !important;
  border-radius: 8px;
  margin: 0 30px 10px;
  padding: 15px 20px !important;
  width: calc(100% - 60px);
}

.navbar-offcanvas .sliding-menu .sliding-menu__back:before {
  margin-left: 0;
  content: "\e64a";
}

.navbar-offcanvas .dropdown-menu {
  margin: 0;
}

.navbar-offcanvas .dropdown-menu > li a {
  background: transparent !important;
}

.navbar-offcanvas .dropdown-menu > li.active > a,
.navbar-offcanvas .dropdown-menu > li > a:hover,
.navbar-offcanvas .dropdown-menu > li > a:focus {
  color: var(--homez-link-color);
  text-decoration: underline;
}

.navbar-offcanvas .dropdown-menu [class*="col-sm"] {
  width: 100%;
}

.navbar-offcanvas .dropdown-menu .dropdown-menu-inner {
  padding: 0 1.875rem;
}

.navbar-offcanvas .dropdown-menu .widgettitle {
  font-weight: 500;
  margin: 0 0 10px;
}

.navbar-offcanvas .dropdown-menu .dropdown-menu {
  left: 100%;
  top: 0;
}

.navbar-offcanvas li:hover .dropdown-menu {
  display: block;
}

.navbar-offcanvas .aligned-fullwidth > .dropdown-menu {
  width: 100%;
}

.mobile-vertical-menu .navbar-nav li {
  border-bottom: 1px dashed #E9E9E9;
}

.mobile-vertical-menu .navbar-nav li:last-child {
  border-bottom: 0;
}

.mobile-vertical-menu .navbar-nav li > a {
  padding: 5px 0;
}

.mobile-vertical-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--homez-theme-color);
  font-family: var(--homez-main-font);
}

.mobile-vertical-menu .text-label.label-hot {
  color: #dc3545;
}

.mobile-vertical-menu .text-label.label-new {
  color: #198754;
}

#apus-mobile-menu .btn-toggle-canvas {
  color: #dc3545;
  font-size: 1rem;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
}

#apus-mobile-menu .btn-toggle-canvas:hover, #apus-mobile-menu .btn-toggle-canvas:active {
  color: #dc3545;
}

#apus-mobile-menu .offcanvas-head strong {
  margin: 0 5px;
}

#apus-mobile-menu .widget-nav-menu .menu li {
  margin-bottom: 5px;
}

.main-mobile-menu {
  float: none;
}

.main-mobile-menu > li {
  float: none;
}

.main-mobile-menu .has-submenu > .sub-menu {
  padding-left: 1.875rem;
  list-style: none;
  display: none;
}

.main-mobile-menu .has-submenu > .sub-menu li > .icon-toggle {
  top: 1px;
  padding: 0 5px;
}

.main-mobile-menu .has-submenu > .sub-menu li a {
  font-size: 15px;
  padding: 2px 0;
}

.main-mobile-menu .widget .widget-title, .main-mobile-menu .widget .widgettitle, .main-mobile-menu .widget .widget-heading {
  margin: 0 0 10px;
  font-size: 16px;
  padding: 0 0 8px;
  text-align: inherit;
}

.main-mobile-menu .sub-menu {
  max-width: 100%;
}

.main-mobile-menu .shop-list-small {
  margin-bottom: 10px;
}

.main-mobile-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--homez-theme-color);
  font-family: var(--homez-main-font);
}

.main-mobile-menu .text-label.label-hot {
  color: #dc3545;
}

.main-mobile-menu .text-label.label-new {
  color: #198754;
}

.menu-setting-menu-container .apus-menu-top {
  margin: 0;
  padding: 0;
  list-style: none;
  line-height: 2;
}

.menu-setting-menu-container .apus-menu-top li a {
  padding: 0 15px;
  width: 100%;
}

.menu-setting-menu-container .apus-menu-top ul {
  padding-left: 15px;
}

.wrapper-topmenu .dropdown-menu {
  border-radius: 0;
}

.topmenu-menu {
  width: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
}

.topmenu-menu i {
  font-size: 18px;
  line-height: 1;
  margin-right: 8px;
  display: inline-block;
  vertical-align: middle;
}

@media (min-width: 1200px) {
  .topmenu-menu i {
    margin-right: 15px;
    font-size: 22px;
  }
}

.topmenu-menu > li {
  float: none;
  white-space: nowrap;
  margin-bottom: 2px;
}

.topmenu-menu > li > a {
  text-decoration: none;
  font-weight: 600;
  border-radius: 8px;
  background: transparent;
  padding: 10px 0.9375rem;
  display: inline-block;
  width: 100%;
}

@media (min-width: 1200px) {
  .topmenu-menu > li > a {
    padding: 14px 1.875rem;
    border-radius: 12px;
  }
}

.topmenu-menu > li:hover > a, .topmenu-menu > li.active > a {
  background: #181A20;
  color: #fff;
}

.mm-menu {
  background: #fff;
  color: var(--homez-text-color);
  font-size: 15px;
  border: none;
  border-radius: 0;
}

.mm-menu .mm-listview .mm-next::before {
  border: none;
}

.mm-menu .elementor-column-wrap {
  padding: 0 !important;
}

.mm-menu .mm-panel {
  width: 100% !important;
}

.mm-menu .mm-panel .dropdown-menu-inner {
  padding-top: 20px;
  margin: -20px -20px 20px;
}

.mm-menu .menu li {
  line-height: 41px;
  margin: 0 !important;
}

.mm-menu .menu li:last-child {
  border-bottom: 0;
}

.mm-menu .menu li a {
  padding: 0 !important;
}

.mm-menu .menu li a:before {
  display: none !important;
}

.mm-menu .menu li.active > a {
  text-decoration: underline;
}

.mm-menu .elementor-element .elementor-widget-container {
  margin: 0 !important;
}

.mm-menu .elementor-widget-wrap {
  padding-left: 5px !important;
  padding-right: 0 !important;
  padding-top: 0 !important;
}

.mm-menu .mm-listview > li > a {
  color: var(--homez-link-color);
  background: transparent !important;
  line-height: 1.5;
}

.mm-menu .mm-listview > li > a:hover, .mm-menu .mm-listview > li > a:focus {
  color: var(--homez-link_hover_color);
  text-decoration: underline;
}

.mm-menu .mm-listview > li > a .text-label {
  font-size: 11px;
  padding: 0px 5px;
  background: #0dcaf0;
  position: absolute;
  right: 50px;
  top: 0;
  line-height: 2;
  display: inline-block;
  text-transform: capitalize;
  border-radius: 2px;
}

.mm-menu .mm-listview > li > a .text-label.label-hot {
  background: #dc3545;
}

.mm-menu .mm-listview > li > a .text-label.label-hot:before {
  border-color: #dc3545 transparent transparent #dc3545;
}

.mm-menu .mm-listview > li > a .text-label.label-new {
  background: #198754;
}

.mm-menu .mm-listview > li > a .text-label.label-new:before {
  border-color: #198754 transparent transparent #198754;
}

.mm-menu .mm-listview > li > a .text-label:before {
  content: '';
  position: absolute;
  z-index: 9;
  top: 100%;
  left: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: #0dcaf0 transparent transparent #0dcaf0;
}

.mm-menu .mm-listview .menu-item-description {
  font-size: 12px;
}

.mm-menu .mm-listview > li:after {
  display: none;
}

.mm-menu .mm-listview > li .mm-next:after {
  border-color: var(--homez-link-color);
}

.mm-menu .mm-listview > li.active > a {
  text-decoration: underline;
  color: var(--homez-link_hover_color);
}

.mm-menu #mm-0 .mm-listview > li > a:not(.mm-next) {
  position: relative;
  text-decoration: none;
  font-weight: 600;
  line-height: 2.15;
}

.mm-menu #mm-0 .mm-listview > li > a:not(.mm-next):before {
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: var(--homez-theme-color);
  content: '';
  -webkit-transition: all 0.2s eaes-in-out 0s;
  -o-transition: all 0.2s eaes-in-out 0s;
  transition: all 0.2s eaes-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
}

.mm-menu #mm-0 .mm-listview > li:hover, .mm-menu #mm-0 .mm-listview > li.active {
  background: var(--homez-theme-color-007);
}

.mm-menu #mm-0 .mm-listview > li:hover > a:before, .mm-menu #mm-0 .mm-listview > li.active > a:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.mm-menu .mm-btn:before {
  border-color: var(--homez-link-color);
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
}

.mm-menu .mm-btn:hover:before, .mm-menu .mm-btn:focus:before {
  border-color: var(--homez-theme-color);
}

.mm-menu .mm-title {
  background: var(--homez-theme-color-007);
  padding: 15px 0;
  font-weight: 600;
  font-size: 15px;
  height: auto;
  color: var(--homez-link-color) !important;
}

.mm-menu .mm-navbar {
  padding: 0;
  border: 0;
  height: 50px;
}

.mm-menu .mm-navbar .mm-btn {
  top: 7px;
}

.mm-menu li.text-title {
  font-weight: 700;
  font-size: 15px;
  padding: 15px;
  color: var(--homez-link-color);
  text-align: center;
  border: 0 !important;
}

.mm-menu li.text-title ~ li {
  font-size: 13px;
  padding-left: 20px;
}

.mm-menu li.text-title ~ li a {
  padding: 7px;
  text-transform: capitalize !important;
}

.mm-menu li.text-title ~ li i {
  margin-right: 7px;
}

.mm-panels > .mm-panel > .mm-listview {
  padding-top: 15px;
  padding-bottom: 40px;
}

.mm-panels > .mm-panel > .mm-listview > li:last-child {
  border-bottom: 0;
}

.mm-panels > .mm-panel > .mm-listview > li.space-20 {
  border: none;
}

.mobile-submit {
  display: block;
  position: absolute;
  z-index: 1;
  width: 100%;
  background: #fff;
  padding: 15px;
  bottom: 0;
  left: 0;
}

.top-menu > li > a {
  padding: 0 15px;
  text-transform: capitalize;
}

#mm-blocker {
  z-index: 999990;
  background-color: rgba(24, 26, 32, 0.7);
}

html.mm-opening .mm-menu ~ .mm-slideout {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}

.mm-listview .mm-next {
  padding: 0 !important;
}

.mm-menu.mm-offcanvas {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  visibility: hidden;
  z-index: 999991;
  max-width: 80%;
}

@media (max-width: 1200px) {
  .mm-menu.mm-offcanvas {
    display: block;
  }
}

.mm-menu.mm-offcanvas .social-top:after {
  display: none;
}

.mm-menu.mm-offcanvas .social-top a {
  display: inline-block;
  font-size: 16px;
}

.mm-menu.mm-offcanvas .social-top a:hover, .mm-menu.mm-offcanvas .social-top a:active {
  color: var(--homez-theme-color);
}

.mm-menu.mm-offcanvas .widget {
  margin: 0;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper {
  padding: 10px;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper > * {
  margin-bottom: 15px;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper > *:last-child {
  margin: 0;
}

.mm-menu.mm-offcanvas .topbar-right-wrapper:after {
  display: none;
}

.mm-menu.mm-offcanvas .woocommerce-currency-switcher-form ul.dd-options {
  margin-top: 0;
}

.mm-menu.mm-offcanvas.mm-opened {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
  visibility: visible;
}

#mm-blocker {
  cursor: not-allowed;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li > a {
  font-weight: 400;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li > a i {
  margin-right: 5px;
  min-width: 20px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .fa-minus {
  color: var(--homez-theme-color);
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .sub-menu {
  max-width: 100%;
  display: none;
  padding: 0 15px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widgettitle,
.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widget-title {
  padding: 0;
  border: none;
  margin: 0 0 10px;
  font-size: 16px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widgettitle:before, .mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widgettitle:after,
.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widget-title:before,
.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .widget .widget-title:after {
  display: none;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .dropdown-menu-inner {
  padding-left: 20px;
}

.mobile-vertical-menu .navbar-offcanvas .navbar-nav li .menu li a {
  padding: 0;
  font-size: 14px;
}

.mobile-vertical-menu .widget {
  margin-bottom: 10px;
}

.menu-dashboard {
  overflow-x: auto;
}

.menu-dashboard a {
  display: inline-block;
  text-decoration: none;
  white-space: nowrap;
  width: 100%;
  background-color: transparent;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-weight: 600;
  padding: 10px 15px;
  border-radius: 8px;
  color: var(--homez-link-color);
}

@media (min-width: 1200px) {
  .menu-dashboard a {
    padding: 14px 20px;
    border-radius: 12px;
  }
}

.menu-dashboard a i {
  display: inline-block;
  vertical-align: text-bottom;
  margin-right: 8px;
  font-size: 1rem;
  line-height: 1;
}

@media (min-width: 1200px) {
  .menu-dashboard a i {
    margin-right: 15px;
    font-size: 20px;
  }
}

.menu-dashboard li {
  margin-bottom: 2px;
}

.menu-dashboard li:hover > a, .menu-dashboard li.active > a {
  color: #fff;
  background-color: var(--homez-link-color);
}

/* 6. pages */
.home-page-default {
  padding-top: 1.875rem;
  padding-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .home-page-default {
    padding-top: 70px;
    padding-bottom: 70px;
  }
}

.main-page .page-links {
  clear: both;
  overflow: hidden;
  padding: 1.875rem 0;
  margin: 0;
}

.main-page #comments {
  padding-top: 30px;
  clear: both;
}

body.single-property,
body.page-template-page-properties,
body[class*="property_type"],
body.blog {
  background: #F7F7F7;
}

.main-content {
  padding-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .main-content {
    padding-bottom: 60px;
  }
}

.apus-breadscrumb + .home-page-default {
  padding-top: 0;
}

.main-content-detail {
  padding-bottom: 40px;
}

@media (min-width: 768px) {
  .main-content-detail {
    padding-bottom: 50px;
  }
}

@media (min-width: 1200px) {
  .main-content-detail {
    padding-bottom: 100px;
  }
}

body.no-footer #apus-footer {
  display: none !important;
}

div.wpcf7-validation-errors {
  margin: 0;
  padding: 15px;
}

.contact-form-content {
  padding: 1.875rem;
  background: white;
  min-height: 260px;
}

.contact-form-content .rounded {
  margin-right: 10px;
  color: #fff;
  width: 40px;
  height: 40px;
  background: #cccccc;
}

.contact-form-content .rounded .fa, .contact-form-content .rounded .icon {
  font-size: 16px;
  margin: 13px;
}

.page-404 {
  position: relative;
}

.page-404 .not-found {
  padding: 30px 0;
}

@media (min-width: 1200px) {
  .page-404 .not-found {
    padding: 180px 0;
  }
}

@media (max-width: 767px) {
  .page-404 .not-found {
    text-align: center;
  }
}

.page-404 .description {
  font-size: 15px;
  margin-top: 15px;
}

.page-404 .return {
  margin-top: 0.9375rem;
}

@media (min-width: 1200px) {
  .page-404 .return {
    margin-top: 1.875rem;
  }
}

.page-404 .title-big {
  font-size: 22px;
  margin: 0;
}

@media (min-width: 1200px) {
  .page-404 .title-big {
    font-size: 30px;
  }
}

.page-404 img {
  max-width: 80%;
}

.page-404 .image-icon {
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .page-404 .image-icon {
    margin-bottom: 50px;
  }
}

@media (max-width: 767px) {
  .page-404 .top-image {
    margin-bottom: 20px;
  }
}

.top_profile {
  padding: 30px 30px 25px;
  border-bottom: 1px solid #dee6ed;
}

.top_profile .user-logo {
  margin: 0 0 25px;
}

.top_profile .logo-inner {
  width: 150px;
  height: 150px;
  padding: 5px;
  border-radius: 50%;
  background: #fff;
  overflow: hidden;
  margin: auto;
}

.top_profile .title {
  font-size: 18px;
  margin: 0;
}

.top_profile .categories a {
  color: var(--homez-text-color);
}

.top_profile .categories a:hover, .top_profile .categories a:focus {
  color: var(--homez-link-color);
}

@media (min-width: 992px) {
  .sidebar-fixed.p-left {
    padding-left: 70px;
  }
  .sidebar-fixed.p-left .sidebar-wrapper {
    left: 0;
  }
  .sidebar-fixed.p-right {
    padding-right: 70px;
  }
  .sidebar-fixed.p-right .sidebar-wrapper {
    right: 0;
  }
}

.sidebar-fixed .sidebar-wrapper {
  padding: 0;
  width: 70px;
  height: 100vh;
  overflow: auto;
  scrollbar-width: thin;
  position: fixed;
  z-index: 4;
  top: 0;
  background: #fff;
}

@media (max-width: 991px) {
  .sidebar-fixed .sidebar-wrapper {
    display: none;
  }
}

.sidebar-fixed .main-page {
  width: 100%;
  padding: 0 !important;
}

.sidebar-fixed .mobile-sidebar-btn {
  display: none !important;
}

.sidebar-fixed #main-container {
  padding-right: 0;
  padding-left: 0;
}

.sidebar-fixed #main-container .row-page {
  margin-left: 0;
  margin-right: 0;
}

@media (min-width: 992px) {
  .header_transparent .sidebar-fixed.p-left .apus-header {
    padding-left: 70px;
  }
  .header_transparent .sidebar-fixed.p-right .apus-header {
    padding-right: 70px;
  }
}

/* 7. post */
/*
* General Post Style using for all with naming class entry
*/
.post.no-results {
  text-align: center;
  margin: 0 0 30px;
}

.post.no-results .widget_search {
  margin: 25px auto 0;
  max-width: 600px;
}

.post.no-results .title-no-results {
  color: var(--homez-text-color);
  margin: 0 0 10px;
  color: var(--homez-link-color);
  font-size: 25px;
}

@media (min-width: 1200px) {
  .post.no-results .title-no-results {
    font-size: 30px;
  }
}

@media (min-width: 1200px) {
  .post.no-results {
    margin: 0 0 50px;
  }
}

.entry-title {
  font-size: 18px;
  font-weight: 600;
  word-wrap: break-word;
}

@media (min-width: 1200px) {
  .entry-title {
    font-size: 20px;
  }
}

.detail-title {
  font-weight: 700;
  margin: 0 0 15px;
  font-size: 22px;
}

@media (min-width: 1200px) {
  .detail-title {
    font-size: 32px;
  }
}

.entry-create {
  margin: 0 0 15px;
}

.entry-create > * {
  margin-right: 2px;
}

.entry-create .author {
  font-style: italic;
  text-transform: capitalize;
}

.comment-form-cookies-consent [type="checkbox"] {
  margin-right: 7px;
}

.entry-link {
  margin-top: 20px;
}

.entry-link .readmore {
  color: var(--homez-theme-color);
  text-transform: capitalize;
  font-weight: 500;
  font-size: -1.125rem;
}

.entry-link .readmore:hover {
  color: #000;
}

.entry-meta {
  margin: 0;
}

.entry-meta .fa, .entry-meta .icon {
  margin-right: 3px;
}

.wp-block-quote,
blockquote {
  margin: 1.875rem 0;
  position: relative;
  border-radius: 0;
  font-weight: 500;
  font-style: italic;
  color: var(--homez-link-color);
  font-size: 15px;
  padding: 20px;
  border-width: 0 0 0 3px;
  border-style: solid;
  border-color: var(--homez-link-color);
  background: rgba(235, 103, 83, 0.07);
}

@media (min-width: 1200px) {
  .wp-block-quote,
  blockquote {
    padding: 40px 60px 35px;
    margin: 50px 0;
  }
}

.wp-block-quote cite,
blockquote cite {
  font-size: 15px;
  font-style: normal;
  color: var(--homez-link-color);
  margin-top: 5px;
  display: block;
  font-weight: 600;
}

@media (min-width: 768px) {
  .wp-block-quote cite,
  blockquote cite {
    margin-top: 10px;
  }
}

.wp-block-quote p:last-child,
blockquote p:last-child {
  margin-bottom: 0;
}

.entry-vote {
  z-index: 1;
  display: table;
  text-align: center;
  top: 20px;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  width: 44px;
  height: 44px;
  right: 20px;
}

.entry-vote .entry-vote-inner {
  color: #fff;
  display: table-cell;
  vertical-align: middle;
  font-weight: var(--homez-heading-font-weight);
}

.entry-vote.vote-perfect .entry-vote-inner {
  color: #dc3545;
}

.entry-vote.vote-good .entry-vote-inner {
  color: #ffc107;
}

.entry-vote.vote-average .entry-vote-inner {
  color: #91e536;
}

.entry-vote.vote-bad .entry-vote-inner {
  color: #fd7e14;
}

.entry-vote.vote-poor .entry-vote-inner {
  color: #198754;
}

.blog-title {
  margin-bottom: 1.875rem;
}

.comment-form {
  margin-bottom: 10px;
}

.comment-form label {
  font-size: 14px;
  font-weight: 500;
  color: var(--homez-link-color);
  margin: 0 0 8px;
}

.comment-form .comment-form-cookies-consent {
  margin-bottom: 10px;
}

.comment-form .comment-form-cookies-consent label {
  margin-bottom: 0;
  display: inline;
  font-weight: 400;
}

.comment-form .form-group {
  margin-bottom: 1.25rem;
  position: relative;
}

.comment-form .form-submit {
  padding-top: 10px;
}

.comment-form textarea.form-control {
  height: 150px;
  resize: none;
}

@media (min-width: 1200px) {
  .comment-form textarea.form-control {
    height: 200px;
  }
}

.comment-form .form-submit {
  margin-bottom: 0;
}

.comment-form #cancel-comment-reply-link {
  color: #dc3545;
}

.comment-form .group-upload [class="hidden"] {
  display: none;
}

.comment-form .group-upload button {
  border: 1px solid var(--homez-theme-color);
  padding: 0.5rem 1rem;
  background-color: #fff;
  color: var(--homez-theme-color);
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .comment-form .group-upload button {
    padding: 1rem 2rem;
    min-width: 300px;
    text-align: center;
  }
}

.comment-form .group-upload button:hover, .comment-form .group-upload button:focus {
  background-color: var(--homez-theme-color);
  color: #fff;
  border-color: var(--homez-theme-color);
}

.comment-form .group-upload button .upload-file-btn {
  margin-top: 5px;
}

.comment-form .group-upload button i {
  margin-right: 5px;
}

.commentform.reset-button-default {
  padding-top: 0.625rem;
}

/* Post type: List widget list*/
.posts-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.posts-list > li {
  margin: 0 0 20px;
}

.posts-list > li:last-child {
  margin: 0;
}

.posts-list .entry-title {
  line-height: 1.5;
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 2px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.posts-list .image {
  width: 105px;
  padding-right: 15px;
}

.posts-list .image .image-inner {
  max-height: 80px;
  overflow: hidden;
  border-radius: 8px;
}

.posts-list .date {
  font-size: 14px;
  color: #717171;
}

.post-layout {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .post-layout {
    margin-bottom: 1.875rem;
    border-radius: 12px;
  }
}

.post-layout .post-sticky {
  background: #dc3545;
  color: #fff;
  display: inline-block;
  padding: 0 15px;
  margin: 5px 0;
  font-size: 14px;
}

.post-layout .entry-title {
  line-height: 1.5;
  margin: 0;
}

.post-layout .entry-title .stick-icon {
  display: inline-block;
  line-height: 1;
  margin-right: 5px;
}

.post-layout .top-image {
  overflow: hidden;
}

.post-layout .top-image img {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.post-layout .top-image .entry-thumb {
  margin: 0;
}

.post-layout .top-image + .col-content .date {
  position: absolute;
  z-index: 1;
  right: 20px;
  top: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  margin: 0;
}

.post-layout .top-image .date {
  position: absolute;
  z-index: 1;
  left: 10px;
  bottom: 10px;
  margin: 0;
}

.post-layout iframe {
  max-width: 100%;
}

.post-layout.sticky .entry-title a {
  color: var(--homez-theme-color);
}

.post-layout .date {
  background: #fff;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  border-radius: 8px;
  padding: 13px 22px;
  font-size: 13px;
  line-height: 1;
  margin-bottom: 10px;
}

.post-layout .date .date-d {
  margin-top: 10px;
  font-size: 20px;
  font-weight: 600;
}

.post-layout .list-categories {
  font-size: 13px;
  margin-bottom: 10px;
}

.post-layout .list-categories a {
  color: #717171;
}

.post-layout .list-categories a:hover, .post-layout .list-categories a:focus {
  color: var(--homez-link-color);
  text-decoration: none;
}

.post-layout .description {
  margin-top: 10px;
}

@media (min-width: 1200px) {
  .post-layout .description {
    margin-top: 17px;
  }
}

.post-layout:hover {
  -webkit-transform: translateY(-7px);
  -ms-transform: translateY(-7px);
  -o-transform: translateY(-7px);
  transform: translateY(-7px);
}

.post-list-item .col-content {
  padding: 20px 1.875rem 1.875rem;
}

.post-list-item.v2 .top-image {
  width: 280px;
}

.post-list-item.v2 .top-image + .col-content {
  padding-left: 0.9375rem;
}

@media (min-width: 1200px) {
  .post-list-item.v2 .top-image + .col-content {
    padding-left: 1.875rem;
  }
}

.post-list-item-v3 {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.post-list-item-v3 .top-image {
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .post-list-item-v3 .top-image {
    border-radius: 12px;
  }
}

.post-list-item-v3 .date-post {
  margin-top: 7px;
  font-size: 13px;
  color: #717171;
}

.post-list-item-v3 .entry-title {
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-list-item-v3 .col-content {
  padding-left: 20px;
}

.post-grid {
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent;
}

.post-grid .top-image {
  border-radius: 8px;
}

.post-grid .entry-title {
  font-size: 0.875rem;
  line-height: 1.75;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-grid .col-content {
  padding-top: 20px;
}

/* Post type: By Category */
.categories-blog-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1.875rem;
}

.categories-blog-list li {
  display: inline-block;
  margin-right: 10px;
}

.categories-blog-list li a {
  display: inline-block;
  position: relative;
  padding: 6px 18px;
  border: 1px solid var(--homez-link-color);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--homez-link-color);
  white-space: nowrap;
}

.categories-blog-list li a:hover, .categories-blog-list li a:focus, .categories-blog-list li a.active {
  background: var(--homez-link-color);
  border-color: var(--homez-link-color);
  color: #fff;
  text-decoration: none;
}

.top-blog-info {
  padding: 25px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #E9E9E9;
}

.top-blog-info i {
  margin-right: 10px;
}

.top-blog-info .categories {
  margin-right: 35px;
}

.top-blog-info .author a {
  color: var(--homez-theme-color);
}

.top-blog-info a {
  color: var(--homez-text-color);
}

.top-blog-info a:hover, .top-blog-info a:active {
  color: var(--homez-theme-color);
}

.category-posts {
  position: relative;
}

.category-posts::after {
  content: "";
  top: 20px;
  position: absolute;
  right: 0;
  width: 1px;
  height: 1000px;
  background: #E9E9E9;
}

.category-posts .post {
  border-bottom: 1px solid #E9E9E9;
}

.category-posts .category-posts-label {
  padding: 1px 3px;
  border-radius: 0;
  background: var(--homez-theme-color);
  font-weight: 400;
  text-transform: uppercase;
}

.category-posts .category-posts-label a {
  color: #fff;
}

.category-posts .entry-meta::after {
  display: none;
}

.category-posts .posts-more .post:last-child {
  border: 0px;
}

.category-posts .posts-more .entry-title a {
  color: #f8f9fa;
}

.category-posts .posts-more .entry-title a:hover {
  color: var(--homez-theme-color);
}

/*------------------------------------*\
    Comment List
\*------------------------------------*/
.comment-list {
  padding: 0;
  margin: 0;
  list-style: none;
  color: var(--homez-link-color);
}

.comment-list .comment-respond {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .comment-list .comment-respond {
    margin-bottom: 40px;
  }
}

.comment-list .comment-respond small {
  margin-left: 5px;
}

.comment-list .comment-respond #submit {
  min-width: auto !important;
}

.comment-list .comment-respond textarea#comment {
  height: 150px;
}

@media (min-width: 1200px) {
  .comment-list .comment-respond textarea#comment {
    height: 260px;
    border-radius: 12px;
  }
}

.comment-list #cancel-comment-reply-link {
  color: #dc3545;
}

.comment-list .comment-author {
  font-size: 13px;
  font-weight: 500;
}

.comment-list .comment-author > * {
  margin-right: 15px;
}

.comment-list .comment-author > *:last-child {
  margin-right: 0;
}

.comment-list .name-comment {
  color: var(--homez-link-color);
  margin: 4px 0;
  font-weight: 600;
  text-transform: capitalize;
  line-height: 1.4;
  font-size: 15px;
}

.comment-list .name-comment + .star-rating {
  margin-left: 15px;
}

.comment-list .date {
  font-size: 13px;
}

.comment-list .children {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .comment-list .children {
    padding-left: 70px;
  }
}

.comment-list .comment-edit-link {
  color: #dc3545;
}

.comment-list .comment-reply-link {
  color: #02ccad;
  white-space: nowrap;
}

.comment-list .comment-text {
  clear: both;
  padding-top: 10px;
}

@media (min-width: 1200px) {
  .comment-list .comment-text {
    padding-top: 15px;
  }
}

.comment-list .comment-text p:last-child {
  margin: 0;
}

.comment-list div.avatar {
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  float: left;
}

@media (min-width: 768px) {
  .comment-list div.avatar {
    width: 60px;
    height: 60px;
  }
}

.comment-list div.avatar img {
  margin: 0;
}

.comment-list div.avatar + .comment-box {
  overflow: hidden;
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .comment-list div.avatar + .comment-box {
    padding-left: 20px;
  }
}

.comment-list .the-comment {
  margin: 0 0 20px;
  padding: 0 0 20px;
  border-bottom: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  .comment-list .the-comment {
    margin: 0 0 1.875rem;
    padding: 0 0 1.875rem;
  }
}

.comment-respond #commentform {
  margin-bottom: 0;
}

.logged-in-as a + a {
  color: #dc3545;
}

/*------------------------------------*\
    Single post
\*------------------------------------*/
.social-networks li {
  padding-left: 10px;
  padding-right: 10px;
}

.social-networks li:last-child a {
  margin-right: 0;
}

.social-networks li a {
  font-size: 14px;
}

.social-networks li a:hover {
  color: var(--homez-theme-color);
}

.detail-post #comments {
  padding-top: 1.875rem;
}

@media (min-width: 1200px) {
  .detail-post #comments {
    padding-top: 55px;
  }
}

.post-navigation {
  position: relative;
}

@media (min-width: 1200px) {
  .post-navigation {
    padding: 5px 0;
  }
}

.post-navigation .screen-reader-text {
  display: none;
}

.post-navigation .nav-links {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: -15px;
  margin-right: -15px;
}

.post-navigation .nav-links > * {
  width: 50%;
  padding-left: 15px;
  padding-right: 15px;
}

.post-navigation .nav-links > * i {
  font-weight: 400;
  font-size: 0.75rem;
  margin-right: 3px;
}

.post-navigation .nav-links > *.nav-next {
  text-align: right;
  margin-left: auto;
}

.post-navigation .nav-links > *.nav-next i {
  margin: 0;
  margin-left: 3px;
}

.post-navigation .nav-links > *.nav-next .title-direct {
  float: right;
}

.post-navigation .nav-links > * > a:hover {
  text-decoration: none;
}

.post-navigation .nav-links > * > a:hover .navi {
  color: var(--homez-theme-color);
}

.post-navigation .nav-links .title-direct {
  font-size: 13px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-width: 80%;
  color: var(--homez-link-color);
}

.post-navigation .nav-links .navi {
  font-size: 15px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  color: var(--homez-link-color);
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 2px;
}

.author-info {
  padding: 20px 0;
}

@media (min-width: 1200px) {
  .author-info {
    padding: 40px 0;
  }
}

.author-info .author-title {
  font-size: 15px;
  margin: 0 0 10px;
  text-transform: capitalize;
}

.author-info .avatar-img {
  width: 70px;
  height: 70px;
  overflow: hidden;
  border-radius: 50%;
}

.author-info .description {
  color: var(--homez-link-color);
  padding-left: 0.9375rem;
}

@media (min-width: 1200px) {
  .author-info .description {
    padding-left: 1.875rem;
  }
}

.wrapper-posts-related {
  padding: 0 0 1.875rem 0;
}

@media (min-width: 768px) {
  .wrapper-posts-related {
    padding: 0 0 50px 0;
  }
}

@media (min-width: 1200px) {
  .wrapper-posts-related {
    padding: 0 0 100px 0;
  }
}

.wrapper-posts-related .property-item,
.wrapper-posts-related .post-grid {
  margin-bottom: 0;
}

.related-posts .slick-list {
  padding-top: 10px;
}

.related-posts .title {
  font-size: 20px;
  margin: 0 0 15px;
}

@media (min-width: 1200px) {
  .related-posts .title {
    margin-bottom: 1.875rem;
    font-size: 30px;
  }
}

/*------------------------------------*\
    Blog Masonry Page
\*------------------------------------*/
.gallery {
  margin-left: -15px;
  margin-right: -15px;
  overflow: hidden;
}

.gallery .gallery-item {
  float: left;
  margin-bottom: 15px;
  padding-right: 15px;
  padding-left: 15px;
  position: relative;
}

.gallery .gallery-item figcaption {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  color: #fff;
  max-height: 50%;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  margin-left: 15px;
  margin-right: 15px;
  opacity: 0;
  filter: alpha(opacity=0);
  padding: 8px 15px;
}

.gallery .gallery-item:hover figcaption {
  opacity: 1;
  filter: alpha(opacity=100);
}

.gallery.gallery-columns-9 .gallery-item {
  width: 11%;
}

.gallery.gallery-columns-8 .gallery-item {
  width: 12.5%;
}

.gallery.gallery-columns-7 .gallery-item {
  width: 14%;
}

.gallery.gallery-columns-6 .gallery-item {
  width: 16.5%;
}

.gallery.gallery-columns-5 .gallery-item {
  width: 20%;
}

.gallery.gallery-columns-4 .gallery-item {
  width: 25%;
}

.gallery.gallery-columns-3 .gallery-item {
  width: 33%;
}

.gallery.gallery-columns-1 .gallery-item {
  width: 100%;
}

.gallery.gallery-columns-2 .gallery-item {
  width: 50%;
}

.comment-navigation {
  overflow: hidden;
  padding: 20px 0;
}

.comment-navigation .nav-links > div {
  display: inline-block;
}

.comment-navigation .nav-links > div + div {
  line-height: 1.1;
  margin-left: 15px;
  padding-left: 15px;
  border-left: 2px solid #E9E9E9;
}

.header-info-blog {
  font-size: 13px;
  color: #717171;
  margin-top: 1.875rem;
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .header-info-blog {
    margin-top: 55px;
    margin-bottom: 40px;
  }
}

.header-info-blog a {
  color: #717171;
}

.header-info-blog a:hover, .header-info-blog a:focus {
  color: var(--homez-link-color);
}

.header-info-blog .entry-thumb {
  border-radius: 8px;
  margin-top: 1.875rem;
  overflow: hidden;
  max-width: 1600px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1200px) {
  .header-info-blog .entry-thumb {
    border-radius: 12px;
    margin-top: 55px;
  }
}

.header-info-blog .entry-title {
  font-size: 25px;
  margin: 0 0 15px;
}

@media (min-width: 1200px) {
  .header-info-blog .entry-title {
    font-size: 30px;
  }
}

.header-info-blog .top-detail-info > div + div {
  margin-left: 15px;
  padding-left: 15px;
  border-left: 1px solid #E9E9E9;
}

.header-info-blog .author img.avatar {
  border-radius: 50%;
  margin: 0 10px 0 0;
}

.header-info-blog.v2 .entry-thumb {
  margin-top: 1.875rem;
}

.max-800 {
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.main-content-detail.main .inner-content-bottom {
  max-width: 925px;
  margin-left: auto;
  margin-right: auto;
}

.main-content-detail .inner-content-bottom {
  margin-top: 1.875rem;
}

@media (min-width: 1200px) {
  .main-content-detail .inner-content-bottom {
    margin-top: 40px;
  }
}

.detail-post iframe {
  max-width: 100%;
}

.detail-post .bottom-info {
  margin-bottom: 20px;
}

.detail-post .bottom-info .suffix {
  font-style: italic;
}

.detail-post .bottom-info .author {
  text-transform: uppercase;
}

.detail-post .bottom-info > div {
  display: inline-block;
  vertical-align: middle;
}

.detail-post .bottom-info a {
  color: var(--homez-text-color);
}

.detail-post .bottom-info a:hover, .detail-post .bottom-info a:active {
  color: var(--homez-theme-color);
}

.detail-post .entry-tags-list a {
  font-size: 13px !important;
  padding: 0 18px;
  line-height: 35px;
  border: 0;
  background: rgba(235, 103, 83, 0.07);
  color: var(--homez-link-color);
}

.detail-post .tag-social {
  border-bottom: 1px solid #E9E9E9;
  border-top: 1px solid #E9E9E9;
  padding: 20px 0;
}

@media (min-width: 1200px) {
  .detail-post .tag-social {
    padding: 40px 0;
  }
}

.detail-post .entry-tags-list {
  display: block;
  margin: 10px 0 0;
  position: relative;
}

.detail-post .post-navigation {
  border-width: 1px 0;
  border-style: solid;
  border-color: #E9E9E9;
  padding: 20px 0;
  margin-top: -1px;
}

@media (min-width: 1200px) {
  .detail-post .post-navigation {
    padding: 40px 0;
  }
}

.detail-post .entry-description {
  color: var(--homez-link-color);
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .detail-post .entry-description {
    margin-bottom: 40px;
  }
}

.author-post .avatar {
  border-radius: 50%;
}

.author-post .avatar-img {
  padding-right: 8px;
  float: left;
}

.author-post .name-author {
  display: inline-block;
  margin-top: 9px;
}

.author-wrapper .avatar-img {
  overflow: hidden;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  -ms-align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
}

.author-wrapper .avatar-img img {
  margin: 0 !important;
}

.author-wrapper .author-title {
  font-size: 0.875rem;
  font-weight: 400;
  margin: 0;
  text-transform: capitalize;
}

.author-wrapper .author-title a {
  color: #777777;
}

.author-wrapper .author-title a:hover, .author-wrapper .author-title a:focus {
  color: var(--homez-link-color);
}

.author-wrapper .right-inner {
  padding-left: 10px;
}

.comment-reply-title {
  font-size: 17px;
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .comment-reply-title {
    margin-bottom: 20px;
  }
}

.comment-reply-title #cancel-comment-reply-link {
  color: #dc3545;
}

.comments-title {
  font-size: 17px;
  margin: 0 0 20px;
}

@media (min-width: 1200px) {
  .comments-title {
    margin-bottom: 30px;
  }
}

.widget-blogs .slick-list {
  padding-top: 10px;
}

.widget-blogs .slick-list .post-layout {
  margin-bottom: 0;
}

.widget-blogs .info-widget-top {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .widget-blogs .info-widget-top {
    margin-bottom: 40px;
  }
}

.info-widget-top {
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .info-widget-top {
    margin-bottom: 50px;
  }
}

.info-widget-top .widget-title,
.info-widget-top .widgettitle {
  font-size: 22px;
  margin: 0;
}

@media (min-width: 1200px) {
  .info-widget-top .widget-title,
  .info-widget-top .widgettitle {
    font-size: 30px;
  }
}

.info-widget-top .des {
  color: #717171;
  margin-top: 7px;
}

@media (max-width: 575px) {
  .info-widget-top .ms-auto {
    margin-top: 10px;
  }
}

/* 8. effect */
.effect-1 {
  position: relative;
}

.effect-1:after {
  content: '';
  display: block;
  width: 0px;
  height: 1px;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  left: 0;
  bottom: 0;
  right: 0;
  background: transparent;
  margin: auto;
}

.effect-1:hover:after {
  width: 100%;
  height: 1px;
  background: var(--homez-theme-color);
}

.zoom-2 {
  overflow: hidden;
  display: block;
  border-radius: 3px;
}

.zoom-2 img {
  position: relative;
  width: 100%;
  height: auto;
  -webkit-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.zoom-2:hover img {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.close .fa {
  -webkit-transition: all 1s ease-in-out;
  -o-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
}

.close:hover .fa {
  -webkit-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  transform: rotate(360deg);
}

.image-overlay-1:after, .image-overlay-1:before {
  content: "";
  display: block;
  position: absolute;
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.image-overlay-1:after {
  top: -100%;
}

.image-overlay-1:before {
  bottom: -100%;
}

.image-overlay-1:hover:after {
  top: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}

.image-overlay-1:hover:before {
  bottom: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}

@keyframes scale {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0.8) translateY(50px);
    -moz-transform: scale(0.8) translateY(50px);
    -ms-transform: scale(0.8) translateY(50px);
    -o-transform: scale(0.8) translateY(50px);
    transform: scale(0.8) translateY(50px);
    transition-property: opacity, transform;
    transition-duration: 1.2s;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@-webkit-keyframes scale {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0.8) translateY(50px);
    -moz-transform: scale(0.8) translateY(50px);
    -ms-transform: scale(0.8) translateY(50px);
    -o-transform: scale(0.8) translateY(50px);
    transform: scale(0.8) translateY(50px);
    transition-property: opacity, transform;
    transition-duration: 1.2s;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@-webkit-keyframes fancy {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translateY(110px) rotateY(20deg);
    -moz-transform: translateY(110px) rotateY(20deg);
    -ms-transform: translateY(110px) rotateY(20deg);
    -o-transform: translateY(110px) rotateY(20deg);
    transform: translateY(110px) rotateY(20deg);
    transform-origin: left;
    transition-property: opacity, transform;
    transition-duration: 1.2s;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fancy {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translateY(110px) rotateY(20deg);
    -moz-transform: translateY(110px) rotateY(20deg);
    -ms-transform: translateY(110px) rotateY(20deg);
    -o-transform: translateY(110px) rotateY(20deg);
    transform: translateY(110px) rotateY(20deg);
    transform-origin: left;
    transition-property: opacity, transform;
    transition-duration: 1.2s;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@-webkit-keyframes slide-up {
  0% {
    -webkit-transform: translate3d(0, 3rem, 0);
    transform: translate3d(0, 3rem, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slide-up {
  0% {
    -webkit-transform: translate3d(0, 3rem, 0);
    transform: translate3d(0, 3rem, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes slide-left {
  0% {
    -webkit-transform: translate3d(3rem, 0, 0);
    transform: translate3d(3rem, 0, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slide-left {
  0% {
    -webkit-transform: translate3d(3rem, 0, 0);
    transform: translate3d(3rem, 0, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes slide-right {
  0% {
    -webkit-transform: translate3d(-3rem, 0, 0);
    transform: translate3d(-3rem, 0, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slide-right {
  0% {
    -webkit-transform: translate3d(-3rem, 0, 0);
    transform: translate3d(-3rem, 0, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes slide-down {
  0% {
    -webkit-transform: translate3d(0, -3rem, 0);
    transform: translate3d(0, -3rem, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slide-down {
  0% {
    -webkit-transform: translate3d(0, -3rem, 0);
    transform: translate3d(0, -3rem, 0);
    opacity: 0;
    filter: alpha(opacity=0);
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.scale {
  animation-name: scale;
}

.fancy {
  animation-name: fancy;
}

.slide-up {
  animation-name: slide-up;
}

.slide-left {
  animation-name: slide-left;
}

.slide-right {
  animation-name: slide-right;
}

.slide-down {
  animation-name: slide-down;
}

.delay-1 {
  transition-delay: 0.1s;
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.delay-2 {
  transition-delay: 0.2s;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.delay-3 {
  transition-delay: 0.3s;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

@keyframes scale_icon_map {
  form {
    -webkit-transform: rotateX(-60deg) scale(0);
    -moz-transform: rotateX(-60deg) scale(0);
    -ms-transform: rotateX(-60deg) scale(0);
    -o-transform: rotateX(-60deg) scale(0);
    transform: rotateX(-60deg) scale(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    -webkit-transform: rotateX(-60deg) scale(1.2);
    -moz-transform: rotateX(-60deg) scale(1.2);
    -ms-transform: rotateX(-60deg) scale(1.2);
    -o-transform: rotateX(-60deg) scale(1.2);
    transform: rotateX(-60deg) scale(1.2);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@-webkit-keyframes scale_icon_map {
  form {
    -webkit-transform: rotateX(-60deg) scale(0);
    -moz-transform: rotateX(-60deg) scale(0);
    -ms-transform: rotateX(-60deg) scale(0);
    -o-transform: rotateX(-60deg) scale(0);
    transform: rotateX(-60deg) scale(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    -webkit-transform: rotateX(-60deg) scale(1.2);
    -moz-transform: rotateX(-60deg) scale(1.2);
    -ms-transform: rotateX(-60deg) scale(1.2);
    -o-transform: rotateX(-60deg) scale(1.2);
    transform: rotateX(-60deg) scale(1.2);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

/* 10. widgets layout */
.vertical-wrapper {
  position: relative;
}

.vertical-wrapper .action-vertical {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  display: inline-block;
  padding: 6px 22px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 40px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
}

.vertical-wrapper .action-vertical i {
  display: inline-block;
  margin-right: 10px;
  font-weight: 400;
  line-height: 1;
  vertical-align: middle;
}

.vertical-wrapper .content-vertical {
  padding: 0;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100% !important;
  z-index: 3;
  min-width: 330px;
  margin-top: 10px;
}

.apus-vertical-menu {
  padding: 0;
  background: #fff;
  margin: 0;
  list-style: none;
  border-radius: 6px;
  border: 1px solid #E9E9E9;
}

.apus-vertical-menu > li {
  display: block;
  width: 100%;
}

.apus-vertical-menu > li > a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  padding: 0 20px;
  line-height: 50px;
  background: transparent;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .apus-vertical-menu > li > a {
    line-height: 55px;
  }
}

.apus-vertical-menu > li > a:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  position: absolute;
  top: 0;
  left: -1px;
  width: 2px;
  height: 100%;
  background: var(--homez-theme-color);
  opacity: 0;
  filter: alpha(opacity=0);
}

.apus-vertical-menu > li > a:after {
  margin-left: auto;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.apus-vertical-menu > li > a > i,
.apus-vertical-menu > li > a > img {
  font-size: 22px;
  margin-right: 12px;
  width: 22px;
  display: inline-block;
  color: var(--homez-second-color);
  line-height: 1;
  vertical-align: middle;
}

.apus-vertical-menu > li .apus-container {
  padding: 10px 35px;
}

.apus-vertical-menu > li:hover > a, .apus-vertical-menu > li.active > a {
  color: var(--homez-link-color);
  background: #F0EFEC;
}

.apus-vertical-menu > li:hover > a:before, .apus-vertical-menu > li.active > a:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-vertical-menu li:hover > .dropdown-menu {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}

.apus-vertical-menu .text-label {
  font-size: 12px;
  vertical-align: super;
  margin-left: 5px;
  color: var(--homez-theme-color);
  font-family: var(--homez-main-font);
}

.apus-vertical-menu .text-label.label-hot {
  color: #dc3545;
}

.apus-vertical-menu .text-label.label-new {
  color: #198754;
}

.apus-vertical-menu .dropdown-menu {
  min-width: 240px;
  min-height: 100%;
  visibility: hidden;
  padding: 0.9375rem;
  font-size: 14px;
  border-radius: 6px;
  display: block;
  border: 1px solid #E9E9E9;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

@media (min-width: 1200px) {
  .apus-vertical-menu .dropdown-menu {
    padding: 1.875rem;
  }
}

.apus-vertical-menu .dropdown-menu > li > a {
  color: var(--homez-link-color);
  background: transparent !important;
  padding: 0;
}

.apus-vertical-menu .dropdown-menu > li > a > i,
.apus-vertical-menu .dropdown-menu > li > a > img {
  font-size: 20px;
  margin-right: 10px;
  width: 15px;
  display: inline-block;
}

.apus-vertical-menu .dropdown-menu > li:hover > a, .apus-vertical-menu .dropdown-menu > li.active > a, .apus-vertical-menu .dropdown-menu > li:active > a {
  color: var(--homez-theme-color);
}

.apus-vertical-menu .dropdown-menu .wpb_button, .apus-vertical-menu .dropdown-menu .wpb_content_element, .apus-vertical-menu .dropdown-menu ul.wpb_thumbnails-fluid > li,
.apus-vertical-menu .dropdown-menu .widget {
  margin: 0;
}

.apus-vertical-menu .dropdown-menu .widget .widget-title, .apus-vertical-menu .dropdown-menu .widget .widgettitle, .apus-vertical-menu .dropdown-menu .widget .widget-heading {
  margin: 0 0 10px;
  font-size: 18px;
  padding: 0;
}

.apus-vertical-menu .dropdown-menu .widget .widget-title:after, .apus-vertical-menu .dropdown-menu .widget .widget-title:before, .apus-vertical-menu .dropdown-menu .widget .widgettitle:after, .apus-vertical-menu .dropdown-menu .widget .widgettitle:before, .apus-vertical-menu .dropdown-menu .widget .widget-heading:after, .apus-vertical-menu .dropdown-menu .widget .widget-heading:before {
  display: none;
}

.apus-vertical-menu .aligned-left:hover > a:after {
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

.apus-vertical-menu .aligned-left > .dropdown-menu {
  top: 0;
  left: 100%;
  margin: 0 0 0 -1px;
}

.apus-vertical-menu .aligned-right:hover > a:after {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

.apus-vertical-menu .aligned-right > .dropdown-menu {
  top: 0;
  right: 100%;
  left: inherit;
  margin: 0 -1px 0 0;
}

.apus_custom_menu.center {
  text-align: center;
}

.apus_custom_menu.center li {
  display: inline-block;
  margin: 0 15px;
}

.apus_custom_menu.left {
  text-align: left;
}

.apus_custom_menu.right {
  text-align: right;
}

.apus_custom_menu.inline li {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 0;
  margin-right: 20px;
}

@media (min-width: 1200px) {
  .apus_custom_menu.inline li {
    margin-right: 40px;
  }
}

.apus_custom_menu.inline li:last-child {
  margin: 0;
}

.slick-carousel {
  position: relative;
  margin-right: -8px;
  margin-left: -8px;
}

@media (min-width: 768px) {
  .slick-carousel {
    margin-right: -15px;
    margin-left: -15px;
  }
}

.slick-carousel .slick-arrow {
  background: #fff;
  padding: 0;
  display: inline-block;
  font-size: 12px;
  width: 35px;
  height: 35px;
  line-height: 35px;
  opacity: 0.8;
  filter: alpha(opacity=80);
  color: var(--homez-link-color);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  transform: translate(0, -50%);
  z-index: 1;
  border: 1px solid transparent;
  border-radius: 50%;
  -webkit-box-shadow: 0 10px 35px 0 rgba(5, 16, 54, 0.1);
  box-shadow: 0 10px 35px 0 rgba(5, 16, 54, 0.1);
}

@media (min-width: 1200px) {
  .slick-carousel .slick-arrow {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 15px;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.slick-carousel .slick-arrow .textnav {
  display: none;
}

.slick-carousel .slick-arrow:hover, .slick-carousel .slick-arrow:focus {
  color: #fff;
  background: var(--homez-link-color);
  border-color: var(--homez-link-color);
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-box-shadow: 0 10px 35px 0 rgba(5, 16, 54, 0.1);
  box-shadow: 0 10px 35px 0 rgba(5, 16, 54, 0.1);
}

.slick-carousel .slick-prev {
  left: 5px;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-prev {
    left: -10px;
  }
}

.slick-carousel .slick-next {
  right: 5px;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-next {
    right: -10px;
  }
}

.slick-carousel .slick-slide {
  outline: none !important;
  padding-left: 8px;
  padding-right: 8px;
}

@media (min-width: 768px) {
  .slick-carousel .slick-slide {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.slick-carousel.no-gap {
  margin: 0;
}

.slick-carousel.no-gap .slick-slide {
  padding-left: 0;
  padding-right: 0;
}

.slick-carousel.gap-10 {
  margin-left: -5px;
  margin-right: -5px;
}

.slick-carousel.gap-10 .slick-slide {
  padding-left: 5px;
  padding-right: 5px;
}

.slick-carousel.gap-2 {
  margin-left: -1px;
  margin-right: -1px;
}

.slick-carousel.gap-2 .slick-slide {
  padding-left: 1px;
  padding-right: 1px;
}

.slick-carousel.show-text .textnav {
  display: inline-block;
  margin: 0 2px;
}

.slick-carousel.show-text .slick-arrow {
  width: auto;
  height: auto;
  background: transparent !important;
  font-weight: 500;
  font-size: 12px;
  color: var(--homez-link-color);
}

.slick-carousel.show-text .slick-arrow:hover, .slick-carousel.show-text .slick-arrow:active, .slick-carousel.show-text .slick-arrow:focus {
  color: var(--homez-theme-color);
}

.slick-carousel.show-text .slick-prev {
  left: 0;
  right: inherit;
}

.slick-carousel.show-text .slick-next {
  right: 0;
  left: inherit;
}

.slick-carousel .slick-track {
  margin: inherit;
}

.slick-carousel .slick-dots {
  margin: 0;
  padding: 20px 0 0;
  text-align: center;
  list-style: none;
  line-height: 1;
}

@media (min-width: 1200px) {
  .slick-carousel .slick-dots {
    padding: 1.875rem 0 0;
  }
}

.slick-carousel .slick-dots li {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: relative;
  display: inline-block;
  cursor: pointer;
  margin: 0 5px;
  border: 0;
  padding: 0;
  background: transparent;
}

.slick-carousel .slick-dots li button {
  border: none;
  display: block;
  text-indent: -9999em;
  width: 8px;
  height: 8px;
  -webkit-transform: scale(0.85);
  -ms-transform: scale(0.85);
  -o-transform: scale(0.85);
  transform: scale(0.85);
  padding: 0;
  background: #C0C0C0;
  border-radius: 8px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.slick-carousel .slick-dots li.slick-active button {
  background: #222222;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.slick-carousel.st_white .slick-dots button {
  background-color: #fff !important;
  -webkit-transform: scale(0.7);
  -ms-transform: scale(0.7);
  -o-transform: scale(0.7);
  transform: scale(0.7);
}

.slick-carousel.st_white .slick-dots .slick-active button {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.widget-socials .social {
  padding: 0;
  list-style: none;
  margin: 0;
}

.widget-socials .social > li {
  display: inline-block;
}

.widget-socials .social a {
  font-size: 0.875rem;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--homez-theme-color);
  color: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .widget-socials .social a {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
}

.widget-socials .social a:hover, .widget-socials .social a:focus {
  color: #fff;
  background: var(--homez-theme-hover-color);
}

.widget-socials.special {
  flex-direction: row-reverse;
  transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  -o-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  -webkit-transform: rotate(-90deg) translateX(-100%);
  -moz-transform: rotate(-90deg) translateX(-100%);
  -ms-transform: rotate(-90deg) translateX(-100%);
  -o-transform: rotate(-90deg) translateX(-100%);
  transform: rotate(-90deg) translateX(-100%);
}

.widget-socials.special .social a {
  background: transparent;
  color: var(--homez-link-color);
  width: 30px;
  height: 30px;
}

.widget-socials.special .social a:hover, .widget-socials.special .social a:focus {
  background: transparent;
  color: var(--homez-theme-color);
}

.widget-socials.special > * {
  padding-right: 30px;
  white-space: nowrap;
}

@media (min-width: 1200px) {
  .widget-socials.special > * {
    padding-right: 60px;
  }
}

.list-icon {
  margin-bottom: 0.75rem;
}

.list-icon:last-child {
  margin-bottom: 0;
}

.list-icon .title {
  margin: 0;
}

.list-icon .box-content {
  padding-left: 10px;
}

@media (min-width: 1200px) {
  .list-icon .box-content {
    padding-left: 20px;
  }
}

a.direction {
  text-decoration: underline;
  color: var(--homez-theme-color);
}

a.direction:hover, a.direction:focus {
  color: var(--homez-theme-color);
  text-decoration: none;
}

.vertical-icon {
  position: relative;
  display: block;
  width: 25px;
  height: 12px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
}

.vertical-icon:after, .vertical-icon:before {
  content: '';
  position: absolute;
  right: 0;
  bottom: 0;
  width: 20px;
  height: 2px;
  background-color: var(--homez-link-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.vertical-icon:after {
  width: 25px;
  height: 2px;
  top: 0;
  bottom: inherit;
}

.vertical-icon:focus:before, .vertical-icon:hover:before {
  width: 100%;
}

.vertical-icon.st_right:after, .vertical-icon.st_right:before {
  height: 1px;
}

.vertical-icon.st_right:before {
  left: 0;
  right: inherit;
}

.navbar-wrapper .close-navbar-sidebar {
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 40px;
  height: 40px;
  color: var(--homez-link-color);
  background-color: #F7F7F7;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  display: inline-block;
}

.navbar-wrapper .close-navbar-sidebar i {
  font-size: 13px;
}

.navbar-wrapper .close-navbar-sidebar:hover, .navbar-wrapper .close-navbar-sidebar:focus {
  background-color: #dc3545;
  color: #fff;
}

.navbar-wrapper .navbar-sidebar-wrapper {
  z-index: 3;
  position: fixed;
  overflow-y: auto;
  scrollbar-width: thin;
  right: 0;
  top: 0;
  -webkit-transition: all 0.35s ease-in-out 0s;
  -o-transition: all 0.35s ease-in-out 0s;
  transition: all 0.35s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  width: 400px;
  max-width: 80%;
  height: 100vh;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
  background: #fff;
}

.navbar-wrapper .navbar-sidebar-wrapper.active {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.navbar-wrapper .navbar-sidebar-overlay {
  background: rgba(24, 26, 32, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  cursor: no-drop;
  visibility: hidden;
  z-index: 2;
}

.navbar-wrapper .navbar-sidebar-overlay.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.navbar-wrapper.st_left .navbar-sidebar-wrapper {
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  right: initial;
  left: 0;
}

.navbar-wrapper.st_left .navbar-sidebar-wrapper.active {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.navbar-wrapper .navbar-header {
  padding: 20px 1.875rem;
  border-bottom: 1px solid #E9E9E9;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
}

.navbar-wrapper .title-navbar {
  margin: 0;
  font-size: 20px;
}

.navbar-wrapper .widget-nav-menu {
  padding: 0;
}

.navbar-wrapper .widget-nav-menu .menu li {
  margin-bottom: 2px;
}

.navbar-wrapper .widget-nav-menu .menu li > a {
  font-size: 15px;
  font-weight: 600;
  display: inline-block;
  width: 100%;
  padding: 11px 1.875rem;
  position: relative;
  text-decoration: none;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.navbar-wrapper .widget-nav-menu .menu li > a:before {
  content: '';
  width: 2px;
  height: 100%;
  background: var(--homez-theme-color);
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
}

.navbar-wrapper .widget-nav-menu .menu li > a:hover {
  background: var(--homez-theme-color-007);
}

.navbar-wrapper .widget-nav-menu .menu li > a:hover:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.navbar-wrapper .widget-nav-menu .menu li.active > a {
  background: var(--homez-theme-color-007);
}

.navbar-wrapper .widget-nav-menu .menu li.active > a:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.wpml-ls-legacy-dropdown {
  width: auto;
}

.wpml-ls-legacy-dropdown > ul {
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
}

.wpml-ls-legacy-dropdown a {
  display: block;
  padding: 3px 10px;
  border: 0;
  background-color: transparent;
  line-height: 1.87;
}

.wpml-ls-legacy-dropdown a span {
  vertical-align: baseline;
}

.wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  padding: 0;
}

.wpml-ls-legacy-dropdown .wpml-ls-current-language:hover .wpml-ls-sub-menu {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.wpml-ls-legacy-dropdown .wpml-ls-current-language:hover a.wpml-ls-item-toggle {
  color: #fff;
}

.wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
  position: absolute;
  z-index: 2;
  top: inherit;
  bottom: 100%;
  width: 100%;
  background-color: #fff;
  border: 1px solid #E9E9E9;
  border-radius: 6px;
  font-size: 14px;
  list-style: none;
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin: 0 0 13px;
  -webkit-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  min-width: 130px;
  padding: 15px 20px;
}

.wpml-ls-legacy-dropdown .wpml-ls-sub-menu a {
  padding: 0;
}

.wpml-ls-legacy-dropdown .wpml-ls-sub-menu:before {
  content: '';
  position: absolute;
  top: 100%;
  left: 20px;
  border-width: 8px;
  border-style: solid;
  border-color: #fff transparent transparent;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

.wpml-ls-legacy-dropdown a.wpml-ls-item-toggle {
  background-color: rgba(255, 255, 255, 0.05);
  position: relative;
  color: rgba(255, 255, 255, 0.7);
  padding: 7px 20px;
  font-size: 14px;
  border-radius: 6px;
}

.wpml-ls-legacy-dropdown a.wpml-ls-item-toggle:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 13px;
  bottom: 100%;
  left: 0;
}

.wpml-ls-legacy-dropdown a.wpml-ls-item-toggle:after {
  display: inline-block;
  margin-left: 0.4em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}

.wpml-ls-legacy-dropdown a.wpml-ls-item-toggle:hover {
  color: #fff;
}

.widget_icl_lang_sel_widget.st_normal .wpml-ls-current-language a.wpml-ls-item-toggle {
  border: 1px solid #E9E9E9;
  color: var(--homez-text-color);
  background: #fff;
}

.widget_icl_lang_sel_widget.st_normal .wpml-ls-current-language:hover a.wpml-ls-item-toggle {
  color: var(--homez-second-color);
  border-color: #222;
  background: #fff;
}

.social-link {
  display: inline-block;
  margin: 0 5px;
  padding: 0;
}

.social-link li {
  display: inline-block;
  margin: 0 5px;
}

.social-link li a {
  background: #f4f4f4 none repeat scroll 0 0;
  border-radius: 100%;
  color: var(--homez-text-color);
  display: inline-block;
  height: 40px;
  line-height: 38px;
  text-align: center;
  width: 40px;
  border: 1px solid #E9E9E9;
}

.social-link.lighten li a {
  background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
  border: 1px solid #ffffff;
  color: #ffffff;
}

.widget-gallery .image {
  position: relative;
}

.widget-gallery .image:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: var(--homez-theme-color);
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: 2;
}

.widget-gallery .image .content-cover {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 9;
}

.widget-gallery .image:hover:before {
  opacity: 0.9;
  filter: alpha(opacity=90);
}

.widget-gallery .image:hover .content-cover {
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget-gallery .popup-image-gallery {
  width: 60px;
  height: 60px;
  line-height: 60px;
  display: inline-block;
  text-align: center;
  background: #fff;
  font-size: 24px;
  color: var(--homez-theme-color);
  border-radius: 50%;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.widget-gallery .popup-image-gallery:hover, .widget-gallery .popup-image-gallery:active {
  color: var(--homez-theme-color);
  background: #e6e6e6;
}

.widget-gallery .title {
  font-size: 24px;
  font-size: var(--homez-main-font);
  margin: 0;
  color: #fff;
}

.widget-gallery .description {
  color: #e0dede;
  font-size: 12px;
  margin-bottom: 20px;
}

.widget-gallery .gutter-default {
  margin-left: 0;
  margin-right: 0;
}

.widget-gallery .gutter-default > div {
  padding-right: 0;
  padding-left: 0;
}

.widget-gallery.gutter30 .title {
  font-size: 18px;
}

.widget-gallery.gutter30 .image {
  margin-bottom: 30px;
}

.widget-gallery.gutter30 .description {
  margin-bottom: 10px;
}

.widget-gallery.gutter30 .gutter-default {
  margin-left: -15px;
  margin-right: -15px;
}

.widget-gallery.gutter30 .gutter-default > div {
  padding-right: 15px;
  padding-left: 15px;
}

.item-features-inner .title {
  font-size: 15px;
  margin: 0;
}

.item-features-inner .description {
  margin-top: 12px;
}

.item-features-inner.style1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}

.item-features-inner.style1 .top-inner {
  flex-shrink: 0;
}

.item-features-inner.style1 .features-box-content {
  flex-grow: 1;
  padding-left: 20px;
}

.item-features-inner.style1 .features-box-image {
  overflow: hidden;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 25px;
  background: var(--homez-theme-color-007);
  color: var(--homez-theme-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .item-features-inner.style1 .features-box-image {
    width: 70px;
    height: 70px;
    font-size: 30px;
  }
}

.item-features-inner.style1:hover .features-box-image {
  background-color: var(--homez-theme-color);
  color: #fff;
}

.item-features-inner.style2 {
  background: #fff;
  border-radius: 8px;
  padding: 0.9375rem;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

@media (min-width: 1200px) {
  .item-features-inner.style2 {
    padding: 40px;
    border-radius: 12px;
  }
}

.item-features-inner.style2 .features-box-image {
  justify-content: start !important;
  font-size: 30px;
  line-height: 1;
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .item-features-inner.style2 .features-box-image {
    font-size: 40px;
    margin-bottom: 20px;
  }
}

.item-features-inner.style3 .features-box-image {
  overflow: hidden;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 25px;
  background: var(--homez-theme-color-007);
  color: var(--homez-theme-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .item-features-inner.style3 .features-box-image {
    width: 70px;
    height: 70px;
    font-size: 30px;
  }
}

.item-features-inner.style3 .top-inner {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .item-features-inner.style3 .top-inner {
    margin-bottom: 20px;
  }
}

.item-features-inner.style3 .title {
  font-size: 18px;
}

@media (min-width: 1200px) {
  .item-features-inner.style3 .title {
    font-size: 20px;
  }
}

.widget-features-box .item-grid {
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .widget-features-box .item-grid {
    margin-bottom: 1.875rem;
  }
}

.widget-features-box .item-grid:last-child {
  margin-bottom: 0;
}

.widget-testimonials .star {
  color: #E59819;
}

.widget-testimonials .star .inner {
  width: 79px;
  position: relative;
  font-size: 10px;
  color: #c1cde4;
  letter-spacing: 5px;
}

.widget-testimonials .star .inner:before {
  content: "\f005\f005\f005\f005\f005";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.widget-testimonials .w-percent {
  color: #E59819;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.widget-testimonials .w-percent:before {
  content: "\f005\f005\f005\f005\f005";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.widget-testimonials .slick-active .testimonials-item {
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

.widget-testimonials .widgettitle {
  font-size: 22px;
  margin: 0;
}

@media (min-width: 1200px) {
  .widget-testimonials .widgettitle {
    font-size: 30px;
  }
}

.widget-testimonials .des {
  color: #717171;
  margin-top: 7px;
}

.widget-testimonials .info-top {
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .widget-testimonials .info-top {
    margin-bottom: 50px;
  }
}

@media (min-width: 768px) {
  .widget-testimonials .info-top + .slick-carousel .slick-dots {
    padding: 0;
    text-align: left;
    position: absolute;
    right: 0;
    top: -55px;
  }
}

@media (min-width: 768px) and (min-width: 1200px) {
  .widget-testimonials .info-top + .slick-carousel .slick-dots {
    top: -75px;
  }
}

.widget-testimonials.style4 .slick-carousel {
  padding-bottom: 60px;
}

@media (min-width: 1200px) {
  .widget-testimonials.style4 .slick-carousel {
    padding-bottom: 100px;
  }
}

.widget-testimonials.style4 .slick-carousel .slick-arrow {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
  top: inherit;
  bottom: 0;
}

.widget-testimonials.style4 .slick-carousel .slick-prev {
  left: 0;
}

@media (min-width: 768px) {
  .widget-testimonials.style4 .slick-carousel .slick-prev {
    left: 15px;
  }
}

.widget-testimonials.style4 .slick-carousel .slick-next {
  left: 65px;
  right: inherit;
}

@media (min-width: 768px) {
  .widget-testimonials.style4 .slick-carousel .slick-next {
    left: 80px;
  }
}

@media (min-width: 1200px) {
  .widget-testimonials.style4 .slick-carousel .slick-next {
    left: 90px;
  }
}

.testimonials-item {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: relative;
  padding: 0.9375rem;
  background: #fff;
  border-radius: 8px;
  border: 1px solid transparent;
}

@media (min-width: 1200px) {
  .testimonials-item {
    padding: 1.875rem;
    border-radius: 12px;
  }
}

.testimonials-item .avarta {
  overflow: hidden;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  background-color: #fff;
  position: relative;
}

.testimonials-item .name-client {
  font-weight: 600;
  font-size: 0.875rem;
  margin: 0 0 2px;
}

.testimonials-item .description {
  margin-top: 12px;
  font-size: 14px;
  line-height: 28px;
  font-weight: 600;
  color: var(--homez-link-color);
}

@media (min-width: 1200px) {
  .testimonials-item .description {
    margin-top: 18px;
  }
}

.testimonials-item .star {
  margin-top: 7px;
}

.testimonials-item .job {
  color: #717171;
  font-size: 13px;
}

.testimonials-item .info-testimonials {
  padding-left: 20px;
}

.testimonials-item .title {
  font-size: 1rem;
  margin: 0;
}

.testimonials-item .icon-top {
  color: rgba(235, 103, 83, 0.15);
}

.testimonials-item .icon-top path {
  opacity: 1;
  filter: alpha(opacity=100);
}

.testimonials-item .inner-bottom {
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid #E9E9E9;
}

.testimonials-item2 .description {
  line-height: 2;
  font-size: 17px;
  font-weight: 600;
  color: var(--homez-link-color);
  max-width: 800px;
  margin: 0 auto 20px;
}

@media (min-width: 1200px) {
  .testimonials-item2 .description {
    margin-bottom: 35px;
    font-size: 22px;
  }
}

.testimonials-item2 .name-client {
  font-size: 15px;
  margin: 0;
}

.testimonials-item2 .job {
  margin-top: 5px;
  font-size: 13px;
}

.wrapper-testimonial-thumbnail {
  max-width: 500px;
  margin: 15px auto 0;
}

@media (min-width: 1200px) {
  .wrapper-testimonial-thumbnail {
    margin-top: 1.875rem;
  }
  .wrapper-testimonial-thumbnail .slick-carousel {
    margin-left: -20px;
    margin-right: -20px;
  }
  .wrapper-testimonial-thumbnail .slick-carousel .slick-slide {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.wrapper-testimonial-thumbnail .avarta {
  cursor: pointer;
  max-width: 100%;
  margin: auto;
  width: 60px;
  height: 60px;
  overflow: hidden;
  border-radius: 50%;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0.2;
  filter: alpha(opacity=20);
}

@media (min-width: 1200px) {
  .wrapper-testimonial-thumbnail .avarta {
    width: 70px;
    height: 70px;
  }
}

.wrapper-testimonial-thumbnail .avarta img {
  border-radius: 50%;
}

.wrapper-testimonial-thumbnail .avarta:hover, .wrapper-testimonial-thumbnail .avarta:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.wrapper-testimonial-thumbnail .slick-current .avarta {
  opacity: 1;
  filter: alpha(opacity=100);
}

.testimonials-item3 {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  overflow: hidden;
  padding: 20px;
}

@media (min-width: 1200px) {
  .testimonials-item3 {
    padding: 60px 60px 50px;
    border-radius: 12px;
  }
}

.testimonials-item3:before {
  content: '';
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 100%;
  height: 4px;
  background: var(--homez-theme-color);
  position: absolute;
  bottom: 0;
  left: 0;
  -webkit-transform: scale(0, 1);
  -ms-transform: scale(0, 1);
  -o-transform: scale(0, 1);
  transform: scale(0, 1);
}

@media (min-width: 1200px) {
  .testimonials-item3:before {
    width: 100%;
    height: 6px;
  }
}

.testimonials-item3 .icon-top {
  opacity: 0.35;
  filter: alpha(opacity=35);
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

@media (min-width: 1200px) {
  .testimonials-item3 .icon-top {
    top: 40px;
    right: 40px;
  }
}

.testimonials-item3 .wrapper-avarta {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
}

.testimonials-item3 .flex-shrink-0 + .info-testimonials {
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .testimonials-item3 .flex-shrink-0 + .info-testimonials {
    padding-left: 20px;
  }
}

.testimonials-item3 .title {
  color: var(--homez-theme-color);
  font-size: 25px;
  margin: 0 0 20px;
}

.testimonials-item3 .name-client {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
}

.testimonials-item3 .job {
  margin-top: 5px;
  color: #717171;
  font-size: 13px;
}

.testimonials-item3 .description {
  font-size: 15px;
  margin-top: 15px;
}

@media (min-width: 1200px) {
  .testimonials-item3 .description {
    margin-top: 35px;
  }
}

.testimonials-item3:hover {
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

.testimonials-item3:hover:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.testimonials-item4 .icon-top {
  opacity: 0.35;
  filter: alpha(opacity=35);
}

.testimonials-item4 .description {
  font-size: 17px;
  margin-bottom: 15px;
  font-weight: 600;
}

@media (min-width: 1200px) {
  .testimonials-item4 .description {
    font-size: 20px;
    margin-bottom: 25px;
  }
}

.testimonials-item4 .top-inner {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .testimonials-item4 .top-inner {
    margin-bottom: 25px;
  }
}

.testimonials-item4 .title {
  font-size: 15px;
  margin: 0;
}

.testimonials-item4 .name-client {
  font-size: 14px;
  margin: 0;
}

.testimonials-item4 .job {
  margin-top: 5px;
  color: #717171;
  font-size: 13px;
}

.wrapper-testimonial-thumbnail4 {
  max-width: 1170px;
  margin-right: auto;
  margin-left: auto;
}

.slick-center .item-testimonials4 {
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

.item-testimonials4 {
  cursor: pointer;
  border: 1px solid #E9E9E9;
  background: #fff;
  border-radius: 100px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  padding: 10px;
  margin-bottom: 15px;
}

.item-testimonials4 .wrapper-avarta {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-testimonials4 .inner-right {
  padding-left: 20px;
}

.item-testimonials4 .name-client {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 5px;
}

.item-testimonials4 .job {
  font-size: 14px;
}

.widget-nav-menu .menu li {
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .widget-nav-menu .menu li {
    margin-bottom: 12px;
  }
}

.widget-nav-menu .menu li:hover > a, .widget-nav-menu .menu li.current-cat-parent > a, .widget-nav-menu .menu li.current-cat > a {
  color: var(--homez-link_hover_color);
}

.widget-nav-menu .menu li:last-child {
  margin: 0;
}

.widget-nav-menu.st_line .menu li {
  margin-bottom: 0;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
  padding-right: 10px;
  position: relative;
}

.widget-nav-menu.st_line .menu li:before {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translate(50%, -50%);
  -ms-transform: translate(50%, -50%);
  -o-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
  background: #BEBDBD;
  border-radius: 50%;
  width: 2px;
  height: 2px;
  display: block;
}

.widget-nav-menu.st_line .menu li:last-child {
  margin-right: 0;
  padding-right: 0;
}

.widget-nav-menu.st_line .menu li:last-child:before {
  display: none;
}

form.mc4wp-form {
  background: #fff;
  padding: 8px;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  form.mc4wp-form {
    border-radius: 12px;
  }
}

form.mc4wp-form [type="email"] {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent;
  border-color: transparent;
}

form.mc4wp-form [type="email"]::-webkit-input-placeholder {
  /* Edge */
  opacity: 1;
  filter: alpha(opacity=100);
}

form.mc4wp-form [type="email"]:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  opacity: 1;
  filter: alpha(opacity=100);
}

form.mc4wp-form [type="email"]::placeholder {
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget-mailchimp.style1 i {
  display: none;
}

.widget-mailchimp.style2 [type="email"] {
  height: 40px;
  padding: 5px 20px;
}

.widget-mailchimp.style2 .btn-submit {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  text-align: center;
  font-size: 1rem;
  line-height: 38px;
}

.widget-mailchimp.style2 .btn-submit .text {
  display: none;
}

.widget-brand {
  text-align: center;
}

.widget-brand .item img {
  max-width: 80%;
  display: inline-block;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0.7;
  filter: alpha(opacity=70);
}

.widget-brand .item img:hover, .widget-brand .item img:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget-brand .slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  -ms-align-items: center;
}

.widget-brand.st2 .slick-list {
  padding-top: 10px;
}

.widget-brand.st2 .item img {
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget-brand.st2 .item:hover img, .widget-brand.st2 .item:focus img {
  -webkit-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  -o-transform: translateY(-10px);
  transform: translateY(-10px);
}

.text-theme {
  color: var(--homez-theme-color) !important;
}

.text-link {
  color: var(--homez-link-color) !important;
}

.text-hover-link {
  color: var(--homez-link_hover_color) !important;
}

.text-white {
  color: #fff !important;
}

.text-white-70 {
  color: rgba(255, 255, 255, 0.7) !important;
}

.bg-theme {
  background: var(--homez-theme-color);
}

.border-theme {
  border-color: var(--homez-theme-color);
}

.radius-3x {
  border-radius: 3px !important;
}

.radius-50 {
  border-radius: 50px !important;
}

.deleted_wpb_single_image {
  position: relative;
  overflow: hidden;
}

.deleted_wpb_single_image:before {
  position: absolute;
  z-index: 2;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  background: var(--homez-theme-color);
  opacity: 0;
  filter: alpha(opacity=0);
  top: 0;
  left: 0;
}

.deleted_wpb_single_image:after {
  position: absolute;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  content: '';
  top: 1.875rem;
  left: 1.875rem;
  right: 1.875rem;
  bottom: 1.875rem;
  border: 1px solid #fff;
  z-index: 3;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.deleted_wpb_single_image:hover:before {
  opacity: 0.5;
  filter: alpha(opacity=50);
}

.deleted_wpb_single_image:hover:after {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.widget-team .team-item {
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  margin: 0 0 15px;
}

.widget-team .team-item:before {
  content: '';
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: rgba(39, 42, 51, 0.7);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 100%;
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
}

.widget-team .team-item img {
  -webkit-transition: all 0.5s ease-in-out 0s;
  -o-transition: all 0.5s ease-in-out 0s;
  transition: all 0.5s ease-in-out 0s;
}

.widget-team .name-team {
  font-size: 17px;
  font-weight: 500;
  margin: 0 0 5px;
}

.widget-team .social {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 1;
  list-style: none;
  margin: 0;
  top: 50%;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  visibility: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  text-align: center;
}

.widget-team .social li {
  display: inline-block;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .widget-team .social li {
    margin-right: 20px;
  }
}

.widget-team .social li:last-child {
  margin-right: 0;
}

.widget-team .social li a {
  color: #fff !important;
}

.widget-team:hover .team-item img {
  -webkit-transform: scale(1.15) rotate(-1deg);
  -moz-transform: scale(1.15) rotate(-1deg);
  -ms-transform: scale(1.15) rotate(-1deg);
  -o-transform: scale(1.15) rotate(-1deg);
  transform: scale(1.15) rotate(-1deg);
}

.widget-team:hover .team-item:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget-team:hover .social {
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.widget_apus_vertical_menu {
  border-left: 4px solid #2e2d2d;
}

.widget_apus_vertical_menu .widget-title {
  font-size: 16px;
  font-weight: normal;
  margin: 0 0 10px;
  padding: 15px 1.875rem 0;
}

.widget_apus_vertical_menu .apus-vertical-menu {
  border: none;
}

.widget_apus_vertical_menu .apus-vertical-menu > li {
  margin-left: -4px;
}

.widget_apus_vertical_menu .apus-vertical-menu > li > a {
  border-left: 4px solid transparent;
  font-size: 16px;
  padding: 0 1.875rem;
}

.widget_apus_vertical_menu .apus-vertical-menu > li.active > a, .widget_apus_vertical_menu .apus-vertical-menu > li:hover > a {
  border-color: var(--homez-theme-color);
}

.nav.tabs-product {
  border: none;
  margin: 0 0 1rem;
  padding: 10px 0;
  background: #fff;
}

@media (min-width: 1200px) {
  .nav.tabs-product {
    margin-bottom: 2rem;
    padding: 0;
  }
}

.nav.tabs-product > li {
  display: inline-block;
  float: none;
  margin-bottom: 5px;
  margin-right: 0.9375rem;
}

@media (min-width: 1200px) {
  .nav.tabs-product > li {
    margin-right: 3.125rem;
  }
}

.nav.tabs-product > li > a {
  border: none !important;
  text-transform: capitalize;
  font-weight: 500;
  line-height: 1;
  color: var(--homez-text-color);
  display: inline-block;
  background: transparent;
  position: relative;
  padding: 0 0 10px;
}

@media (min-width: 1200px) {
  .nav.tabs-product > li > a {
    font-size: 17px;
  }
}

.nav.tabs-product > li > a:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--homez-link-color);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.nav.tabs-product > li > a.active {
  color: var(--homez-link-color);
}

.nav.tabs-product > li > a.active:before {
  width: 100%;
}

.updow:hover .top-img img,
.updow:hover .img img,
.updow:hover .image-wrapper img {
  -webkit-animation: updow 0.8s ease-in-out 0s infinite;
  animation: updow 0.8s ease-in-out 0s infinite;
}

.updow-infinite img {
  -webkit-animation: updow 1s ease-in-out 0s infinite;
  animation: updow 1s ease-in-out 0s infinite;
}

@-webkit-keyframes updow {
  50% {
    -webkit-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  0%, 100% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);
  }
}

@keyframes updow {
  50% {
    -webkit-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  0%, 100% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);
  }
}

@-webkit-keyframes fadeleft {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(-15px, 0);
    -ms-transform: translate(-15px, 0);
    -o-transform: translate(-15px, 0);
    transform: translate(-15px, 0);
  }
}

@keyframes fadeleft {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(-15px, 0);
    -ms-transform: translate(-15px, 0);
    -o-transform: translate(-15px, 0);
    transform: translate(-15px, 0);
  }
}

@-webkit-keyframes faderight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(15px, 0);
    -ms-transform: translate(15px, 0);
    -o-transform: translate(15px, 0);
    transform: translate(15px, 0);
  }
  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes faderight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(15px, 0);
    -ms-transform: translate(15px, 0);
    -o-transform: translate(15px, 0);
    transform: translate(15px, 0);
  }
  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

.mb0 {
  margin-bottom: 0px !important;
}

.width-full {
  width: 100% !important;
}

.custom-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.custom-menu li {
  padding-left: 22px;
  margin-bottom: 18px;
  position: relative;
  line-height: 1.4;
}

.custom-menu li:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: #221f1f;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.custom-menu li:last-child {
  margin: 0;
}

.custom-menu li i {
  margin-right: 15px;
  font-size: 18px;
}

@media (min-width: 1200px) {
  .custom-menu li i {
    font-size: 23px;
  }
}

.custom-menu li a {
  color: var(--homez-text-color);
}

.custom-menu li a:focus, .custom-menu li a:hover {
  color: var(--homez-link-color);
}

.custom-menu li:hover:before, .custom-menu li.active:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.banner-item {
  min-height: 100px;
  position: relative;
  z-index: 0;
  overflow: hidden;
  border-radius: 8px;
  text-align: center;
  display: block;
}

.banner-item .title {
  position: absolute;
  z-index: 2;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 1.25rem;
  font-size: 1.125rem;
  color: #fff;
}

.banner-item-link {
  display: block;
  position: relative;
}

.banner-item-link:before {
  content: '';
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(39, 42, 51, 0.3);
}

.banner-item-link img {
  -webkit-transition: all 0.6s ease-in-out 0s;
  -o-transition: all 0.6s ease-in-out 0s;
  transition: all 0.6s ease-in-out 0s;
}

.banner-item-link:hover img, .banner-item-link:focus img {
  -webkit-transform: scale(1.1) rotate(-1deg);
  -moz-transform: scale(1.1) rotate(-1deg);
  -ms-transform: scale(1.1) rotate(-1deg);
  -o-transform: scale(1.1) rotate(-1deg);
  transform: scale(1.1) rotate(-1deg);
}

.widget-banner-account {
  text-align: center;
  background-color: #0d6efd;
  padding: 30px 15px;
  min-height: 300px;
  display: -webkit-flex;
  /* Safari */
  -webkit-align-items: center;
  /* Safari 7.0+ */
  display: flex;
  align-items: center;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
  justify-content: center;
  -webkit-justify-content: center;
  /* Safari 6.1+ */
}

@media (min-width: 1200px) {
  .widget-banner-account {
    padding: 50px 30px;
    min-height: 470px;
  }
}

.widget-banner-account .title-account {
  color: #fff;
  margin: 0;
  font-size: 30px;
  margin: 0 0 12px;
}

@media (min-width: 1200px) {
  .widget-banner-account .title-account {
    font-size: 50px;
  }
}

.widget-banner-account .description {
  color: #fff;
  margin: 0 0 30px;
}

/*-----------------------------*\
        Widget video
\*-----------------------------*/
a.popup-video {
  border-radius: 8px;
  background: #fff;
  padding: 20px;
  -webkit-box-shadow: 0 0 50px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 0 50px 0 rgba(24, 26, 32, 0.07);
}

@media (min-width: 1200px) {
  a.popup-video {
    border-radius: 12px;
  }
}

a.popup-video .title-video {
  margin: 0 0 0 15px;
  font-size: 14px;
}

a.popup-video .popup-video-inner {
  font-size: 12px;
  width: 40px;
  height: 40px;
  margin: 5px;
  border-radius: 50%;
  background: var(--homez-link-color);
  color: #fff;
  outline-offset: 7px;
  outline: 1px solid var(--homez-link-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

@media (min-width: 1200px) {
  a.popup-video .popup-video-inner {
    width: 47px;
    height: 47px;
    margin: 8px;
  }
}

a.popup-video:hover {
  text-decoration: none;
}

a.popup-video:hover .popup-video-inner {
  outline-offset: 10px;
}

a.popup-video.style2 {
  padding: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  flex-direction: row-reverse;
  background: transparent;
}

a.popup-video.style2 .title-video {
  margin: 0 15px 0 0;
}

a.popup-video.style2 .popup-video-inner {
  outline: 1px solid #fff;
  color: var(--homez-link-color);
  background: #fff;
}

.item-step {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  background: #fff;
  padding: 30px 20px 20px;
}

@media (min-width: 1200px) {
  .item-step {
    border-radius: 12px;
    padding: 45px 1.875rem 1.875rem;
  }
}

.item-step .step-box-image {
  font-size: 40px;
  line-height: 1;
  color: var(--homez-theme-color);
  position: absolute;
  top: 10px;
  right: 10px;
}

@media (min-width: 1200px) {
  .item-step .step-box-image {
    top: 20px;
    right: 20px;
  }
}

.item-step .number {
  font-size: 22px;
  font-weight: 600;
  color: var(--homez-theme-color);
}

@media (min-width: 1200px) {
  .item-step .number {
    font-size: 28px;
  }
}

.item-step .description {
  color: #717171;
  font-size: 13px;
  margin-top: 10px;
}

.item-step .title {
  font-size: 0.875rem;
  margin: 0;
}

.item-step:hover {
  -webkit-box-shadow: 0 5px 10px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 5px 10px 0 rgba(24, 26, 32, 0.07);
}

.widget-address-box.style2 .item {
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .widget-address-box.style2 .item {
    margin-bottom: 1.875rem;
  }
}

.widget-address-box.style2 .item:last-child {
  margin-bottom: 0;
}

@media (max-width: 767px) {
  .widget-address-box.style1 .item {
    margin-bottom: 0.9375rem;
  }
}

form.wpcf7-form label {
  font-weight: 600;
  color: var(--homez-link-color);
  margin: 0 0 5px;
}

form.wpcf7-form p,
form.wpcf7-form .form-control {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  form.wpcf7-form p,
  form.wpcf7-form .form-control {
    margin-bottom: 20px;
  }
}

form.wpcf7-form textarea.form-control {
  height: 180px;
}

@media (min-width: 1200px) {
  form.wpcf7-form textarea.form-control {
    height: 290px;
    margin-bottom: 1.875rem;
  }
}

form.wpcf7-form p:last-child {
  margin-bottom: 0;
}

form.wpcf7-form .no-radius .form-control,
form.wpcf7-form .no-radius .btn {
  border-radius: 0;
}

form.wpcf7-form .get-touch textarea.form-control {
  height: 100px;
}

@media (min-width: 1200px) {
  form.wpcf7-form .get-touch textarea.form-control {
    height: 150px;
  }
}

form.wpcf7-form .title {
  margin: 0 0 5px;
  font-size: 22px;
}

@media (min-width: 1200px) {
  form.wpcf7-form .title {
    font-size: 30px;
  }
}

form.wpcf7-form .des {
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  form.wpcf7-form .des {
    margin-bottom: 25px;
  }
}

.list-circle {
  list-style: none;
  padding: 0;
}

.list-circle li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 20px;
}

@media (min-width: 1200px) {
  .list-circle li {
    margin-bottom: 17px;
  }
}

.list-circle li:before {
  content: '';
  background-color: #696969;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  left: 0;
}

.list-circle.column2 {
  max-width: 600px;
}

.list-circle-check {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 15px;
  font-weight: 600;
}

.list-circle-check li {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .list-circle-check li {
    margin-bottom: 25px;
  }
}

.list-circle-check li:last-child {
  margin-bottom: 0;
}

.list-circle-check li:before {
  font-size: 8px;
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  display: inline-block;
  vertical-align: text-bottom;
  margin-right: 10px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  background-color: #181A20;
  text-align: center;
  border-radius: 50%;
  color: #fff;
}

@media (min-width: 1200px) {
  .list-circle-check li:before {
    margin-right: 15px;
    width: 25px;
    height: 25px;
    line-height: 25px;
  }
}

.list-border-check {
  list-style: none;
  padding: 0;
}

.list-border-check li {
  margin-bottom: 12px;
  padding-left: 28px;
  position: relative;
}

@media (min-width: 1200px) {
  .list-border-check li {
    margin-bottom: 20px;
  }
}

.list-border-check li:before {
  position: absolute;
  top: 4px;
  left: 0;
  font-size: 8px;
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  display: inline-block;
  vertical-align: text-bottom;
  margin-right: 10px;
  background-color: rgba(91, 187, 123, 0.15);
  width: 18px;
  height: 18px;
  border-radius: 50%;
  line-height: 18px;
  text-align: center;
  color: #5BBB7B;
}

.list-check {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-check li {
  margin-bottom: 12px;
  padding-left: 30px;
  position: relative;
}

@media (min-width: 1200px) {
  .list-check li {
    margin-bottom: 20px;
  }
}

.list-check li:before {
  position: absolute;
  top: 2px;
  left: 0;
  font-size: 8px;
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  display: inline-block;
  vertical-align: text-bottom;
  margin-right: 10px;
  background-color: var(--homez-link-color);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  line-height: 20px;
  text-align: center;
  color: #fff;
}

.fw-500 {
  font-weight: 500;
}

.fs-20 {
  font-size: 20px !important;
}

.max-930 {
  max-width: 930px;
  margin-left: auto;
  margin-right: auto;
}

.max-650 {
  max-width: 650px;
}

.radius-sm {
  border-radius: 6px;
}

.elementor-icon-list-text,
.elementor-icon-list-icon i {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.nowrap {
  white-space: nowrap !important;
}

.align-inherit .elementor-counter .elementor-counter-title {
  text-align: inherit;
}

.align-inherit .elementor-counter .elementor-counter-number-prefix,
.align-inherit .elementor-counter .elementor-counter-number-suffix {
  flex-grow: 0;
}

@media (min-width: 1200px) {
  .fullscreen .slick-list {
    overflow: visible;
  }
}

.border-radius-base {
  border-radius: 8px;
}

.elementor-accordion .elementor-tab-content {
  border: 0 !important;
}

.elementor-accordion .elementor-tab-content p:last-child {
  margin-bottom: 0;
}

.elementor-accordion .elementor-accordion-item {
  margin-bottom: 10px;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .elementor-accordion .elementor-accordion-item {
    border-radius: 12px;
  }
}

.elementor-accordion .elementor-accordion-item:last-child {
  margin-bottom: 0;
}

.elementor-accordion .elementor-accordion-item + .elementor-accordion-item {
  border-top-width: 1px !important;
  border-top-style: solid !important;
}

.elementor-accordion .elementor-tab-title a {
  text-decoration: none;
}

/* 11. widgets */
/*------------------------------------*\
    Widget
\*------------------------------------*/
.widget label {
  font-weight: 400;
}

.widget .widget_sp_image-image-link {
  display: block;
  overflow: hidden;
  position: relative;
}

.widget.widget_text img {
  margin: 15px 0;
  height: auto;
}

.widget.widget_recent_comments ul li {
  background: none;
}

.widget.widget_recent_reviews ul.product_list_widget {
  list-style: none;
}

.widget.widget_recent_reviews ul.product_list_widget li {
  padding: 15px;
  overflow: hidden;
}

.widget.widget_recent_reviews ul.product_list_widget li a img {
  float: left;
  margin-right: 10px;
}

.widget.widget_product_search .woocommerce-product-search {
  padding: 20px 15px;
}

.widget.widget_product_search .woocommerce-product-search label.screen-reader-text {
  display: none;
}

.widget.yith-woocompare-widget .products-list {
  padding-top: 20px;
  padding-bottom: 20px;
}

.widget.yith-woocompare-widget a.clear-all {
  margin-bottom: 20px;
  margin-left: 15px;
}

.widget.yith-woocompare-widget a.compare {
  margin-bottom: 20px;
  margin-right: 15px;
}

.widget.widget_shopping_cart .widget_shopping_cart_content {
  padding: 20px 15px;
  overflow: hidden;
}

.widget.widget_recent_entries ul li a {
  display: block;
}

.widget.widget_calendar table {
  margin: 0;
  width: 100%;
}

.calendar_wrap caption {
  background: #212121;
  color: #fff;
  padding: 5px;
}

.calendar_wrap td, .calendar_wrap th {
  text-align: center;
}

.calendar_wrap tfoot {
  display: none;
}

.calendar_wrap #today {
  font-weight: normal;
  color: var(--homez-theme-color);
}

.form-contact .title {
  font-size: 17px;
  margin: 11.5px 0 28px;
}

.form-contact input:not(.btn),
.form-contact textarea {
  padding: 10px 30px;
  width: 100%;
  color: var(--homez-text-color);
}

.form-contact .contant-inner > * {
  margin: 0 0 20px;
}

.contact-topbar > * {
  margin-right: 1.875rem;
}

.contact-topbar > *:last-child {
  margin: 0;
}

.contact-topbar > * i {
  margin-right: 6px;
}

.widget_pages ul,
.widget_nav_menu ul,
.widget_meta ul,
.widget_archive ul,
.widget_recent_entries ul,
.widget_categories ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.widget_pages ul ul,
.widget_nav_menu ul ul,
.widget_meta ul ul,
.widget_archive ul ul,
.widget_recent_entries ul ul,
.widget_categories ul ul {
  padding-left: 20px;
  margin-top: 15px;
}

.widget_pages ul li,
.widget_nav_menu ul li,
.widget_meta ul li,
.widget_archive ul li,
.widget_recent_entries ul li,
.widget_categories ul li {
  list-style: none;
  margin-bottom: 15px;
}

.widget_pages ul li:last-child,
.widget_nav_menu ul li:last-child,
.widget_meta ul li:last-child,
.widget_archive ul li:last-child,
.widget_recent_entries ul li:last-child,
.widget_categories ul li:last-child {
  margin-bottom: 0;
}

.widget_pages ul li a,
.widget_nav_menu ul li a,
.widget_meta ul li a,
.widget_archive ul li a,
.widget_recent_entries ul li a,
.widget_categories ul li a {
  color: var(--homez-text-color);
}

.widget_pages ul li a:before,
.widget_nav_menu ul li a:before,
.widget_meta ul li a:before,
.widget_archive ul li a:before,
.widget_recent_entries ul li a:before,
.widget_categories ul li a:before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #CCCCCC;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.4rem;
}

@media (min-width: 1200px) {
  .widget_pages ul li a:before,
  .widget_nav_menu ul li a:before,
  .widget_meta ul li a:before,
  .widget_archive ul li a:before,
  .widget_recent_entries ul li a:before,
  .widget_categories ul li a:before {
    margin-right: 0.625rem;
  }
}

.widget_pages ul li:hover > a,
.widget_pages ul li.current-cat-parent > a,
.widget_pages ul li.current-cat > a,
.widget_nav_menu ul li:hover > a,
.widget_nav_menu ul li.current-cat-parent > a,
.widget_nav_menu ul li.current-cat > a,
.widget_meta ul li:hover > a,
.widget_meta ul li.current-cat-parent > a,
.widget_meta ul li.current-cat > a,
.widget_archive ul li:hover > a,
.widget_archive ul li.current-cat-parent > a,
.widget_archive ul li.current-cat > a,
.widget_recent_entries ul li:hover > a,
.widget_recent_entries ul li.current-cat-parent > a,
.widget_recent_entries ul li.current-cat > a,
.widget_categories ul li:hover > a,
.widget_categories ul li.current-cat-parent > a,
.widget_categories ul li.current-cat > a {
  color: var(--homez-theme-color);
}

.widget_pages ul li:hover > a:before,
.widget_pages ul li.current-cat-parent > a:before,
.widget_pages ul li.current-cat > a:before,
.widget_nav_menu ul li:hover > a:before,
.widget_nav_menu ul li.current-cat-parent > a:before,
.widget_nav_menu ul li.current-cat > a:before,
.widget_meta ul li:hover > a:before,
.widget_meta ul li.current-cat-parent > a:before,
.widget_meta ul li.current-cat > a:before,
.widget_archive ul li:hover > a:before,
.widget_archive ul li.current-cat-parent > a:before,
.widget_archive ul li.current-cat > a:before,
.widget_recent_entries ul li:hover > a:before,
.widget_recent_entries ul li.current-cat-parent > a:before,
.widget_recent_entries ul li.current-cat > a:before,
.widget_categories ul li:hover > a:before,
.widget_categories ul li.current-cat-parent > a:before,
.widget_categories ul li.current-cat > a:before {
  background-color: var(--homez-theme-color);
}

ul#recentcomments,
ol.wp-block-latest-comments {
  padding: 0;
}

ul#recentcomments li,
ol.wp-block-latest-comments li {
  line-height: 1.9;
}

.special .post-info {
  position: relative;
}

.special .post-info:before {
  border-width: 14px 20px;
  border-style: solid;
  border-color: #f4f4f4 transparent transparent;
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -10px;
  z-index: 2;
}

.special .special-items > div:nth-child(2n) .post-info {
  position: relative;
}

.special .special-items > div:nth-child(2n) .post-info:before {
  border-color: transparent transparent #f4f4f4;
  top: inherit;
  bottom: 100%;
}

/*------------------------------------*\
    Widget Contact Us
\*------------------------------------*/
.contact {
  margin: 0;
  padding: 0;
}

.contact::after {
  display: block;
  clear: both;
  content: "";
}

.contact dt {
  float: left;
  width: 30px;
  height: auto;
}

.contact dd {
  overflow: hidden;
  margin-bottom: 5px;
}

.contact .contact-icon {
  display: block;
  text-align: center;
  background: var(--homez-theme-color);
  float: left;
  width: 22px;
  height: 22px;
  border-radius: 2px;
}

.contact .contact-icon .fa {
  color: #fff;
  font-size: 0.875rem;
  margin: 0 0 0 4px;
}

/*------------------------------------*\
    Widget Mailchip
\*------------------------------------*/
.mail-form .input-group {
  width: 100%;
  margin: 0 0 10px;
}

/*------------------------------------*\
    Widget Sidebar
\*------------------------------------*/
.apus-sidebar select, .apus-sidebar table {
  width: 100%;
}

.apus-sidebar .post-widget .blog-title, .apus-sidebar .post-widget h6 {
  margin: 0 0 5px;
  line-height: 1.4;
  font-weight: 400;
  height: 40px;
  overflow: hidden;
  font-family: var(--bs-font-sans-serif);
}

/*------------------------------------*\
    search
\*------------------------------------*/
.apus-search-form .select-category {
  display: inline-block;
  float: left;
  overflow: hidden;
  position: relative;
  min-width: 200px;
  padding-right: 12px;
  outline: none !important;
}

.apus-search-form .select-category:after {
  content: '';
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
  background: #dddddd;
  width: 1px;
  height: 20px;
}

.apus-search-form .select-category .dropdown_product_cat {
  border: 0;
  outline: none !important;
  width: calc(100% + 38px);
  height: 48px;
  padding: 0 20px;
}

.apus-search-form .title-top-search {
  font-size: 24px;
  color: var(--homez-link-color);
}

.apus-search-form .close-search-fix {
  font-size: 24px;
  color: #dc3545;
  cursor: pointer;
}

.apus-search-form .close-search-fix:hover, .apus-search-form .close-search-fix:active {
  color: #bd2130;
}

.apus-search-form .select2-container .select2-selection--single {
  background: #fff;
  height: 48px;
  margin: 0;
  font-size: 16px;
  color: var(--homez-link-color);
  outline: none !important;
}

.apus-search-form .select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 20px;
}

.apus-search-form form {
  border: 1px solid #E9E9E9;
  display: table;
  width: 100%;
}

.apus-search-form form .main-search,
.apus-search-form form .btn,
.apus-search-form form > .select-category {
  display: table-cell !important;
  vertical-align: middle;
  float: none !important;
}

.apus-search-form form .btn {
  height: 50px;
  line-height: 1;
  margin-top: -1px;
  margin-bottom: -1px;
  margin-right: -1px;
}

.apus-search-form form .btn i {
  font-size: 18px;
}

.apus-search-form form .btn i + span {
  margin-left: 5px;
}

.apus-search-form form .btn.st_small {
  padding-left: 15px;
  padding-right: 15px;
}

.apus-search-form form .form-control {
  border: none;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 0;
}

.apus-search-form form .form-control {
  height: 48px;
}

.apus-search-form form .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #d1d1d1;
}

.apus-search-form form .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #d1d1d1;
}

.apus-search-form form .form-control:-ms-input-placeholder {
  /* IE 10+ */
  color: #d1d1d1;
}

.apus-search-form form .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: #d1d1d1;
}

.apus-search-form form .form-control:focus {
  color: var(--homez-link-color);
}

.apus-search-form .hidden-search {
  cursor: pointer;
  float: right;
  font-size: 35px;
  line-height: 1.4;
  color: #dc3545;
  display: inline-block;
  margin-left: 30px;
}

.apus-search-form .main-search {
  width: 100%;
  position: relative;
}

.apus-search-form .main-search .autocomplete-list {
  text-align: left;
  margin-top: 1px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 8;
  background: #fff;
  -webkit-box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.15);
  max-height: 350px;
  overflow: auto;
}

.apus-search-form div.twitter-typeahead {
  width: 100%;
  position: relative;
}

.apus-search-form div.twitter-typeahead span.twitter-typeahead {
  vertical-align: top;
  width: 100%;
}

.apus-search-form div.twitter-typeahead:before {
  content: '';
  position: absolute;
  top: 13px;
  right: 10px;
  width: 18px;
  height: 100%;
  background: url(../images/loading-quick.gif) no-repeat scroll 0 0/18px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: 9;
}

.apus-search-form div.twitter-typeahead.loading:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.apus-search-form .tt-menu {
  background: #fff;
  width: 100%;
  padding: 0;
  margin-top: 1px;
}

.apus-search-form .tt-menu > * {
  position: relative;
  z-index: 9;
}

.apus-search-form .tt-menu a.media {
  display: block;
  margin: 0;
  padding: 12px;
}

.apus-search-form .tt-menu a.media img {
  max-width: 60px;
}

.apus-search-form .tt-menu h4 {
  font-size: 14px;
  margin: 0;
}

.apus-search-form .tt-menu h4 strong {
  font-weight: normal;
  color: var(--homez-theme-color);
}

.apus-search-form .tt-menu .price {
  font-size: 13px;
  margin: 0;
}

.apus-search-form .tt-menu .tt-dataset-search > *:first-child {
  display: none;
}

.apus-search-form.style2 form {
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  position: relative;
  padding-right: 40px;
}

.apus-search-form.style2 form .form-control {
  border: none !important;
  height: 45px;
  background-color: transparent;
  color: #fff;
}

.apus-search-form.style2 form .form-control:focus {
  color: #fff;
}

.apus-search-form.style2 form .btn {
  line-height: 32px;
  height: 35px;
  font-size: 16px;
  position: absolute;
  top: 6px;
  padding: 0 9px;
  right: 5px;
  border-radius: 50% !important;
}

.apus-search-form.style2 form .btn i {
  font-size: 16px;
}

.apus-search-form .autocomplete-list {
  padding: 15px;
  border-radius: 2px;
}

@media (min-width: 1200px) {
  .apus-search-form .autocomplete-list {
    padding: 20px;
  }
}

.apus-search-form .autocomplete-list-item {
  padding: 0 0 10px;
  margin: 0 0 10px;
  border-bottom: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  .apus-search-form .autocomplete-list-item {
    padding: 0 0 15px;
    margin: 0 0 15px;
  }
}

.apus-search-form .autocomplete-list-item:last-child {
  border: none;
  padding: 0;
  margin: 0;
}

.apus-search-form .autocomplete-list-item .autocompleate-media {
  display: block;
}

.apus-search-form .autocomplete-list-item .autocompleate-media:hover .name-product {
  color: var(--homez-link-color);
}

.apus-search-form .autocomplete-list-item img {
  width: 60px;
  max-width: none;
}

.apus-search-form .autocomplete-list-item .price {
  color: var(--homez-text-color);
}

.apus-search-form .autocomplete-list-item .name-product {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin: 0 0 3px;
  font-size: 15px;
  font-family: var(--homez-main-font);
  font-weight: normal;
  color: var(--homez-text-color);
  text-transform: capitalize;
}

.apus-search-form .autocomplete-list-item .name-product strong {
  color: #dc3545;
}

.apus-search-nocategory {
  background: #f0f2f9;
  border-radius: 50px;
}

.apus-search-nocategory .form-control {
  background: #f0f2f9;
  border-color: #f0f2f9;
  color: #999591;
  border: none;
  max-width: 185px;
  font-size: 12px;
}

.apus-search-nocategory .btn {
  padding-left: 12px;
  padding-right: 12px;
  background: transparent;
  color: var(--homez-link-color);
  font-size: 16px;
  border-radius: 50% !important;
  border: none;
}

.apus-search-nocategory .btn:hover, .apus-search-nocategory .btn:active {
  color: #fff;
  background: var(--homez-theme-color);
}

.apus-search-nocategory {
  background: #f0f2f9;
  border-radius: 50px;
}

.apus-search-nocategory .form-control {
  background: #f0f2f9;
  border-color: #f0f2f9;
  color: #999591;
  border: none;
  max-width: 185px;
  font-size: 12px;
}

.apus-search-nocategory .btn {
  padding-left: 12px;
  padding-right: 12px;
  background: transparent;
  color: var(--homez-link-color);
  font-size: 16px;
  border-radius: 50% !important;
  border: none;
}

.apus-search-nocategory .btn:hover, .apus-search-nocategory .btn:active {
  color: #fff;
  background: var(--homez-theme-color);
}

.widget_search form {
  position: relative;
}

@media (min-width: 1200px) {
  .widget_search form .form-control {
    height: 55px;
    border-radius: 8px;
  }
}

.widget_search form .btn-search {
  padding: 3px;
  background: transparent;
  color: var(--homez-link-color);
  font-size: 19px;
  border: 0;
  position: absolute;
  top: 50%;
  right: 15px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 1;
}

.widget_search form .btn-search:hover, .widget_search form .btn-search:focus {
  color: var(--homez-second-color);
}

/*------------------------------------*\
    Tags Widget
\*------------------------------------*/
.wp-block-tag-cloud,
.entry-tags-list,
.tagcloud {
  margin: 0;
}

.wp-block-tag-cloud a,
.entry-tags-list a,
.tagcloud a {
  text-transform: capitalize;
  margin-bottom: 12px;
  margin-right: 8px;
  font-size: 14px !important;
  font-weight: 500;
  display: inline-block;
  padding: 0 20px;
  line-height: 38px;
  -webkit-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
  color: var(--homez-link-color);
  background: #fff;
  border: 1px solid #E9E9E9;
  border-radius: 40px;
}

.wp-block-tag-cloud a:hover, .wp-block-tag-cloud a:focus, .wp-block-tag-cloud a.active,
.entry-tags-list a:hover,
.entry-tags-list a:focus,
.entry-tags-list a.active,
.tagcloud a:hover,
.tagcloud a:focus,
.tagcloud a.active {
  background: var(--homez-link-color);
  border-color: var(--homez-link-color);
  color: #fff;
  text-decoration: none;
}

.wp-block-tag-cloud a:last-child,
.entry-tags-list a:last-child,
.tagcloud a:last-child {
  margin-right: 0;
}

.apus-search-top .button-show-search {
  font-size: 18px;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: none;
  color: var(--homez-text-color);
  line-height: 1;
  padding: 0 5px;
  background: transparent !important;
}

.apus-search-top .button-show-search:hover, .apus-search-top .button-show-search:active {
  color: var(--homez-theme-color);
}

.apus-search-top .content-form {
  -webkit-box-shadow: none;
  box-shadow: none;
  margin: 0;
  border: none;
  padding: 0;
  min-width: 280px;
}

/*-----------------------------*\
    Widget Vertical Menu
\*-----------------------------*/
.vertical-menu {
  display: none !important;
  padding: 0;
  background: #fff;
  z-index: 999;
}

.vertical-menu > .nav {
  position: relative;
  width: 100%;
  height: auto;
}

.vertical-menu > .nav .open > a, .vertical-menu > .nav .open > a:hover, .vertical-menu > .nav .open > a:focus,
.vertical-menu > .nav .active > a,
.vertical-menu > .nav .active > a:hover,
.vertical-menu > .nav .active > a:focus {
  color: var(--homez-theme-color);
}

.vertical-menu > .nav > li {
  float: none;
  position: static;
  border-bottom: 1px solid #E9E9E9;
}

.vertical-menu > .nav > li.active > a {
  color: var(--homez-theme-color);
  background: #fafafa;
}

.vertical-menu > .nav > li > a {
  color: #414141;
  padding: 15px 20px;
  font-weight: 500;
  font-size: 12px;
  text-transform: uppercase;
  white-space: nowrap;
}

.vertical-menu > .nav > li > a:hover {
  color: var(--homez-theme-color);
  background: #fafafa;
}

.vertical-menu > .nav > li > a .fa {
  font-size: 15px;
  min-width: 15px;
  margin-right: 12px;
}

.vertical-menu > .nav > li .dropdown-menu {
  min-width: 230px;
  min-height: 100%;
  border-radius: 0;
}

.vertical-menu > .nav .product-block {
  padding: 0 !important;
  overflow: hidden;
  display: block;
}

.vertical-menu .dropdown-menu {
  margin: 0;
  padding: 1.875rem;
  border: none;
  top: 0;
}

.vertical-menu .dropdown-menu::after {
  display: block;
  clear: both;
  content: "";
}

.vertical-menu .dropdown-menu ul {
  padding: 0;
  list-style: none;
}

.vertical-menu .dropdown-menu ul li {
  line-height: 34px;
}

.vertical-menu .dropdown-menu ul li a {
  color: #414141;
}

.vertical-menu .dropdown-menu ul li a:hover, .vertical-menu .dropdown-menu ul li a.active {
  color: var(--homez-theme-color);
}

.vertical-menu .dropdown-menu ul ul {
  padding-left: 15px;
}

.vertical-menu .dropdown-menu .widget-title {
  border: none;
  font-size: 16px;
  padding: 0 0 15px;
  color: var(--homez-link-color);
}

.vertical-menu .dropdown-menu .woocommerce .product-wrapper {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.vertical-menu.menu-left .dropdown-menu {
  left: 100% !important;
  right: auto !important;
}

.vertical-menu.menu-right .dropdown-menu {
  left: auto !important;
  right: 100% !important;
}

.vertical-menu .icon-ver {
  margin-right: 10px;
}

#recentcomments {
  list-style: none;
}

#recentcomments > li {
  margin: 0 0 1em;
}

#recentcomments > li:last-child {
  margin: 0;
}

.widget_rss ul {
  list-style: none;
  padding: 0;
}

.widget_rss ul li {
  margin-bottom: 1rem;
}

.widget_rss ul li:last-child {
  margin-bottom: 0;
}

.widget-quicklink-menu {
  background-color: #f6f6f6;
  line-height: 35px;
}

.widget-quicklink-menu .quicklink-heading {
  background-color: #333333;
  color: #ffffff;
  display: inline-block;
  font-size: 10px;
  margin: 0 20px 0 0;
  padding: 12px 15px 12px 25px;
  position: relative;
  text-transform: uppercase;
  font-family: var(--homez-main-font);
}

.woo-onsale .onsale {
  display: none;
}

.woo-onsale .product-sale-label {
  position: absolute;
  width: 36px;
  height: 36px;
  background-color: #fb4949;
  color: #fff;
  top: 10px;
  right: 10px;
  border-radius: 50%;
  line-height: 36px;
  font-size: 12px;
  font-weight: 400;
}

.widget-tabs .widget-title {
  display: inline-block;
}

.widget-tabs .nav-tabs {
  border: none;
  display: inline-block;
  vertical-align: middle;
  margin: 0 0 7px;
}

.widget-tabs .nav-tabs.tabs-list-v2 {
  margin: 0 0 15px;
}

.widget-tabs .carousel-controls {
  top: -42px;
}

.widget-infor .media .fa, .widget-infor .media .icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: var(--homez-theme-color);
  background: #252525;
}

.contant-info .title {
  margin: 0 0 20px;
  font-size: 30px;
}

.contant-info .info-description {
  margin: 0 0 30px;
}

.contant-info .media-heading {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 5px;
}

.contant-info .media {
  margin-top: 30px;
}

.contant-info .media:hover .fa {
  border-color: #dc3545;
  color: #dc3545;
}

.contant-info .media-left {
  padding-right: 20px;
}

.contant-info .media-left .fa {
  border: 2px solid #0d6efd;
  border-radius: 50%;
  color: #0d6efd;
  font-size: 25px;
  height: 58px;
  line-height: 52px;
  text-align: center;
  width: 58px;
}

.widget_text select,
.widget_categories select,
.widget_archive select {
  width: 100%;
  padding: 8px 15px;
  height: 50px;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  -webkit-appearance: none;
  -moz-appearance: none;
  -o-appearance: none;
  background: url("../images/select.png") #fff right 15px center no-repeat;
}

.widget-twitter .twitter-timeline {
  display: block !important;
}

.widget-twitter .timeline-Tweet-media {
  display: none;
}

.widget_apus_instagram {
  margin: 0;
}

.widget_apus_instagram .widget-title {
  font-size: 35px;
  font-weight: 300;
  margin: 0 0 60px;
  padding: 0;
  text-align: center;
  text-transform: inherit;
}

.widget_apus_instagram .widget-title a {
  font-weight: 400;
  color: var(--homez-theme-color);
}

.widget_apus_instagram .instagram-pics a {
  display: block;
  position: relative;
  -webkit-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
}

.widget_apus_instagram .instagram-pics a:hover, .widget_apus_instagram .instagram-pics a:active {
  outline: 8px solid var(--homez-theme-color);
  outline-offset: -8px;
}

.widget_apus_instagram .instagram-pics a:hover:before, .widget_apus_instagram .instagram-pics a:active:before {
  opacity: 0;
  filter: alpha(opacity=0);
}

.widget_apus_instagram .instagram-pics a:before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
  filter: alpha(opacity=50);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.widget-ticket-pricing {
  background: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border: 2px dashed #E9E9E9;
  border-radius: 50px;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .widget-ticket-pricing .product-block-pricing {
    max-width: 170px;
    margin: auto;
  }
}

.widget-ticket-pricing .column {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  overflow: hidden;
  border: 2px dashed #E9E9E9;
  border-radius: 50px;
  margin: -2px 0;
  padding: 0 10px !important;
}

.widget-ticket-pricing .column:last-child, .widget-ticket-pricing .column:first-child {
  border: none;
  margin: 0;
}

.widget-ticket-pricing.style-style2 {
  border: 1px solid #E9E9E9;
  overflow: visible;
}

.widget-ticket-pricing.style-style2 .column {
  border: 1px solid #E9E9E9;
  margin: -1px 0;
}

.widget-ticket-pricing.style-style2 .column:last-child, .widget-ticket-pricing.style-style2 .column:first-child {
  margin: 0;
  border: none;
}

.widget-ticket-pricing.style-style2 .column:hover {
  border-color: var(--homez-theme-color);
}

.widget-ticket-pricing.style-style2 .column:hover .product-block-pricing .wrapper-pricing .price {
  border: 1px solid var(--homez-theme-color);
}

.widget-ticket-pricing.style-style2 .column:hover .product-block-pricing .wrapper-pricing:before {
  border-bottom: 1px solid var(--homez-theme-color);
}

.widget-ticket-pricing.style-style2 .product-block-pricing .wrapper-pricing .price {
  border: 1px solid #E9E9E9;
}

.widget-ticket-pricing.style-style2 .product-block-pricing .wrapper-pricing:before {
  border-bottom: 1px solid #E9E9E9;
}

.widget-ticket-pricing.style-style3 {
  border: none;
  overflow: visible;
}

.widget-ticket-pricing.style-style3 .column {
  border: none;
  overflow: visible;
  margin: 20px 0;
}

.product-block-pricing .name {
  font-size: 22px;
  font-family: var(--homez-main-font);
  margin: 37px 0 30px;
  font-weight: 400;
  text-align: center;
}

.product-block-pricing .wrapper-pricing {
  text-align: center;
  position: relative;
}

.product-block-pricing .wrapper-pricing:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 1000px;
  height: 2px;
  border-bottom: 2px dashed #E9E9E9;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: -150px;
  z-index: 1;
  content: '';
}

.product-block-pricing .wrapper-pricing .price {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border: 2px dashed #E9E9E9;
  border-radius: 50%;
  padding: 8px;
  background: #fff;
  display: inline-block;
  z-index: 2;
  position: relative;
}

.product-block-pricing .woocommerce-Price-amount {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 100px;
  height: 100px;
  line-height: 100px;
  display: inline-block;
  text-align: center;
  font-size: 30px;
  font-weight: normal;
  color: var(--homez-link-color);
  background: #f1f1f1;
  border-radius: 50%;
}

.product-block-pricing .woocommerce-Price-amount > span {
  font-weight: 300;
}

.product-block-pricing .block-inner-content .desc {
  margin: 20px 0 35px;
}

.product-block-pricing .block-inner-content .desc strong {
  color: var(--homez-link-color);
}

.product-block-pricing .block-inner-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.product-block-pricing .block-inner-content ul li {
  margin: 0 0 5px;
}

.product-block-pricing .block-inner-content ul i {
  margin-right: 15px;
  color: var(--homez-theme-color);
}

.product-block-pricing .button {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.product-block-pricing .groups-button {
  margin: 40px 0 45px;
}

.product-block-pricing .groups-button .button.added {
  display: none;
}

.product-block-pricing .groups-button .added_to_cart.wc-forward {
  display: inline-block;
  padding: 3.875rem 0.75rem;
  white-space: nowrap;
  vertical-align: middle;
  font-size: 14px;
  font-weight: normal;
  text-transform: uppercase;
  border-radius: 25px;
  background: var(--homez-theme-color);
  color: #fff;
}

.product-block-pricing .groups-button .added_to_cart.wc-forward:hover, .product-block-pricing .groups-button .added_to_cart.wc-forward:active {
  color: #fff;
  background: var(--homez-theme-hover-color);
}

.product-block-pricing:hover .woocommerce-Price-amount {
  background-color: var(--homez-theme-color);
  color: #fff;
}

.product-block-pricing:hover .button {
  color: #fff !important;
}

.product-block-pricing:hover .button:before {
  opacity: 0;
  filter: alpha(opacity=0);
}

.popupnewsletter-wrapper .mfp-content {
  width: 590px;
  max-width: 90%;
}

.popupnewsletter-wrapper .apus-mfp-close {
  background: #dc3545;
  color: #fff;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 16px;
  line-height: 47px;
  border-radius: 50%;
  -webkit-transform: translate(22px, -22px);
  -ms-transform: translate(22px, -22px);
  -o-transform: translate(22px, -22px);
  transform: translate(22px, -22px);
  opacity: 1;
  filter: alpha(opacity=100);
}

.popupnewsletter-wrapper .apus-mfp-close:hover, .popupnewsletter-wrapper .apus-mfp-close:focus {
  background: #d32535;
}

.popupnewsletter-wrapper .modal-content {
  border-radius: 0;
  padding: 260px 60px 40px;
  text-align: center;
}

.popupnewsletter-wrapper .modal-content h3 {
  font-size: 20px;
  margin: 0 0 15px;
}

@media (min-width: 1200px) {
  .popupnewsletter-wrapper .modal-content h3 {
    font-size: 30px;
  }
}

.popupnewsletter-wrapper .modal-content .description {
  font-family: var(--homez-main-font);
  font-size: 16px;
  margin: 0 0 20px;
}

.popupnewsletter-wrapper .modal-content form {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 1px solid #E9E9E9;
  width: 325px;
  background: #f5f6f7;
  clear: both;
  margin: 0 auto 20px;
  border-radius: 46px;
  position: relative;
  padding-right: 46px;
}

@media (min-width: 1200px) {
  .popupnewsletter-wrapper .modal-content form {
    margin: 0 auto 40px;
  }
}

.popupnewsletter-wrapper .modal-content form:hover {
  border-color: #d0d0d0;
}

.popupnewsletter-wrapper .modal-content form .form-control {
  background: #f5f6f7;
  width: 100%;
  border: none;
  border-radius: 46px;
  height: 44px;
  display: block;
}

.popupnewsletter-wrapper .modal-content form .input-group {
  position: static;
  width: 100%;
  display: block;
}

.popupnewsletter-wrapper .modal-content form .input-group > * {
  display: block;
  float: none;
  position: static;
}

.popupnewsletter-wrapper .modal-content form [type="submit"] {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 2 !important;
  top: -1px;
  right: -1px;
  z-index: 1;
  width: 46px;
  height: 46px;
  line-height: 46px;
  border-radius: 46px;
  display: inline-block;
  color: transparent;
  background: var(--homez-theme-color);
}

.popupnewsletter-wrapper .modal-content form [type="submit"]:before {
  content: "\f1d8";
  font-family: 'FontAwesome';
  font-size: 18px;
  color: #fff;
  display: inline-block;
  width: 45px;
  text-align: center;
}

.popupnewsletter-wrapper .modal-content form [type="submit"]:hover, .popupnewsletter-wrapper .modal-content form [type="submit"]:focus {
  background-color: var(--homez-theme-hover-color);
}

.popupnewsletter-wrapper .close-dont-show:hover, .popupnewsletter-wrapper .close-dont-show:focus {
  color: #dc3545;
}

.form-login-register-inner {
  background: #fff;
  border-radius: 8px;
  padding: 0.9375rem;
}

@media (min-width: 1200px) {
  .form-login-register-inner {
    padding: 1.875rem;
  }
}

.form-login-register-inner #register-phone-cc + .select2 {
  width: 95px !important;
}

.form-login-register-inner #register-phone-cc + .select2 .select2-selection--single {
  border-radius: 4px 0 0 4px;
}

.form-login-register-inner #register-phone-cc + .select2 .select2-selection--single .select2-selection__rendered {
  padding: 10px 8px;
}

.form-login-register-inner #register-phone {
  width: calc(100% - 95px) !important;
  border-radius: 0 4px 4px 0;
  border-left: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.form-login-register-inner [for="user-remember-field"] {
  font-weight: 400;
  margin: 0;
}

.form-login-register-inner .lostpassword-link {
  margin-top: 15px;
}

.form-login-register-inner .top-login {
  background-color: #F4F4F4;
  border-radius: 8px 8px 0 0;
  padding: 10px 20px;
  margin: -20px -20px 20px;
}

@media (min-width: 1200px) {
  .form-login-register-inner .top-login {
    padding: 21px 40px;
    margin: -40px -40px 40px;
  }
}

.form-login-register-inner .nav-tabs {
  border: 0;
}

.form-login-register-inner .nav-tabs li + li {
  margin-left: 45px;
}

@media (min-width: 1200px) {
  .form-login-register-inner .nav-tabs li + li {
    margin-left: 25px;
  }
}

.form-login-register-inner .nav-tabs a {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--homez-text-color);
}

.form-login-register-inner .nav-tabs a.active {
  color: var(--homez-link-color);
}

.form-login-register-inner .close-magnific-popup {
  color: var(--homez-link-color);
  font-size: 1rem;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.form-login-register-inner .close-magnific-popup:hover, .form-login-register-inner .close-magnific-popup:focus {
  color: #dc3545;
}

.form-forgot-password-inner {
  display: none;
}

.register-form-otp {
  display: none;
}

.register-form-otp .resend {
  margin-top: 10px;
}

.register-form-otp .sent-txt {
  text-align: center;
  margin-bottom: 10px;
}

.register-form-otp .sent-txt strong {
  font-size: 18px;
}

.register-form-otp .sent-txt .no-change {
  color: #000;
  font-weight: 600;
  cursor: pointer;
}

.otp-input-cont {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  -webkit-justify-content: center;
  width: 100%;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
}

.otp-input-cont input {
  border-radius: 4px;
  border: 1px solid #E9E9E9 !important;
  -webkit-box-shadow: none;
  box-shadow: none;
  width: calc(20% - 20px);
  margin: 3px 10px 0;
  text-align: center;
  padding: 7px;
  outline: none;
}

.tabs-account {
  border: 0;
  padding: 0 0.9375rem;
  margin-top: 0.9375rem;
}

@media (min-width: 1200px) {
  .tabs-account {
    padding: 0 1.875rem;
    margin-top: 1.875rem;
  }
}

.tabs-account > li > a {
  color: #717171;
  font-weight: 600;
  display: inline-block;
  text-decoration: none;
  padding: 0 10px 12px;
  border-bottom: 2px solid transparent;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.tabs-account > li > a:hover, .tabs-account > li > a.active {
  color: var(--homez-link-color);
}

.tabs-account > li > a.active {
  border-color: var(--homez-link-color);
}

.login-form-wrapper .lostpassword-link .back-link,
.register-form-wrapper .lostpassword-link .back-link {
  color: #dc3545;
  text-decoration: underline;
  display: inline-block;
  font-weight: 600;
}

.login-form-wrapper [type="checkbox"],
.register-form-wrapper [type="checkbox"] {
  margin-right: 2px;
}

.login-form-wrapper .info,
.register-form-wrapper .info {
  font-size: 14px;
  margin-bottom: 10px;
}

.login-form-wrapper .link-right,
.register-form-wrapper .link-right {
  text-align: right;
}

.login-form-wrapper .create-account,
.register-form-wrapper .create-account {
  margin: 0 0 5px;
}

.login-form-wrapper .create-account .create,
.register-form-wrapper .create-account .create {
  font-weight: 500;
}

.login-form-wrapper .forgotpassword-form-wrapper,
.register-form-wrapper .forgotpassword-form-wrapper {
  display: none;
}

.login-form-wrapper #recaptcha-contact-form,
.register-form-wrapper #recaptcha-contact-form {
  min-height: 88px;
}

.login-form-wrapper #recaptcha-contact-form > div,
.register-form-wrapper #recaptcha-contact-form > div {
  margin: 0 auto 10px;
}

.login-form-wrapper .line-header,
.register-form-wrapper .line-header {
  text-align: center;
  margin: 0 0 15px;
  position: relative;
}

@media (min-width: 1200px) {
  .login-form-wrapper .line-header,
  .register-form-wrapper .line-header {
    margin-bottom: 25px;
  }
}

.login-form-wrapper .line-header span,
.register-form-wrapper .line-header span {
  position: relative;
  text-transform: uppercase;
  color: var(--homez-link-color);
  font-size: 1rem;
  font-weight: 600;
  display: inline-block;
  padding: 0 10px;
  background: #fff;
}

.login-form-wrapper .line-header:before,
.register-form-wrapper .line-header:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  top: 50%;
  left: 0;
  background: #E9E9E9;
}

.login-form-wrapper label,
.register-form-wrapper label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--homez-link-color);
  margin: 0 0 5px;
}

.login-form-wrapper .show_hide_password,
.register-form-wrapper .show_hide_password {
  display: block;
  position: relative;
}

.login-form-wrapper .show_hide_password .toggle-password,
.register-form-wrapper .show_hide_password .toggle-password {
  cursor: pointer;
  position: absolute;
  z-index: 1;
  top: 50%;
  right: 0.9375rem;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.login-form-wrapper .des-forgot,
.register-form-wrapper .des-forgot {
  color: var(--homez-link-color);
  margin: 0 0 10px;
}

.login-form-wrapper .title-small,
.register-form-wrapper .title-small {
  margin: 0 0 15px;
  font-size: 18px;
}

.sign-in-demo-notice {
  padding: 10px 15px;
  margin-top: 15px;
  border-radius: 8px;
  background: #F6F8F9;
  line-height: 2;
}

@media (min-width: 1200px) {
  .sign-in-demo-notice {
    padding: 20px 30px;
  }
}

.sign-in-demo-notice strong {
  color: var(--homez-link-color);
  font-weight: 600;
}

.forgotpassword-form,
.job-apply-email-form,
.change-password-form,
.delete-profile-form,
.register-form,
.login-form {
  position: relative;
}

.forgotpassword-form:before,
.job-apply-email-form:before,
.change-password-form:before,
.delete-profile-form:before,
.register-form:before,
.login-form:before {
  display: block;
  content: '';
  position: absolute;
  top: 0;
  left: -1px;
  width: calc(100% + 2px);
  height: 100%;
  background: rgba(255, 255, 255, 0.9) url("../images/loading.gif") no-repeat center center/32px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  z-index: 2;
  visibility: hidden;
}

.forgotpassword-form.loading:before,
.job-apply-email-form.loading:before,
.change-password-form.loading:before,
.delete-profile-form.loading:before,
.register-form.loading:before,
.login-form.loading:before {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.inner-social {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
}

.inner-social > div {
  order: 2;
  margin-bottom: 10px;
  text-align: center;
}

.inner-social > div.line-header {
  order: 1;
}

.inner-social > div a {
  display: inline-block;
  width: 100%;
  padding: 10px 20px;
  border-radius: 8px;
  border: 1px solid var(--homez-theme-color);
  color: var(--homez-theme-color);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .inner-social > div a {
    padding: 13px 30px;
  }
}

.inner-social > div a:hover {
  background-color: var(--homez-theme-color);
  color: #fff !important;
}

.inner-social > div a > i {
  font-size: 16px;
  margin-right: 10px;
  float: left;
  margin-top: 5px;
}

.inner-social > div a.facebook-login-btn {
  border-color: #1967D2;
  color: #1967D2;
}

.inner-social > div a.facebook-login-btn:hover, .inner-social > div a.facebook-login-btn:focus {
  background-color: #1967D2;
}

.inner-social > div a.twitter-login-btn {
  border-color: #1DA1F2;
  color: #1DA1F2;
}

.inner-social > div a.twitter-login-btn:hover, .inner-social > div a.twitter-login-btn:focus {
  background-color: #1DA1F2;
}

.inner-social > div a.google-login-btn {
  border-color: #D93025;
  color: #D93025;
}

.inner-social > div a.google-login-btn:hover, .inner-social > div a.google-login-btn:focus {
  background-color: #D93025;
}

.wp-block-search .wp-block-search__label {
  font-size: 1.05rem;
  margin: 0 0 18px;
  text-transform: capitalize;
  color: var(--homez-heading-color);
  font-weight: 500;
  line-height: 1.2;
}

.wp-block-search .wp-block-search__input {
  height: 50px;
  outline: none !important;
  padding: 5px 20px;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.wp-block-search .wp-block-search__input:hover, .wp-block-search .wp-block-search__input:focus {
  border-color: #b6b6b6;
}

.wp-block-search .wp-block-search__button {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  height: 50px;
  padding: 0 20px;
  border: 0;
  background: var(--homez-theme-color);
  color: #fff;
  border-radius: 8px;
}

.wp-block-search .wp-block-search__button:hover, .wp-block-search .wp-block-search__button:focus {
  background: var(--homez-theme-hover-color);
}

.widget-search .input-group {
  z-index: 1;
}

.widget-search .btn-search {
  position: absolute;
  z-index: 4;
  left: 10px;
  top: 14px;
  padding: 5px;
  background: transparent;
  border: 0;
  font-size: 18px;
  font-weight: normal;
  line-height: 1;
}

.widget-search .btn-search:hover, .widget-search .btn-search:focus {
  color: var(--homez-theme-color);
}

.widget-search .form-control {
  border-radius: 8px !important;
  padding-left: 46px;
}

.from-to-text-wrapper {
  position: relative;
  height: calc(1.9em + (1.625rem + 2px));
  background: #fff;
  outline: none;
  border-radius: 8px;
  border: 1px solid #E9E9E9;
  padding: 0.8125rem 1.29rem;
}

.from-to-text-wrapper .heading-filter-price {
  cursor: pointer;
}

.from-to-text-wrapper .price-input-wrapper {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 150px;
  z-index: 1;
  background-color: #fff;
  padding: 10px;
  border-radius: 8px;
}

.from-to-text-wrapper .price-input-wrapper input.form-control {
  height: 40px !important;
  padding: 10px;
}

.from-to-text-wrapper .price-text-wrapper {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
}

.contact-form-wrapper .button {
  position: relative;
}

.contact-form-wrapper .button:before {
  display: block;
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  background: rgba(255, 255, 255, 0.9) url("../images/loading.gif") no-repeat center center/20px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  z-index: 1;
  visibility: hidden;
}

.contact-form-wrapper.loading .button:before {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.contact-form-wrapper .ga-recaptcha {
  margin-bottom: 10px;
}

.contact-form-wrapper .btn-whatsapp {
  margin-top: 0.9375rem;
}

.phone-wrapper.phone-hide .phone {
  display: none;
}

.phone-wrapper.phone-hide .phone-show span {
  display: inline-block;
  vertical-align: top;
  font-size: 11px;
  font-weight: 700;
  border-radius: 2px;
  padding: 1px 6px;
  color: #fff;
  background-color: #24324A;
  text-transform: capitalize;
  cursor: pointer;
  margin-left: 2px;
}

.phone-wrapper.phone-hide.show .phone {
  display: inline-block;
}

.phone-wrapper.phone-hide.show .phone-show {
  display: none;
}

.map-popup {
  width: 40px;
  height: 40px;
  margin-top: -40px;
  margin-left: -20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: flex-end;
  -webkit-align-items: flex-end;
  justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
}

.map-popup:before {
  display: none !important;
}

.map-popup .icon-wrapper {
  color: var(--homez-theme-color) !important;
  font-size: 38px;
  line-height: 1;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: relative;
}

.map-popup .icon-wrapper:before {
  content: '';
  position: absolute;
  display: block;
  top: 100%;
  left: 0;
  width: 100%;
  height: 35px;
  background-color: var(--homez-theme-color);
  border-radius: 50%;
  margin-top: -20px;
  -webkit-transform: rotateX(-60deg) scale(0);
  -moz-transform: rotateX(-60deg) scale(0);
  -ms-transform: rotateX(-60deg) scale(0);
  -o-transform: rotateX(-60deg) scale(0);
  transform: rotateX(-60deg) scale(0);
  opacity: 1;
  filter: alpha(opacity=100);
}

.map-popup .icon-wrapper:not(.has-img):after {
  content: "\f13d";
  font-family: "Flaticon";
}

.map-popup .icon-wrapper:hover, .map-popup .icon-wrapper:focus {
  color: var(--homez-theme-color);
}

.map-popup .icon-wrapper:hover:before, .map-popup .icon-wrapper:focus:before {
  animation: scale_icon_map 1.5s ease-in-out 0s infinite alternate;
  -webkit-animation: scale_icon_map 1.5s ease-in-out 0s infinite alternate;
}

.map-popup.map-popup-selected .icon-wrapper:before {
  animation: scale_icon_map 1.5s ease-in-out 0s infinite normal;
  -webkit-animation: scale_icon_map 1.5s ease-in-out 0s infinite normal;
}

.cmb-form .icon-wrapper:before,
.single-property-map .icon-wrapper:before {
  animation: scale_icon_map 1.5s ease-in-out 0s infinite normal;
  -webkit-animation: scale_icon_map 1.5s ease-in-out 0s infinite normal;
}

#properties-google-maps,
.main-items-wrapper {
  position: relative;
}

#properties-google-maps.loading:before,
.main-items-wrapper.loading:before {
  content: '';
  background-position: center 200px;
  background-repeat: no-repeat;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  max-height: 100%;
  background-color: rgba(247, 247, 247, 0.95);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  width: 100%;
  height: 100%;
}

.btn-remove-compare-all,
.loadmore-message-btn,
.loadmore-replied-btn,
.reply-message-btn {
  position: relative;
}

.btn-remove-compare-all:before,
.loadmore-message-btn:before,
.loadmore-replied-btn:before,
.reply-message-btn:before {
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  background: rgba(255, 255, 255, 0.8) !important;
  opacity: 0;
  filter: alpha(opacity=0);
}

.btn-remove-compare-all:after,
.loadmore-message-btn:after,
.loadmore-replied-btn:after,
.reply-message-btn:after {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 14px;
  opacity: 0;
  filter: alpha(opacity=0);
  color: var(--homez-link-color);
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  margin: -14px -8px;
}

.btn-remove-compare-all.loading,
.loadmore-message-btn.loading,
.loadmore-replied-btn.loading,
.reply-message-btn.loading {
  color: transparent !important;
}

.btn-remove-compare-all.loading:after,
.loadmore-message-btn.loading:after,
.loadmore-replied-btn.loading:after,
.reply-message-btn.loading:after {
  opacity: 0.8;
  filter: alpha(opacity=80);
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.btn-remove-compare-all.loading:before,
.loadmore-message-btn.loading:before,
.loadmore-replied-btn.loading:before,
.reply-message-btn.loading:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

#properties-google-maps.loading:before {
  z-index: 999;
  background-color: rgba(255, 255, 255, 0.75);
}

.property-title {
  font-size: 20px;
  margin: 0 0 15px;
}

.property-price {
  color: var(--homez-link-color);
  font-size: 20px;
  font-weight: 600;
}

@media (min-width: 1200px) {
  .property-price {
    font-size: 24px;
  }
}

.review-author i {
  margin-right: 6px;
  font-size: 10px;
  color: #C4C640;
}

.review-author .space {
  margin: 0 3px;
}

.featured-icon {
  color: #C4C640;
}

.featured-property {
  line-height: 30px;
  display: inline-block;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #fff;
  background-color: #EB6753;
  border-radius: 6px;
  white-space: nowrap;
}

@media (min-width: 1200px) {
  .featured-property {
    padding: 0 12px;
  }
}

.featured-property i {
  margin-right: 4px;
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
  vertical-align: middle;
}

.label-property-label {
  line-height: 30px;
  display: inline-block;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #fff !important;
  background-color: var(--homez-second-color);
  border-radius: 6px;
  white-space: nowrap;
  text-decoration: none !important;
}

@media (min-width: 1200px) {
  .label-property-label {
    padding: 0 12px;
  }
}

.label-property-label i {
  margin-right: 4px;
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
  vertical-align: middle;
}

.gallery-metas > * + * {
  margin-left: 15px;
}

.gallery-property-main-detail {
  position: relative;
  overflow: hidden;
}

.gallery-property-main-detail .gallery-metas {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  padding: 20px;
}

.gallery-property-main-detail .status-property-label {
  background-color: #0D263B;
  color: #fff;
  display: inline-block;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 3px 12px;
  border-radius: 8px;
}

.gallery-property-main-detail .status-property-label:hover, .gallery-property-main-detail .status-property-label:focus {
  color: #fff;
  background-color: #040b11;
}

.avatar-wrapper .avatar-img {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #fff;
  background: #fff;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
}

@media (min-width: 1200px) {
  .avatar-wrapper .avatar-img {
    width: 84px;
    height: 84px;
  }
}

.avatar-wrapper .avatar-img img {
  margin: 0;
}

.avatar-wrapper .avatar-img + .name-author {
  width: calc(100% - 40px);
  padding-left: 10px;
}

.avatar-wrapper .name-author {
  color: var(--homez-link-color);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.property-postdate {
  white-space: nowrap;
}

.box-white-detail {
  padding: 15px;
  background: #fff;
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .box-white-detail {
    padding: 25px 30px;
    margin-bottom: 30px;
  }
}

.box-white-detail .title {
  font-size: 20px;
  margin: 0 0 15px;
}

.title-wrapper > * {
  display: inline-block;
  vertical-align: middle;
}

.title-wrapper > * + * {
  margin-left: 7px;
}

.title-wrapper .agency-title,
.title-wrapper .agent-title {
  margin: 0;
  font-size: 30px;
  line-height: 1.2;
}

@media (min-width: 1200px) {
  .title-wrapper .agency-title,
  .title-wrapper .agent-title {
    font-size: 40px;
  }
}

.title-wrapper .featured-icon {
  margin-top: -10px;
  margin-left: 10px;
}

.star-rating .review-avg {
  display: none;
}

.review-stars-rated {
  position: relative;
  overflow: hidden;
  width: 85px;
}

.review-stars-rated .review-stars {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #e1e1e1;
  white-space: nowrap;
  overflow: hidden;
  font-size: 10px;
  letter-spacing: 2px;
}

.review-stars-rated .review-stars li {
  display: inline-block;
}

.review-stars-rated .review-stars.filled {
  color: #C4C640;
  position: absolute;
  top: 0;
  left: 0;
}

.review-stars-rated-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
}

.review-stars-rated-wrapper .nb-review {
  margin-left: 7px;
}

.review-stars-rated-wrapper .nb-pre-review {
  margin-right: 7px;
}

.review-title-wrapper {
  margin-bottom: 1.875rem;
}

.review-title-wrapper .comments-title,
.review-title-wrapper .title {
  margin: 0;
  font-size: 18px;
}

#properties-google-maps {
  position: relative;
  z-index: 1;
  font-size: 0.875rem;
  font-family: var(--bs-font-sans-serif);
  line-height: 1.9;
  overflow: hidden;
  width: 100%;
  height: 280px;
}

@media (min-width: 1200px) {
  #properties-google-maps {
    height: 400px;
  }
}

#properties-google-maps .leaflet-popup-content {
  line-height: 1.9;
}

#properties-google-maps .leaflet-popup-content {
  margin: 0;
}

#properties-google-maps .leaflet-popup-content-wrapper {
  padding: 0;
  background-color: transparent;
  -webkit-box-shadow: 0 10px 50px 0 rgba(13, 38, 59, 0.1);
  box-shadow: 0 10px 50px 0 rgba(13, 38, 59, 0.1);
}

#properties-google-maps a {
  color: var(--homez-link-color);
}

#properties-google-maps a:hover, #properties-google-maps a:focus {
  color: var(--homez-theme-color);
}

#properties-google-maps .btn-show-filter {
  position: absolute;
  top: 0.9375rem;
  left: 1.875rem;
  z-index: 401;
}

@media (min-width: 1200px) {
  #properties-google-maps .btn-show-filter {
    top: 1.875rem;
    left: 70px;
  }
}

#properties-google-maps .property-grid {
  border-radius: 8px;
  overflow: hidden;
  margin: 0;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

#properties-google-maps .property-grid .property-price {
  font-size: 14px;
  font-weight: 500;
}

#properties-google-maps .property-grid .top-info {
  padding: 16px 20px 20px;
}

#properties-google-maps .property-grid .property-title {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

#properties-google-maps .property-grid .property-metas {
  margin: 5px 0 0;
  justify-content: start;
  -webkit-justify-content: start;
  -ms-justify-content: start;
}

#properties-google-maps .property-grid .property-metas > div {
  margin-right: 20px;
}

#properties-google-maps .property-grid .property-metas > div:last-child {
  margin-right: 0;
}

#properties-google-maps .property-grid .property-metas i {
  display: none;
}

#properties-google-maps.leaflet-container a.leaflet-popup-close-button {
  margin: 10px;
  background: #fff;
  padding: 3px 7px 6px;
  height: auto;
  width: auto;
  line-height: 1;
  border-radius: 6px;
  color: #dc3545;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

#properties-google-maps.leaflet-container a.leaflet-popup-close-button:hover, #properties-google-maps.leaflet-container a.leaflet-popup-close-button:focus {
  color: #fff;
  background: #dc3545;
  opacity: 1;
  filter: alpha(opacity=100);
}

#properties-google-maps .leaflet-popup {
  margin-bottom: 1.875rem;
}

#properties-google-maps .leaflet-popup-tip-container {
  margin-top: -2px;
}

#properties-google-maps .leaflet-popup-tip {
  -webkit-box-shadow: 0 10px 50px 0 rgba(13, 38, 59, 0.1);
  box-shadow: 0 10px 50px 0 rgba(13, 38, 59, 0.1);
}

#properties-google-maps .marker-cluster {
  width: 40px !important;
  height: 40px !important;
  margin-left: -20px !important;
  margin-top: -20px !important;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

#properties-google-maps .marker-cluster > div {
  width: 40px;
  height: 40px;
  display: inline-block;
  line-height: 40px;
  text-align: center;
  background: var(--homez-second-color);
  border-radius: 50%;
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

#properties-google-maps .marker-cluster:before {
  content: '';
  position: absolute;
  top: -7px;
  left: -7px;
  display: block;
  border-radius: 50%;
  width: calc(100% + 14px);
  height: calc(100% + 14px);
  opacity: 0.1;
  filter: alpha(opacity=10);
  background-color: var(--homez-second-color);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

#properties-google-maps .marker-cluster:hover:before, #properties-google-maps .marker-cluster:focus:before {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.socials-member a {
  margin-right: 10px;
  color: #767676;
}

.socials-member a:hover, .socials-member a:focus {
  color: var(--homez-theme-color);
}

@media (min-width: 1200px) {
  .socials-member a {
    margin-right: 15px;
  }
}

.properties-ordering-wrapper,
.agencies-ordering-wrapper,
.agents-ordering-wrapper {
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .properties-ordering-wrapper,
  .agencies-ordering-wrapper,
  .agents-ordering-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    -webkit-align-items: center;
  }
  .properties-ordering-wrapper .ordering-display-mode-wrapper,
  .agencies-ordering-wrapper .ordering-display-mode-wrapper,
  .agents-ordering-wrapper .ordering-display-mode-wrapper {
    margin-left: auto;
  }
  .properties-ordering-wrapper > aside,
  .agencies-ordering-wrapper > aside,
  .agents-ordering-wrapper > aside {
    min-width: 60%;
  }
}

@media (min-width: 1200px) {
  .properties-ordering-wrapper,
  .agencies-ordering-wrapper,
  .agents-ordering-wrapper {
    margin-bottom: 1.875rem;
  }
}

.properties-ordering-wrapper .saved-search-form-btn,
.agencies-ordering-wrapper .saved-search-form-btn,
.agents-ordering-wrapper .saved-search-form-btn {
  margin-left: 15px;
}

@media (min-width: 1200px) {
  .properties-ordering-wrapper .saved-search-form-btn,
  .agencies-ordering-wrapper .saved-search-form-btn,
  .agents-ordering-wrapper .saved-search-form-btn {
    margin-left: 25px;
  }
}

.properties-ordering-wrapper .btn-saved-search,
.agencies-ordering-wrapper .btn-saved-search,
.agents-ordering-wrapper .btn-saved-search {
  font-size: 16px;
}

.properties-ordering-wrapper .btn-saved-search i,
.agencies-ordering-wrapper .btn-saved-search i,
.agents-ordering-wrapper .btn-saved-search i {
  font-size: 21px;
  margin-right: 5px;
}

.properties-ordering-wrapper .properties-display-mode-wrapper,
.agencies-ordering-wrapper .properties-display-mode-wrapper,
.agents-ordering-wrapper .properties-display-mode-wrapper {
  display: none;
}

.properties-ordering-wrapper .results-count,
.agencies-ordering-wrapper .results-count,
.agents-ordering-wrapper .results-count {
  color: var(--homez-link-color);
}

.properties-ordering-wrapper .filter-in-sidebar-wrapper,
.agencies-ordering-wrapper .filter-in-sidebar-wrapper,
.agents-ordering-wrapper .filter-in-sidebar-wrapper {
  margin-right: 20px;
}

@media (max-width: 767px) {
  .properties-ordering-wrapper .filter-in-sidebar-wrapper,
  .agencies-ordering-wrapper .filter-in-sidebar-wrapper,
  .agents-ordering-wrapper .filter-in-sidebar-wrapper {
    margin: 0 0 5px;
  }
}

.properties-ordering-wrapper form.properties-ordering,
.agencies-ordering-wrapper form.properties-ordering,
.agents-ordering-wrapper form.properties-ordering {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.agent-title {
  margin: 0 0 2px;
  font-size: 15px;
}

.agent-item {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .agent-item {
    margin-bottom: 1.875rem;
  }
}

.agent-item .nb-property {
  display: inline-block;
  padding: 4px 12px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  background: var(--homez-theme-color);
  border-radius: 6px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  top: 20px;
  left: 20px;
}

.agent-item .top-info {
  margin-bottom: 20px;
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .agent-item .top-info {
    border-radius: 12px;
  }
}

.agent-item .property-job {
  font-size: 15px;
}

.agent-item:hover .nb-property {
  opacity: 1;
  filter: alpha(opacity=100);
}

.agent-item.v2 .top-info {
  border-radius: 50%;
  overflow: hidden;
}

.author-information > div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 12px;
  justify-content: space-between;
}

.author-information > div:last-child {
  margin-bottom: 0;
}

.author-information .with-title,
.author-information .location-label {
  flex-shrink: 0;
  font-weight: 600;
  padding-right: 10px;
  display: inline-block;
  width: 40%;
}

.top-author-inner {
  margin-left: auto;
  margin-right: auto;
  max-width: 1600px;
  background: var(--homez-theme-color-007);
  padding: 1.875rem 0;
  border-radius: 8px;
  margin-top: 1.875rem;
}

@media (min-width: 1200px) {
  .top-author-inner {
    margin-top: 60px;
    padding: 70px 0;
    border-radius: 12px;
  }
}

.top-author-inner.header-agency {
  background: var(--homez-second-color);
}

.top-author-inner.header-agency .member-title, .top-author-inner.header-agency, .top-author-inner.header-agency a {
  color: #fff !important;
}

@media (min-width: 992px) {
  .sidebar-wrapper-author {
    margin-top: -150px;
  }
}

.sidebar-wrapper-author .widget {
  border: 1px solid #E9E9E9;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

.agent-list {
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #E9E9E9;
  padding: 10px;
}

@media (min-width: 1501px) {
  .agent-list {
    padding: 20px;
  }
}

.agent-list .member-thumbnail-wrapper {
  margin-bottom: 0;
  width: 170px;
  padding-right: 15px;
}

@media (min-width: 1501px) {
  .agent-list .member-thumbnail-wrapper {
    width: 220px;
    padding-right: 20px;
  }
}

.agent-list .inner {
  width: 100%;
}

.agent-list .agent-information-bottom,
.agent-list .metas {
  margin-top: 5px;
}

@media (min-width: 1501px) {
  .agent-list .agent-information-bottom,
  .agent-list .metas {
    margin-top: 15px;
  }
}

.agent-list .agent-information {
  width: calc(100% - 170px);
}

@media (min-width: 1501px) {
  .agent-list .agent-information {
    width: calc(100% - 220px);
  }
}

.agent-list .agent-information.no-image {
  width: 100%;
}

.agent-list:hover {
  -webkit-box-shadow: 0 10px 50px 0 rgba(13, 38, 59, 0.1);
  box-shadow: 0 10px 50px 0 rgba(13, 38, 59, 0.1);
}

.agency-item {
  font-size: 15px;
  padding: 20px;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  background: #fff;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin-bottom: 0.9375rem;
}

@media (min-width: 1200px) {
  .agency-item {
    padding: 1.875rem;
    margin-bottom: 1.875rem;
    border-radius: 12px;
  }
}

.agency-item .review-author {
  margin-bottom: 5px;
}

.agency-item .agency-information {
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  .agency-item .agency-information {
    padding-top: 1.875rem;
    margin-top: 1.875rem;
  }
}

.agency-item .nb-property {
  display: inline-block;
  padding: 4px 12px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  background: var(--homez-theme-color);
  border-radius: 6px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  z-index: 1;
  top: 20px;
  left: 20px;
}

.agency-item .agency-title {
  font-size: 15px;
  margin: 0 0 3px;
}

.agency-item .agency-information-bottom {
  margin-top: 20px;
}

.agency-item:hover {
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

.agency-item:hover .nb-property {
  opacity: 1;
  filter: alpha(opacity=100);
}

.agents-pagination-wrapper .pagination,
.agencies-pagination-wrapper .pagination {
  padding: 15px 0 0;
}

.agencies-ordering-wrapper .widget,
.agents-ordering-wrapper .widget {
  margin-bottom: 0;
}

.agencies-ordering-wrapper .row.list-fileds,
.agents-ordering-wrapper .row.list-fileds {
  margin-left: -5px;
  margin-right: -5px;
}

.agencies-ordering-wrapper .row.list-fileds > div,
.agents-ordering-wrapper .row.list-fileds > div {
  padding-left: 5px;
  padding-right: 5px;
}

.agencies-ordering-wrapper .select2-container--default.select2-container .select2-selection--single,
.agencies-ordering-wrapper .form-control,
.agents-ordering-wrapper .select2-container--default.select2-container .select2-selection--single,
.agents-ordering-wrapper .form-control {
  height: 45px;
  padding-top: 0;
  padding-bottom: 0;
  border-radius: 45px !important;
}

.agencies-ordering-wrapper .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow,
.agencies-ordering-wrapper .form-control .select2-selection__arrow,
.agents-ordering-wrapper .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow,
.agents-ordering-wrapper .form-control .select2-selection__arrow {
  top: 9px;
}

.send-message-btn {
  display: block;
  position: relative;
}

.send-message-btn:before {
  display: block;
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  background: rgba(255, 255, 255, 0.9) url("../images/loading.gif") no-repeat center center/20px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  z-index: 1;
  visibility: hidden;
}

.send-message-btn.loading:before {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.location-banner-inner {
  display: block;
  text-decoration: none !important;
}

.location-banner-inner .banner-image {
  max-width: 70px;
  max-height: 70px;
  overflow: hidden;
  border-radius: 6px;
}

@media (min-width: 1200px) {
  .location-banner-inner .banner-image {
    max-width: 110px;
    max-height: 110px;
  }
}

.location-banner-inner .banner-image + .inner {
  padding-left: 20px;
}

.location-banner-inner .title {
  font-size: 15px;
  margin: 0;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.location-banner-inner .number {
  font-size: 13px;
  margin-top: 5px;
}

.location-banner-inner:hover .title {
  color: var(--homez-theme-color);
}

.location-banner-inner.style2 {
  overflow: hidden;
}

.location-banner-inner.style2:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background-image: linear-gradient(to bottom, #1A1C21, transparent);
  opacity: 0.8;
  filter: alpha(opacity=80);
}

.location-banner-inner.style2 .banner-image {
  width: 100%;
  height: auto;
  max-width: inherit;
  max-height: inherit;
  border-radius: 0;
}

.location-banner-inner.style2 .inner {
  padding: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
}

@media (min-width: 1200px) {
  .location-banner-inner.style2 .inner {
    padding: 1.875rem;
  }
}

.location-banner-inner.style2 .title,
.location-banner-inner.style2 .number {
  color: #fff;
}

.location-banner-inner.style2:hover:before {
  height: 200%;
}

.type-banner-inner {
  overflow: hidden;
  display: block;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  text-decoration: none !important;
}

.type-banner-inner .type-icon {
  font-size: 25px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  background: #fff;
  color: var(--homez-link-color);
}

@media (min-width: 1200px) {
  .type-banner-inner .type-icon {
    font-size: 30px;
    width: 70px;
    height: 70px;
  }
}

.type-banner-inner .title {
  margin: 0;
  font-size: 15px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.type-banner-inner .number {
  font-size: 13px;
  margin-top: 5px;
}

.type-banner-inner.style1 {
  background: #F7F7F7;
  border-radius: 8px;
  padding: 0.9375rem;
}

@media (min-width: 1200px) {
  .type-banner-inner.style1 {
    border-radius: 12px;
    padding: 1.875rem;
  }
}

.type-banner-inner.style1 .title {
  margin-top: 1.875rem;
}

@media (min-width: 1200px) {
  .type-banner-inner.style1 .title {
    margin-top: 60px;
  }
}

.type-banner-inner.style1:hover {
  background: var(--homez-second-color);
}

.type-banner-inner.style1:hover, .type-banner-inner.style1:hover .title {
  color: #fff;
}

.type-banner-inner.style1:hover .type-icon {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.type-banner-inner.style2 .info-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.type-banner-inner.style2 .inner {
  flex-grow: 1;
}

.type-banner-inner.style2 .type-icon {
  flex-shrink: 0;
  font-size: 0.875rem;
  width: 30px;
  height: 30px;
}

.type-banner-inner.style2 .type-icon + .inner {
  padding-left: 10px;
}

.type-banner-inner.style2 .title {
  font-weight: 400;
  font-size: 0.875rem;
}

.type-banner-inner.style2.has-img {
  overflow: hidden;
  text-align: left;
  background: #fff;
  -webkit-box-shadow: 0 10px 20px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 20px 0 rgba(24, 26, 32, 0.05);
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .type-banner-inner.style2.has-img {
    border-radius: 12px;
  }
}

.type-banner-inner.style2.has-img .info-type {
  position: static;
  padding: 15px 20px;
}

.type-banner-inner.style2.has-img .title {
  font-weight: 600;
  font-size: 15px;
}

.type-banner-inner.style2.has-img:hover {
  -webkit-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  -o-transform: translateY(-10px);
  transform: translateY(-10px);
}

.type-banner-inner.style2.has-img:hover img {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.type-banner-inner.style2:hover .type-icon {
  color: #fff;
  background: var(--homez-theme-color);
}

.type-banner-inner.has-img {
  padding: 0;
  text-align: center;
}

.type-banner-inner.has-img .title {
  margin: 0;
}

.type-banner-inner.has-img .info-type {
  z-index: 1;
  position: absolute;
  top: 20px;
  left: 0;
  width: 100%;
}

@media (min-width: 1200px) {
  .type-banner-inner.has-img .info-type {
    top: 40px;
  }
}

.type-banner-inner.has-img img {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.type-banner-inner.has-img:hover img {
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.type-banner-inner.st_line {
  border-radius: 70px;
  padding: 10px;
  border: 1px solid transparent;
  margin-bottom: 10px;
}

.type-banner-inner.st_line .title {
  margin: 0;
  white-space: nowrap;
}

.type-banner-inner.st_line .type-icon {
  flex-shrink: 0;
  font-size: 22px;
  width: 50px;
  height: 50px;
}

.type-banner-inner.st_line .type-icon + .inner {
  padding: 0 12px;
}

.type-banner-inner.st_line:hover {
  border-color: #E9E9E9;
}

.flex-type-line {
  margin-right: 10px;
}

.widget-property-types.sp_small .row,
.widget-property-types.sp_small .slick-carousel {
  margin-right: -8px;
  margin-left: -8px;
}

.widget-property-types.sp_small .row [class^="col-"],
.widget-property-types.sp_small .row .slick-slide,
.widget-property-types.sp_small .slick-carousel [class^="col-"],
.widget-property-types.sp_small .slick-carousel .slick-slide {
  padding-left: 8px;
  padding-right: 8px;
}

.widget-property-types .list-item {
  margin-bottom: 0.9375rem;
}

.type-banner-image {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  display: block;
  overflow: hidden;
  color: #fff;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .type-banner-image {
    border-radius: 12px;
  }
}

.type-banner-image:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background-image: linear-gradient(to bottom, #1A1C21, transparent);
  opacity: 0.8;
  filter: alpha(opacity=80);
}

.type-banner-image .inner {
  padding: 0.9375rem;
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@media (min-width: 1200px) {
  .type-banner-image .inner {
    padding: 1.875rem;
  }
}

.type-banner-image .title {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 15px;
  margin: 0;
  color: #fff;
}

.type-banner-image .number {
  color: #fff;
  font-size: 13px;
  margin-top: 6px;
}

.type-banner-image .more {
  color: #fff;
  margin-top: auto;
  -webkit-transform: translateY(100%);
  -ms-transform: translateY(100%);
  -o-transform: translateY(100%);
  transform: translateY(100%);
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.type-banner-image .more i {
  margin-left: 7px;
  vertical-align: middle;
}

.type-banner-image:hover .more {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
  filter: alpha(opacity=100);
}

.type-banner-image:hover:before {
  opacity: 0.6;
  filter: alpha(opacity=60);
  height: 200%;
}

.item-location {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  display: block;
  overflow: hidden;
  color: #fff;
  text-decoration: none !important;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .item-location {
    border-radius: 12px;
  }
}

.item-location:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background-image: linear-gradient(to bottom, #1A1C21, transparent);
  opacity: 0.8;
  filter: alpha(opacity=80);
}

.item-location .inner {
  padding: 0.9375rem;
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@media (min-width: 1200px) {
  .item-location .inner {
    padding: 1.875rem;
  }
}

.item-location .title {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 15px;
  margin: 0;
  color: #fff;
}

.item-location .number {
  color: #fff;
  font-size: 13px;
  margin-top: 5px;
}

.item-location .more {
  color: #fff;
  margin-top: auto;
  -webkit-transform: translateY(100%);
  -ms-transform: translateY(100%);
  -o-transform: translateY(100%);
  transform: translateY(100%);
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.item-location .more i {
  margin-left: 7px;
  vertical-align: middle;
}

.item-location.style2:before {
  display: none;
}

.item-location.style2 .banner-image {
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .item-location.style2 .banner-image {
    border-radius: 12px;
  }
}

.item-location.style2 .banner-image img {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.item-location.style2 .inner {
  padding: 20px 0 0;
  position: static;
  width: 100%;
  height: auto;
}

.item-location.style2 .number,
.item-location.style2 .title {
  color: var(--homez-link-color);
}

.item-location.style2 .more {
  display: none;
}

.item-location.style2:hover .banner-image img {
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.item-location.style3 {
  text-align: center;
}

.item-location.style3:before {
  display: none;
}

.item-location.style3 .banner-image {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  overflow: hidden;
  border-radius: 50%;
}

.item-location.style3 .banner-image img {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.item-location.style3 .inner {
  padding: 20px 0 0;
  position: static;
  width: 100%;
  height: auto;
}

.item-location.style3 .number,
.item-location.style3 .title {
  color: var(--homez-link-color);
}

.item-location.style3 .more {
  display: none;
}

.item-location.style3:hover .banner-image img {
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.item-location.style4 {
  background: #fff;
  -webkit-box-shadow: 0 10px 20px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 20px 0 rgba(24, 26, 32, 0.05);
}

.item-location.style4 .more, .item-location.style4:before {
  display: none;
}

.item-location.style4 .inner {
  width: 100%;
  height: auto;
  position: static;
  padding: 15px 20px;
}

.item-location.style4 .title,
.item-location.style4 .number {
  color: var(--homez-link-color);
}

.item-location.style4:hover {
  -webkit-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  -o-transform: translateY(-10px);
  transform: translateY(-10px);
}

.item-location:hover .more {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
  filter: alpha(opacity=100);
}

.item-location:hover:before {
  opacity: 0.6;
  filter: alpha(opacity=60);
  height: 200%;
}

.location-banner-inner-list {
  border-radius: 8px;
  overflow: hidden;
  display: block;
  background-color: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.location-banner-inner-list .inner {
  border: 1px solid #E9E9E9;
  padding: 18px 20px;
  border-radius: 0 0 8px 8px;
  color: var(--homez-text-color);
}

.location-banner-inner-list .title {
  font-size: 16px;
  margin: 0 0 7px;
}

.location-banner-inner-list .number span {
  font-weight: 600;
  color: var(--homez-link-color);
}

.location-banner-inner-list .median-price span {
  color: var(--homez-link-color);
  font-weight: 600;
}

.location-banner-inner-list:hover {
  -webkit-box-shadow: 0 10px 30px 0 rgba(13, 38, 59, 0.07);
  box-shadow: 0 10px 30px 0 rgba(13, 38, 59, 0.07);
}

.single-listing-agent-agency {
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .single-listing-agent-agency {
    margin-bottom: 20px;
  }
}

@media (min-width: 1200px) {
  .single-listing-agent-agency {
    margin-bottom: 50px;
  }
}

.top-detail-member .member-thumbnail-wrapper {
  position: relative;
  width: 170px;
  border-radius: 50%;
  overflow: hidden;
}

.top-detail-member .member-information {
  padding: 15px 0 0 0;
}

@media (min-width: 768px) {
  .top-detail-member .member-information {
    padding: 0 0 0 15px;
  }
}

@media (min-width: 1200px) {
  .top-detail-member .member-information {
    padding-left: 30px;
  }
}

.top-detail-member .member-information.no-image {
  width: 100%;
  padding: 0;
}

.top-detail-member .member-information > .inner {
  width: 100%;
}

.top-detail-member .agency-location {
  font-size: 15px;
  margin-top: 10px;
}

.top-detail-member .property-job {
  font-size: 15px;
  font-weight: 600;
  margin-top: 10px;
}

.top-detail-member .member-metas {
  font-size: 15px;
  line-height: 1.1;
  margin-top: 10px;
}

@media (min-width: 768px) {
  .top-detail-member .member-metas {
    margin-top: 18px;
  }
}

.top-detail-member .member-metas > div {
  margin-bottom: 5px;
  margin-right: 12px;
  padding-right: 12px;
  border-right: 1px solid #E9E9E9;
}

.top-detail-member .member-metas > div:last-child {
  border: 0;
  padding: 0;
  margin-right: 0;
}

.top-detail-member .member-metas .with-icon {
  margin-right: 2px;
  display: inline-block;
  vertical-align: middle;
}

.top-detail-member .member-title {
  font-size: 20px;
  margin: 0;
}

@media (min-width: 1200px) {
  .top-detail-member .member-title {
    font-size: 30px;
  }
}

.top-detail-member .agency-socials {
  font-size: 12px;
  margin-top: 10px;
}

.top-detail-member .agency-socials a {
  margin-right: 20px;
  display: inline-block;
}

.top-detail-member .agency-socials a:hover, .top-detail-member .agency-socials a:focus {
  color: var(--homez-theme-color);
}

.agency-detail-tabs,
.agent-detail-tabs {
  margin-top: 1.875rem;
}

.member-thumbnail-wrapper {
  position: relative;
  text-align: center;
}

.member-thumbnail-wrapper img {
  border-radius: 8px;
}

.member-thumbnail-wrapper .nb-property {
  position: absolute;
  top: 20px;
  left: 20px;
  display: inline-block;
  padding: 2px 10px;
  border-radius: 8px;
  background-color: var(--homez-theme-color);
  color: #fff;
  font-size: 12px;
  font-weight: 500;
}

@media (min-width: 1200px) {
  .member-thumbnail-wrapper .nb-property {
    padding: 4px 15px;
  }
}

.nav-member {
  margin-bottom: 0.9375rem;
  border: 0 !important;
  position: relative;
}

@media (min-width: 1200px) {
  .nav-member {
    margin-bottom: 1.875rem;
  }
}

.nav-member > li {
  margin-bottom: 0 !important;
  margin-right: 20px;
}

@media (min-width: 1200px) {
  .nav-member > li {
    margin-right: 50px;
  }
}

.nav-member > li > a {
  padding: 0 0 5px;
  font-size: 15px;
  font-weight: 600;
  background-color: transparent !important;
  border: 0 !important;
  position: relative;
  display: inline-block;
  text-transform: capitalize;
  color: var(--homez-text-color);
}

@media (min-width: 1200px) {
  .nav-member > li > a {
    font-size: 18px;
  }
}

.nav-member > li > a:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: var(--homez-link-color);
  display: block;
  width: 0;
  height: 2px;
}

.nav-member > li:hover > a, .nav-member > li.active > a, .nav-member > li:focus > a {
  color: var(--homez-link-color) !important;
}

.nav-member > li:hover > a:before, .nav-member > li.active > a:before, .nav-member > li:focus > a:before {
  width: 100%;
}

.location-single-member {
  margin-top: 20px;
}

.location-single-member .title-wrapper {
  margin: 0 0 20px;
}

.location-single-member .title-wrapper .title {
  margin: 0 !important;
  font-size: 18px;
}

.single-map-agency,
.single-map-agent {
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  height: 250px;
}

@media (min-width: 1200px) {
  .single-map-agency,
  .single-map-agent {
    height: 380px;
  }
}

.wp-realestate-mfp-container .saved-search-form-wrapper {
  position: relative;
  background: #fff;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  border-radius: 8px;
  padding: 20px;
}

@media (min-width: 1200px) {
  .wp-realestate-mfp-container .saved-search-form-wrapper {
    padding: 1.875rem;
  }
}

.wp-realestate-mfp-container .saved-search-form-wrapper .mfp-close {
  color: #006c70;
}

.wp-realestate-mfp-container .saved-search-form-wrapper .mfp-close:hover, .wp-realestate-mfp-container .saved-search-form-wrapper .mfp-close:focus {
  color: #dc3545;
}

.properties-display-mode-wrapper label {
  font-weight: 400;
  margin-bottom: 0;
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  margin-right: 15px;
  padding-right: 15px;
  border-right: 1px solid #E9E9E9;
}

.properties-display-mode-wrapper label [type="radio"] {
  display: none;
}

.properties-display-mode-wrapper label input:checked + span {
  font-weight: 500;
  text-decoration: underline;
}

.properties-display-mode-wrapper label:last-child {
  margin-right: 0;
  border-right: 0;
  padding-right: 0;
}

.my-properties-ordering .label,
.sort-my-properties-form .label,
.sort-properties-favorite-form .label,
.properties-ordering .label {
  color: #717171;
  font-size: 0.875rem;
  padding: 0;
  display: inline-block;
  font-weight: normal;
  vertical-align: middle;
  white-space: nowrap;
}

.my-properties-ordering .select2-container--default.select2-container,
.sort-my-properties-form .select2-container--default.select2-container,
.sort-properties-favorite-form .select2-container--default.select2-container,
.properties-ordering .select2-container--default.select2-container {
  min-width: 0;
}

.my-properties-ordering .select2-container--default.select2-container .select2-selection--single,
.sort-my-properties-form .select2-container--default.select2-container .select2-selection--single,
.sort-properties-favorite-form .select2-container--default.select2-container .select2-selection--single,
.properties-ordering .select2-container--default.select2-container .select2-selection--single {
  border: 0 !important;
  height: auto;
  padding: 0;
  color: var(--homez-text-color);
  background: transparent;
}

.my-properties-ordering .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered,
.sort-my-properties-form .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered,
.sort-properties-favorite-form .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered,
.properties-ordering .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered {
  padding-top: 0;
  padding-bottom: 0;
}

.my-properties-ordering .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow,
.sort-my-properties-form .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow,
.sort-properties-favorite-form .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow,
.properties-ordering .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow {
  top: 1px;
  right: 1px;
}

.my-properties-ordering .text-sort,
.sort-my-properties-form .text-sort,
.sort-properties-favorite-form .text-sort,
.properties-ordering .text-sort {
  margin-right: 5px;
}

.search-orderby-wrapper {
  margin-bottom: 30px;
  margin-top: 20px;
}

@media (min-width: 1200px) {
  .search-orderby-wrapper {
    margin-top: 30px;
    margin-bottom: 50px;
  }
}

.search-orderby-wrapper .title-profile {
  margin: 0;
}

.search-orderby-wrapper .sortby-form {
  margin-left: 20px;
}

@media (min-width: 1200px) {
  .search-orderby-wrapper .sortby-form {
    margin-left: 30px;
  }
}

.search-orderby-wrapper .orderby-wrapper {
  padding: 11px 20px;
  background-color: #fff;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
}

.slick-carousel-gallery-properties .slick-carousel {
  margin: 0;
}

.slick-carousel-gallery-properties .slick-carousel .slick-slide,
.slick-carousel-gallery-properties .slick-carousel .slick-list {
  padding: 0 !important;
}

.slick-carousel-gallery-properties .slick-carousel .slick-arrow {
  color: #fff !important;
  background-color: transparent !important;
  border: 0 !important;
}

.action-item [class*="btn"] {
  overflow: hidden;
  width: 35px;
  height: 35px;
  color: var(--homez-link-color);
  background: #fff;
  font-size: 15px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: relative;
  border-radius: 6px;
  margin-right: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-item [class*="btn"]:last-child {
  margin-right: 0 !important;
}

.action-item [class*="btn"].loading {
  background-color: #F7F7F7;
}

.action-item [class*="btn"].loading i:before {
  font-size: 13px;
  display: inline-block;
  vertical-align: top;
  content: '\f110' !important;
  font-family: 'Font Awesome 5 Free' !important;
  font-weight: 900;
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.action-item [class*="btn"][class*="added"], .action-item [class*="btn"][class*="remove"] {
  background-color: #F7F7F7;
}

.action-item [class*="btn"][class*="added"]:hover i:before, .action-item [class*="btn"][class*="remove"]:hover i:before {
  font-size: 13px;
  content: "\e646";
  font-family: 'themify';
}

.action-item [class*="btn"]:hover, .action-item [class*="btn"]:focus {
  background-color: #F7F7F7;
}

.property-item {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border-radius: 8px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .property-item {
    border-radius: 12px;
    margin-bottom: 1.875rem;
  }
}

.property-item.images-loading .property-image:after {
  font-size: 20px;
  color: #fff;
  display: block;
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  margin-top: -17px;
  margin-left: -17px;
  position: absolute;
  top: 50%;
  left: 50%;
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  animation: rotate_icon 1200ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1200ms linear 0s normal none infinite running;
  z-index: 2;
  opacity: 0.8;
  filter: alpha(opacity=80);
}

.property-item .action-item .tooltip {
  white-space: nowrap;
}

.property-item .property-title {
  font-size: 15px;
  margin: 0;
}

.property-item .title-wrapper {
  margin-bottom: 5px;
}

.property-item .title-wrapper .property-title {
  margin-bottom: 0;
}

.property-item .property-image {
  display: block;
  position: relative;
  overflow: hidden;
}

.property-item .property-image img {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.property-item .property-image:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--homez-second-color);
  opacity: 0.1;
  filter: alpha(opacity=10);
  z-index: 1;
  display: block;
}

.property-item .property-thumbnail-wrapper,
.property-item .property-thumbnail-wrapper .image-thumbnail {
  position: relative;
}

.property-item .property-thumbnail-wrapper .top-label {
  padding: 20px;
  top: 0;
  left: 0;
  width: 100%;
  position: absolute;
  z-index: 1;
}

.property-item .property-thumbnail-wrapper .top-label > * {
  margin-right: 15px;
}

.property-item .property-thumbnail-wrapper .top-label > *:last-child {
  margin-right: 0;
}

.property-item .property-thumbnail-wrapper .bottom-label {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: absolute;
  z-index: 1;
  color: #fff;
  padding: 20px;
  bottom: 0;
  left: 0;
  width: 100%;
}

.property-item .property-thumbnail-wrapper .avatar-wrapper {
  position: absolute;
  z-index: 1;
  right: 20px;
  bottom: 0;
  -webkit-transform: translateY(50%);
  -ms-transform: translateY(50%);
  -o-transform: translateY(50%);
  transform: translateY(50%);
}

.property-item .property-thumbnail-wrapper .property-price {
  display: inline-block;
  padding: 5px 15px;
  background: #fff;
  border-radius: 6px;
}

.property-item .property-thumbnail-wrapper .btn-action-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 2;
}

.property-item .property-thumbnail-wrapper .action-item {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
}

.property-item .property-thumbnail-wrapper .action-item [class*="btn"] {
  border-radius: 0;
  color: #fff;
  background: rgba(24, 26, 32, 0.7);
}

.property-item .property-thumbnail-wrapper .action-item [class*="btn"]:hover, .property-item .property-thumbnail-wrapper .action-item [class*="btn"]:focus {
  background-color: var(--homez-second-color);
  color: #fff;
}

.property-item .property-thumbnail-wrapper .action-item [class*="btn"][class*="added"], .property-item .property-thumbnail-wrapper .action-item [class*="btn"][class*="remove"] {
  background-color: #EB6753;
  color: #fff;
}

.property-item .slick-carousel-gallery-properties {
  -webkit-transition: all 0.1s ease-in-out 0s;
  -o-transition: all 0.1s ease-in-out 0s;
  transition: all 0.1s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.property-item .slick-carousel-gallery-properties.active {
  opacity: 1;
  filter: alpha(opacity=100);
}

.property-item .slick-carousel-gallery-properties .slick-carousel .slick-arrow {
  opacity: 0;
  filter: alpha(opacity=0);
  font-size: 12px;
}

.property-item .slick-carousel-gallery-properties .slick-carousel .slick-prev {
  margin-left: -20px;
}

.property-item .slick-carousel-gallery-properties .slick-carousel .slick-next {
  margin-right: -20px;
}

.property-item .top-info {
  font-size: 13px;
}

.property-item .property-price {
  font-size: 15px;
  font-weight: 600;
  color: var(--homez-link-color);
}

.property-item .property-price .suffix-text {
  font-weight: 400;
}

.property-item .property-location a {
  color: #717171;
}

.property-item .property-location a:hover {
  color: var(--homez-link-color);
}

.property-item:hover .slick-carousel-gallery-properties .slick-carousel .slick-arrow {
  opacity: 1;
  filter: alpha(opacity=100);
}

.property-item:hover .slick-carousel-gallery-properties .slick-carousel .slick-arrow {
  margin: 0 !important;
}

.property-item:hover .property-thumbnail-wrapper .action-item {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.property-metas i {
  vertical-align: middle;
  font-size: 1rem;
  margin-right: 3px;
}

.property-metas > div {
  margin-right: 12px;
}

@media (min-width: 1200px) {
  .property-metas > div {
    margin-right: 20px;
  }
}

.property-metas > div:last-child {
  margin-right: 0 !important;
}

.property-grid {
  background-color: #fff;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

.property-grid .top-info {
  padding: 20px 20px 0;
}

.property-grid .property-location {
  margin-top: 5px;
}

.property-grid .property-metas {
  margin-top: 7px;
}

.property-grid .property-metas-bottom {
  border-top: 1px solid #E9E9E9;
  padding: 10px 0;
  margin-top: 13px;
}

.property-grid:hover {
  -webkit-box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
  box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
}

.property-grid.v1 {
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #E9E9E9;
}

.property-grid.v1:hover {
  -webkit-box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
  box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
}

.property-grid.v2 {
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

.property-grid.v2:hover {
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.1);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.1);
}

.property-grid.v3 {
  background: transparent;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  border-radius: 0;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.property-grid.v3 .image-thumbnail {
  border-radius: 6px;
  overflow: hidden;
}

.property-grid.v3 .top-info {
  padding: 20px 0 0;
}

.property-grid.v7 {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
}

.property-grid.v7 .top-info {
  padding: 20px;
}

.property-grid.v7 .info-bottom {
  margin-top: 10px;
}

.property-grid.v7 .property-price {
  padding: 4px 15px;
  background: var(--homez-link-color);
  color: #fff;
}

.property-grid.v7 .property-price .suffix-text {
  display: none;
}

.property-grid.v7:hover {
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

.property-grid.v9 .property-image:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background-image: linear-gradient(to bottom, transparent, #181A20);
  background-color: transparent;
  opacity: 0.7;
  filter: alpha(opacity=70);
}

.property-grid.v9 .property-metas {
  margin-top: 10px;
}

.property-grid.v9 .top-info {
  padding: 20px 20px 18px;
}

.property-grid.v9 .property-thumbnail-wrapper .property-price {
  background: transparent;
  color: #fff;
  padding: 0;
  border-radius: 0;
}

.property-grid.v9 .property-thumbnail-wrapper .action-item [class*="btn"] {
  border-radius: 6px;
}

.property-grid.v10 .property-title {
  font-size: 18px;
}

@media (min-width: 1200px) {
  .property-grid.v10 .property-title {
    font-size: 20px;
  }
  .property-grid.v10 .top-info {
    padding: 1.875rem 1.875rem 10px;
  }
  .property-grid.v10 .property-metas,
  .property-grid.v10 .property-location {
    margin-top: 10px;
  }
  .property-grid.v10 .property-thumbnail-wrapper .bottom-label,
  .property-grid.v10 .property-thumbnail-wrapper .top-label {
    padding: 1.875rem;
  }
  .property-grid.v10 .property-thumbnail-wrapper .avatar-wrapper {
    right: 1.875rem;
  }
}

.property-grid-v4 {
  border-radius: 0;
}

.property-grid-v4 .image-thumbnail {
  border-radius: 6px;
  overflow: hidden;
}

.property-grid-v4 .top-info {
  padding: 12px 0 0;
}

.property-grid-v4 .property-price {
  margin-bottom: 5px;
}

.property-grid-v4 .property-location,
.property-grid-v4 .property-metas {
  margin-top: 3px;
}

.property-grid-v4 .property-thumbnail-wrapper .action-item [class*="btn"] {
  border-radius: 6px;
}

.property-grid-v5 {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.property-grid-v5 .bottom-info {
  position: absolute;
  z-index: 1;
  background: #fff;
  left: 10px;
  right: 10px;
  bottom: 10px;
  padding: 18px 18px 15px;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .property-grid-v5 .bottom-info {
    border-radius: 12px;
  }
}

.property-grid-v5 .bottom-info .property-title {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.property-grid-v5 .bottom-info .property-location {
  margin-top: 3px;
}

.property-grid-v5 .bottom-info .ms-auto {
  padding-left: 5px;
}

.property-grid-v5 .bottom-info .property-price {
  padding: 4px 15px;
  border: 1px solid var(--homez-link-color);
  border-radius: 6px;
}

.property-grid-v5 .bottom-info .property-price .suffix-text {
  display: none;
}

.property-grid-v5 .property-thumbnail-wrapper .action-item {
  right: 10px;
  top: 20px;
  bottom: inherit;
  flex-direction: column;
}

.property-grid-v5 .property-thumbnail-wrapper .action-item [class*="btn"] {
  border-radius: 6px;
  margin: 0 0 5px;
}

.property-grid-v5 .property-image::before,
.property-grid-v5 .slick-carousel-gallery-properties {
  display: none !important;
}

.property-grid-v5:hover {
  -webkit-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  -o-transform: translateY(-10px);
  transform: translateY(-10px);
}

.property-grid-v6 {
  background: #fff;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

.property-grid-v6 .top-info {
  padding: 20px 20px 15px;
}

.property-grid-v6 .property-price {
  color: var(--homez-theme-color);
}

.property-grid-v6 .info-bottom {
  margin-top: 7px;
}

.property-grid-v6 .property-title {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.property-grid-v6 .property-thumbnail-wrapper .action-item [class*="btn"] {
  border-radius: 6px;
  color: var(--homez-link-color);
  background: #F7F7F7;
}

.property-grid-v6 .property-thumbnail-wrapper .action-item [class*="btn"]:hover, .property-grid-v6 .property-thumbnail-wrapper .action-item [class*="btn"]:focus {
  background-color: #eaeaea;
  color: var(--homez-link-color);
}

.property-grid-v6 .property-thumbnail-wrapper .action-item [class*="btn"][class*="added"], .property-grid-v6 .property-thumbnail-wrapper .action-item [class*="btn"][class*="remove"] {
  background-color: #EB6753;
  color: #fff;
}

.property-grid-v6:hover {
  -webkit-box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
  box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
}

.property-grid-v8 {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.property-grid-v8 .property-image:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background-image: linear-gradient(to bottom, rgba(24, 26, 32, 0), #181A20);
  background-color: transparent;
  opacity: 0.7;
  filter: alpha(opacity=70);
}

.property-grid-v8 .bottom-info {
  position: absolute;
  padding: 20px;
  z-index: 1;
  bottom: 0;
  left: 0;
  width: 100%;
}

.property-grid-v8 .bottom-info, .property-grid-v8 .bottom-info a {
  color: #fff;
}

.property-grid-v8 .bottom-info .property-price {
  color: #fff;
  background: transparent;
  padding: 0;
  border-radius: 0;
  margin-bottom: 5px;
}

.property-grid-v8 .property-metas {
  margin-top: 7px;
}

.property-grid-v8:hover {
  -webkit-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  -o-transform: translateY(-10px);
  transform: translateY(-10px);
}

.property-list {
  background: #fff;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

@media (min-width: 576px) {
  .property-list .property-thumbnail-wrapper {
    width: 280px;
  }
}

.property-list .property-thumbnail-wrapper + .top-info {
  padding: 0.9375rem;
}

@media (min-width: 1200px) {
  .property-list .property-thumbnail-wrapper + .top-info {
    padding: 15px 1.875rem;
  }
}

.property-list .property-title {
  font-size: 17px;
}

.property-list .property-location {
  margin-top: 5px;
}

.property-list .description,
.property-list .property-metas {
  margin-top: 10px;
}

.property-list .property-metas-bottom {
  border-top: 1px solid #E9E9E9;
  padding-top: 10px;
  margin-top: 15px;
}

.property-list:hover {
  -webkit-box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
  box-shadow: 0 2px 12px 0 rgba(24, 26, 32, 0.09);
}

.property-list-special {
  overflow: hidden;
}

.property-list-special .property-metas {
  margin-top: 25px;
}

.property-list-special .property-metas .property-meta {
  text-align: center;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .property-list-special .property-metas .property-meta {
    margin-right: 30px;
  }
}

.property-list-special .property-metas .property-meta:last-child {
  margin-right: 0;
}

.property-list-special .property-metas i {
  line-height: 1;
  font-size: 20px;
}

.property-list-special .property-metas .value-suffix {
  display: block;
}

.property-list-special .property-title {
  margin-top: 20px;
}

@media (min-width: 1200px) {
  .property-list-special .property-title {
    font-size: 22px;
    margin: 40px 0 10px;
  }
}

.property-list-special .description {
  margin-top: 25px;
}

.property-list-special .property-metas-bottom {
  padding-top: 15px;
  margin-top: auto;
}

@media (min-width: 1200px) {
  .property-list-special .property-price {
    font-size: 22px;
  }
}

.property-list-special .left-inner {
  width: 100%;
  padding: 20px;
}

@media (min-width: 992px) {
  .property-list-special .left-inner {
    width: 40%;
  }
}

@media (min-width: 1200px) {
  .property-list-special .left-inner {
    padding: 50px;
  }
}

.property-list-special .right-inner {
  width: 60%;
}

@media (min-width: 1200px) {
  .property-list-special .property-thumbnail-wrapper .bottom-label {
    padding: 0 50px;
    bottom: 45px;
  }
}

.property-list-member {
  padding: 0;
  overflow: hidden;
  border-radius: 8px;
}

.property-list-member .property-thumbnail-wrapper {
  border-radius: 0;
}

.property-list-simple {
  border-radius: 0;
}

.property-list-simple .property-thumbnail-wrapper .btn-action-icon {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8) !important;
  color: #dc3545 !important;
  font-size: 25px;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
}

.property-list-simple .property-thumbnail-wrapper .btn-action-icon.loading {
  color: transparent;
}

.property-list-simple:hover .property-thumbnail-wrapper .btn-action-icon {
  opacity: 1;
  filter: alpha(opacity=100);
}

.property-list-simple .property-thumbnail-wrapper {
  position: relative;
  width: 90px;
  height: 80px;
  overflow: hidden;
  border-radius: 6px;
}

.property-list-simple .property-thumbnail-wrapper + .property-information {
  padding-left: 15px;
}

.property-list-simple .property-title {
  font-size: 14px;
  font-weight: 400;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.property-list-simple .property-metas {
  margin-top: 4px;
  font-size: 13px;
}

.property-list-simple .property-metas > div {
  margin-right: 12px;
}

.property-list-simple .property-metas > div:last-child {
  margin-right: 0;
}

.properties-list-simple article {
  margin-bottom: 20px;
}

.properties-list-simple article:last-child {
  margin-bottom: 0;
}

.status-property {
  display: inline-block;
  padding: 5px 18px;
  border-radius: 36px;
}

.status-property.publish {
  color: #3554D1;
  background: #E5F0FD;
}

.status-property.preview, .status-property.pending, .status-property.pending_payment, .status-property.draft {
  color: #E4B303;
  background: #FFF8DD;
}

.status-property.expired {
  color: #F1416C;
  background: #FFF5F8;
}

.my-properties-item {
  padding: 0 15px 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #E9E9E9;
  border-width: 0 0 1px;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

@media (min-width: 1200px) {
  .my-properties-item {
    margin-bottom: 1.875rem;
    padding: 0 1.875rem 1.875rem;
  }
}

.my-properties-item:last-child {
  border-bottom: 0;
  padding-bottom: 0;
  margin-bottom: 0;
}

.my-properties-item .property-table-info-content-expiry {
  color: #dc3545;
}

.my-properties-item .property-price {
  margin-top: 3px;
}

@media (max-width: 767px) {
  .my-properties-item .warpper-action-property {
    padding-top: 10px;
  }
}

.layout-my-properties {
  width: 100%;
}

.layout-my-properties.header-layout {
  background-color: #F7F7F7;
  padding: 11px 15px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  color: var(--homez-link-color);
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .layout-my-properties.header-layout {
    padding: 10px 1.875rem;
    margin-bottom: 1.875rem;
    border-radius: 12px;
  }
}

.layout-my-properties .property-thumbnail-wrapper {
  position: relative;
  width: 110px;
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .layout-my-properties .property-thumbnail-wrapper {
    border-radius: 12px;
  }
}

.layout-my-properties .inner-info {
  width: calc(100% - 110px);
  padding-left: 0.9375rem;
}

@media (min-width: 1200px) {
  .layout-my-properties .inner-info {
    padding-left: 1.875rem;
  }
}

.layout-my-properties .layout-left > div {
  width: 20%;
  padding: 5px;
}

.layout-my-properties .layout-left .inner-info-left {
  width: 100%;
}

@media (min-width: 768px) {
  .layout-my-properties .layout-left .inner-info-left {
    width: 40%;
  }
}

.layout-my-properties .warpper-action-property {
  white-space: nowrap;
}

.property-banner-inner {
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
  background-position: center;
  color: #fff;
  padding: 40px 15px;
  position: relative;
  z-index: 1;
}

@media (min-width: 768px) {
  .property-banner-inner {
    padding: 50px 15px;
  }
}

@media (min-width: 1200px) {
  .property-banner-inner {
    padding: 180px 15px;
  }
}

.property-banner-inner:before {
  content: '';
  z-index: -1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(13, 38, 59, 0.4);
}

.property-banner-inner .tagline {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.property-banner-inner .title {
  color: #fff;
  font-size: 25px;
}

@media (min-width: 1200px) {
  .property-banner-inner .title {
    font-size: 30px;
    margin-bottom: 25px;
  }
}

.property-banner-inner .property-metas {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .property-banner-inner .property-metas {
    margin-bottom: 35px;
  }
}

.property-banner-inner .property-metas .property-meta {
  margin: 0 15px;
}

.property-banner-inner .property-metas i {
  line-height: 1;
  font-size: 20px;
}

.property-banner-inner .property-metas .value-suffix {
  display: block;
}

.property-banner-inner > * {
  margin: 0 0 10px;
}

.property-banner-inner > *:last-child {
  margin: 0;
}

.nav-tabs.tabs-properties {
  border: 0;
}

.nav-tabs.tabs-properties > li {
  margin-right: 10px;
}

@media (max-width: 767px) {
  .nav-tabs.tabs-properties > li {
    margin-bottom: 5px;
  }
}

.nav-tabs.tabs-properties > li:last-child {
  margin-right: 0;
}

.nav-tabs.tabs-properties > li > a {
  display: inline-block;
  border-radius: 6px;
  padding: 4px 12px;
  border: 1px solid var(--homez-link-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  text-decoration: none !important;
}

@media (min-width: 768px) {
  .nav-tabs.tabs-properties > li > a {
    padding: 6px 20px;
  }
}

.nav-tabs.tabs-properties > li > a.active {
  color: #fff;
  border-color: var(--homez-link-color);
  background: var(--homez-link-color);
}

.widget-properties .property-grid-v4 .property-metas > div,
.widget-properties-tabs .property-grid-v4 .property-metas > div {
  margin-right: 9px;
}

@media (min-width: 1200px) {
  .widget-properties .bottom-remore,
  .widget-properties-tabs .bottom-remore {
    margin-top: 1.875rem;
  }
}

.property-grid-slider .title {
  font-size: 20px;
  margin: 0;
}

.property-grid-slider .property-location {
  font-size: 15px;
}

.property-grid-slider .property-metas {
  font-size: 15px;
  font-weight: 600;
  color: var(--homez-link-color);
  margin-top: 15px;
}

@media (min-width: 1200px) {
  .property-grid-slider .property-metas {
    margin-top: 30px;
  }
}

.property-grid-slider .property-metas > div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.property-grid-slider .property-metas i {
  margin-right: 10px;
  -webkit-box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  box-shadow: 0 6px 15px 0 rgba(64, 79, 104, 0.05);
  width: 35px;
  height: 35px;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  background: #fff;
  color: var(--homez-link-color);
}

@media (min-width: 1200px) {
  .property-grid-slider .property-metas i {
    font-size: 20px;
    width: 50px;
    height: 50px;
    border-radius: 12px;
  }
}

.property-grid-slider .property-price {
  font-size: 20px;
}

.property-grid-slider .status-property-label {
  line-height: 30px;
  display: inline-block;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #fff;
  background-color: #0D1C39;
  border-radius: 6px;
  white-space: nowrap;
  text-decoration: none;
}

@media (min-width: 1200px) {
  .property-grid-slider .status-property-label {
    padding: 0 12px;
  }
}

.property-grid-slider .top-label > * {
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .property-grid-slider .top-label > * {
    margin-right: 20px;
  }
}

.property-grid-slider .inner-content {
  border-radius: 8px 0 0 8px;
  padding: 20px;
  background: #F4EFF7;
}

@media (min-width: 1200px) {
  .property-grid-slider .inner-content {
    padding: 90px 60px;
    border-radius: 12px 0 0 12px;
  }
}

.property-grid-slider .information {
  max-width: 420px;
}

.property-grid-slider .banner-image img {
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .property-grid-slider .banner-image img {
    border-radius: 12px;
  }
}

.property-grid-slider .inner-middle {
  margin-top: 20px;
}

@media (min-width: 1200px) {
  .property-grid-slider .inner-middle {
    margin-top: 35px;
  }
}

.property-grid-slider .inner-middle .item1 + .item1 {
  padding-left: 15px;
}

@media (min-width: 1200px) {
  .property-grid-slider .inner-middle .item1 + .item1 {
    padding-left: 1.875rem;
  }
}

.property-grid-slider .inner-middle .value {
  font-weight: 600;
  margin-top: 3px;
}

.property-grid-slider .top-label {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .property-grid-slider .top-label {
    margin-bottom: 25px;
  }
}

.property-grid-slider .inner-bottom {
  margin-top: 15px;
}

@media (min-width: 1200px) {
  .property-grid-slider .inner-bottom {
    margin-top: 25px;
  }
}

.property-grid-slider .action-item [class*="btn"] {
  border-radius: 50%;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .property-grid-slider .action-item [class*="btn"] {
    width: 40px;
    height: 40px;
  }
}

@media (min-width: 1200px) {
  .widget-properties-slider.style1 .slick-carousel .slick-dots {
    padding: 0 75px;
    text-align: left;
    margin-top: -20px;
  }
}

.widget-properties-slider.style2 .slick-carousel .slick-dots {
  padding: 0 55px;
  width: 300px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

@media (min-width: 1200px) {
  .widget-properties-slider.style2 .slick-carousel .slick-dots {
    padding: 0 95px;
    width: 500px;
  }
}

.widget-properties-slider.style2 .slick-carousel .slick-dots li {
  position: relative;
  margin: 0;
  width: 100%;
  width: 100%;
  height: 1px;
  background: rgba(24, 26, 32, 0.3);
}

.widget-properties-slider.style2 .slick-carousel .slick-dots li:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10px;
}

.widget-properties-slider.style2 .slick-carousel .slick-dots li:before {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  content: '';
  position: absolute;
  background: #181A20;
  width: 0;
  height: 3px;
  top: -1px;
  left: 0;
  z-index: 1;
}

.widget-properties-slider.style2 .slick-carousel .slick-dots li button {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  background: transparent !important;
}

.widget-properties-slider.style2 .slick-carousel .slick-dots li.slick-active:before {
  width: 100%;
}

.widget-properties-slider.style2 .slick-carousel .slick-dots li:first-child button {
  text-indent: 0;
  margin: 0 10px;
  position: absolute;
  top: -6px;
  right: 100%;
}

.widget-properties-slider.style2 .slick-carousel .slick-dots li:last-child button {
  text-indent: 0;
  margin: 0 10px;
  position: absolute;
  top: -6px;
  left: 100%;
}

.tabs-account.elementor-tabs-view-horizontal {
  max-width: 410px;
  margin: auto;
  background: #fff;
}

.tabs-account.elementor-tabs-view-horizontal .elementor-tabs-wrapper {
  border-bottom: 1px solid #e7ecea;
  display: flex;
  display: -webkit-flex;
}

.tabs-account.elementor-tabs-view-horizontal .elementor-tab-desktop-title {
  width: 50%;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--homez-link-color);
  border: 0 !important;
  margin: 0;
  position: relative;
  padding: 19px 25px;
  text-align: center;
}

.tabs-account.elementor-tabs-view-horizontal .elementor-tab-desktop-title:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
  background: var(--homez-theme-color);
  -webkit-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
  display: block;
  border: 0 !important;
}

.tabs-account.elementor-tabs-view-horizontal .elementor-tab-desktop-title:after {
  display: none !important;
}

.tabs-account.elementor-tabs-view-horizontal .elementor-tab-desktop-title.elementor-active {
  color: var(--homez-theme-color);
}

.tabs-account.elementor-tabs-view-horizontal .elementor-tab-desktop-title.elementor-active:before {
  -webkit-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
}

.tabs-account.elementor-tabs-view-horizontal .elementor-tab-content {
  border: 0 !important;
  padding: 25px 30px 30px;
}

@media (max-width: 767px) {
  .tabs-account.elementor-tabs-view-horizontal .elementor-tabs-content-wrapper {
    border: 0 !important;
  }
  .tabs-account.elementor-tabs-view-horizontal .elementor-tabs-content-wrapper .elementor-tab-title {
    display: none !important;
  }
}

.nav-detail-center {
  border-bottom: 1px solid #E9E9E9;
  padding: 0 20px;
  margin: 0;
  overflow: auto;
  scrollbar-width: thin;
}

@media (min-width: 1200px) {
  .nav-detail-center {
    padding: 0 1.875rem;
  }
}

.nav-detail-center > li {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .nav-detail-center > li {
    margin-right: 10px;
  }
}

.nav-detail-center > li > a {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: capitalize;
  padding: 10px;
  display: inline-block;
  white-space: nowrap;
  background-color: transparent !important;
  color: #717171;
  border: 0 !important;
  margin: 0 !important;
  border-radius: 0;
  position: relative;
  text-decoration: none;
}

@media (min-width: 1200px) {
  .nav-detail-center > li > a {
    padding: 15px;
  }
}

.nav-detail-center > li > a:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--homez-link-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.nav-detail-center > li > a.active, .nav-detail-center > li > a:hover {
  color: var(--homez-link-color);
}

.nav-detail-center > li > a.active:before, .nav-detail-center > li > a:hover:before {
  width: 100%;
}

.wrapper-tab-v9 {
  margin-bottom: 1.875rem;
  background: #fff;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .wrapper-tab-v9 {
    border-radius: 12px;
  }
}

.wrapper-tab-v9 > .tab-content {
  padding: 20px;
}

@media (min-width: 1200px) {
  .wrapper-tab-v9 > .tab-content {
    padding: 1.875rem;
  }
}

.wrapper-tab-v9 h3,
.wrapper-tab-v9 .title {
  font-size: 17px;
  margin: 0 0 20px;
}

.buttons-group-center {
  padding: 15px 0;
  text-align: center;
}

@media (min-width: 1200px) {
  .buttons-group-center {
    padding: 1.875rem 0;
  }
}

.buttons-group-center [class|="btn"] {
  display: inline-block;
}

.buttons-group-center [class|="btn"] i {
  display: inline-block;
  position: relative;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 18px;
  background-color: #e3e3e3;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

@media (min-width: 1200px) {
  .buttons-group-center [class|="btn"] i {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 21px;
  }
}

.buttons-group-center [class|="btn"] i:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  color: var(--homez-text-color);
  content: '\f110';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 50%;
}

.buttons-group-center [class|="btn"] > span {
  display: block;
  margin-top: 8px;
}

.buttons-group-center [class|="btn"][class*="added"]:hover i:before, .buttons-group-center [class|="btn"][class*="remove"]:hover i:before {
  vertical-align: top;
  font-size: 14px;
  content: "\f103";
  font-family: 'Flaticon';
}

.buttons-group-center [class|="btn"][class*="added"] i, .buttons-group-center [class|="btn"][class*="remove"] i, .buttons-group-center [class|="btn"]:hover i, .buttons-group-center [class|="btn"]:focus i {
  color: #fff;
  background-color: var(--homez-theme-color);
}

.buttons-group-center [class|="btn"].loading i {
  color: transparent !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
}

.buttons-group-center [class|="btn"].loading i:after {
  opacity: 0.7;
  filter: alpha(opacity=70);
  animation: rotate_icon 1500ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1500ms linear 0s normal none infinite running;
}

.buttons-group-center > div {
  padding: 0 10px;
}

@media (min-width: 1200px) {
  .buttons-group-center > div {
    padding: 0 25px;
  }
}

.property-detail-gallery {
  position: relative;
}

.property-detail-gallery .p-popup-image {
  display: block;
  position: relative;
  overflow: hidden;
  margin-bottom: 1.875rem;
}

.property-detail-gallery .p-popup-image img {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.property-detail-gallery .p-popup-image:before {
  content: '';
  width: 100%;
  height: 100%;
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(24, 26, 32, 0.2);
  display: block;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
}

.property-detail-gallery .p-popup-image:hover:before, .property-detail-gallery .p-popup-image:focus:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

.property-detail-gallery .p-popup-image:hover img, .property-detail-gallery .p-popup-image:focus img {
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.property-detail-gallery .p-popup-image .view-more-gallery {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 2;
  padding: 7px 20px;
  font-weight: 600;
  color: var(--homez-link-color);
  background: #fff;
  border-radius: 6px;
}

@media (max-width: 767px) {
  .property-detail-gallery .p-popup-image .view-more-gallery {
    font-size: 13px;
    right: 10px;
    bottom: 10px;
    padding: 5px 15px;
  }
}

.property-detail-gallery .p-popup-image .view-more-gallery.circle {
  right: 50%;
  bottom: 50%;
  -webkit-transform: translate(50%, 50%);
  -ms-transform: translate(50%, 50%);
  -o-transform: translate(50%, 50%);
  transform: translate(50%, 50%);
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 1200px) {
  .property-detail-gallery .p-popup-image .view-more-gallery.circle {
    width: 65px;
    height: 65px;
  }
}

.property-detail-gallery .p-popup-image.view-more-image:before {
  content: '';
  width: 100%;
  height: 100%;
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(13, 38, 59, 0.5);
  display: block;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  opacity: 1;
  filter: alpha(opacity=100);
}

.property-detail-gallery .p-popup-image.view-more-image:hover .view-more-gallery {
  background-color: var(--homez-theme-color);
  color: #fff;
}

.property-detail-gallery .row-10.wrapper {
  margin-bottom: -10px;
}

.property-detail-gallery .row-10.wrapper .p-popup-image {
  margin-bottom: 10px;
}

.property-detail-gallery.v1, .property-detail-gallery.v10, .property-detail-gallery.v3, .property-detail-gallery.v2 {
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .property-detail-gallery.v1, .property-detail-gallery.v10, .property-detail-gallery.v3, .property-detail-gallery.v2 {
    border-radius: 12px;
  }
}

.property-detail-gallery.v4 .inner {
  max-width: 1170px;
  margin-left: auto;
  margin-right: auto;
}

.property-detail-gallery.v4 .slick-list {
  overflow: visible;
}

.property-detail-gallery.v4 .p-popup-image {
  margin-bottom: 0;
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .property-detail-gallery.v4 .p-popup-image {
    border-radius: 12px;
  }
}

@media (min-width: 1400px) {
  .property-detail-gallery.v4 .slick-carousel .slick-prev {
    left: -80px;
  }
  .property-detail-gallery.v4 .slick-carousel .slick-next {
    right: -80px;
  }
}

.property-detail-gallery.v5 .p-popup-image {
  margin-bottom: 0;
}

.property-detail-gallery.v5 .slick-carousel .slick-arrow {
  border-color: #fff;
  color: #fff;
  background: transparent;
}

.property-detail-gallery.v5 .slick-carousel .slick-arrow:hover, .property-detail-gallery.v5 .slick-carousel .slick-arrow:focus {
  color: #fff;
  border-color: #fff;
  background: rgba(255, 255, 255, 0.2);
}

.property-detail-gallery.v5 .slick-carousel .slick-prev {
  left: 10px;
}

@media (min-width: 1200px) {
  .property-detail-gallery.v5 .slick-carousel .slick-prev {
    left: 100px;
  }
}

.property-detail-gallery.v5 .slick-carousel .slick-next {
  right: 10px;
}

@media (min-width: 1200px) {
  .property-detail-gallery.v5 .slick-carousel .slick-next {
    right: 100px;
  }
}

.property-detail-gallery.v6 .bottom-gallery .image-wrapper,
.property-detail-gallery.v6 .p-popup-image {
  cursor: pointer;
  margin-bottom: 0;
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .property-detail-gallery.v6 .bottom-gallery .image-wrapper,
  .property-detail-gallery.v6 .p-popup-image {
    border-radius: 12px;
  }
}

.property-detail-gallery.v6 .bottom-gallery {
  margin-top: 10px;
}

.property-detail-gallery.v9 .p-popup-image {
  margin-bottom: 0;
}

@media (min-width: 1400px) {
  .property-detail-gallery.v9 .slick-carousel .slick-prev {
    left: -80px;
  }
  .property-detail-gallery.v9 .slick-carousel .slick-next {
    right: -80px;
  }
}

.social-property {
  display: inline-block;
  position: relative;
}

.social-property:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 10px;
  bottom: 100%;
  left: 0;
}

.social-property .bo-social-icons {
  display: flex;
  display: -webkit-flex;
  position: absolute;
  bottom: 100%;
  left: 50%;
  color: #fff;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  z-index: 2;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  text-align: center;
  padding: 1px 8px;
  opacity: 0;
  filter: alpha(opacity=0);
  margin-bottom: 15px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  visibility: hidden;
}

.social-property .bo-social-icons:before {
  content: '';
  position: absolute;
  display: block;
  top: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
  z-index: 2;
  border-width: 6px;
  border-style: solid;
  border-color: #24324a transparent transparent;
}

.social-property .bo-social-icons a {
  color: #fff !important;
  font-size: 11px;
  padding: 4px 6px;
  display: inline-block;
}

.social-property:hover .bo-social-icons {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}

.content-property-detail {
  margin-top: 20px;
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .content-property-detail {
    margin-top: 1.875rem;
    margin-bottom: 70px;
  }
}

@media (min-width: 768px) {
  .property-action-detail {
    text-align: right;
  }
}

.property-action-detail .action-item [class*="btn"] {
  border: 1px solid #E9E9E9;
  margin-right: 10px;
  background: transparent;
}

.property-action-detail .action-item [class*="btn"][class*="added"], .property-action-detail .action-item [class*="btn"][class*="remove"], .property-action-detail .action-item [class*="btn"]:hover, .property-action-detail .action-item [class*="btn"]:focus {
  border-color: var(--homez-link-color);
  background: #fff;
}

.property-action-detail .property-price {
  margin-top: 5px;
}

@media (min-width: 768px) {
  .property-action-detail .property-price {
    margin-top: 22px;
  }
}

.attachments-inner .attachment-item {
  margin-bottom: 10px;
  float: left;
  margin-right: 15px;
}

@media (min-width: 1200px) {
  .attachments-inner .attachment-item {
    margin-right: 30px;
  }
}

.attachments-inner i {
  margin-right: 7px;
  display: inline-block;
  font-size: 18px;
  line-height: 1;
  vertical-align: middle;
}

.attachments-inner .attachment-detail-download-url {
  font-size: 12px;
  display: inline-block;
  font-weight: 600;
  text-decoration: underline;
  color: var(--homez-theme-color);
  text-transform: uppercase;
  margin-left: 15px;
}

@media (min-width: 1200px) {
  .attachments-inner .attachment-detail-download-url {
    margin-left: 30px;
  }
}

.attachments-inner .attachment-detail-download-url:hover, .attachments-inner .attachment-detail-download-url:focus {
  text-decoration: none;
}

.attachment-item {
  display: -webkit-flex;
  /* Safari */
  -webkit-align-items: center;
  /* Safari 7.0+ */
  display: flex;
  align-items: center;
}

.attachment-item .icon_type {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 25px;
  text-align: center;
  border-radius: 8px;
  background-color: #f7f7f7;
  color: var(--homez-theme-color);
}

@media (min-width: 1200px) {
  .attachment-item .icon_type {
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 40px;
  }
}

.attachment-item .candidate-detail-attachment {
  display: block;
  width: calc(100% - 50px);
  padding-left: 20px;
}

@media (min-width: 1200px) {
  .attachment-item .candidate-detail-attachment {
    width: calc(100% - 80px);
  }
}

.attachment-item .candidate-detail-attachment i {
  color: var(--homez-theme-color);
  font-size: 18px;
  margin-right: 5px;
}

.nav-table {
  border: 0;
  padding: 0;
  margin: 0;
}

.nav-table > li {
  margin-bottom: 0 !important;
  margin-right: 5px;
}

@media (min-width: 1200px) {
  .nav-table > li {
    margin-right: 10px;
  }
}

.nav-table > li:last-child {
  margin-right: 0;
}

.nav-table > li > a {
  border: 0 !important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  padding: 0;
  margin: 0;
  text-align: center;
  font-size: 16px;
  width: 35px;
  height: 35px;
  background-color: rgba(24, 26, 32, 0.8);
  color: #fff;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .nav-table > li > a {
    border-radius: 12px;
    width: 54px;
    height: 54px;
    font-size: 20px;
  }
}

.nav-table > li > a.active, .nav-table > li > a:hover, .nav-table > li > a:focus {
  background-color: var(--homez-theme-color);
  color: #fff;
}

.tabs-gallery-map {
  position: relative;
  margin-top: 1.875rem;
}

@media (min-width: 1200px) {
  .tabs-gallery-map {
    margin-top: 60px;
  }
}

.tabs-gallery-map .nav-table {
  position: absolute;
  top: 15px;
  z-index: 2;
  right: 15px;
}

@media (min-width: 1200px) {
  .tabs-gallery-map .nav-table {
    top: 1.875rem;
    right: 1.875rem;
  }
}

.tabs-gallery-map #single-tab-property-street-view-map,
.tabs-gallery-map #properties-google-maps {
  height: 215px;
}

@media (min-width: 768px) {
  .tabs-gallery-map #single-tab-property-street-view-map,
  .tabs-gallery-map #properties-google-maps {
    height: 400px;
  }
}

@media (min-width: 1200px) {
  .tabs-gallery-map #single-tab-property-street-view-map,
  .tabs-gallery-map #properties-google-maps {
    height: 600px;
  }
}

.tabs-gallery-map .property-virtual_tour > h3 {
  display: none;
}

.tabs-gallery-map.v2 {
  margin: 0;
}

.tabs-gallery-map.v2:before {
  content: '';
  background: rgba(24, 26, 32, 0.3);
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.tabs-gallery-map.v2 .slick-carousel .slick-arrow {
  z-index: 2;
}

.tabs-gallery-map.v4 {
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .tabs-gallery-map.v4 {
    border-radius: 12px;
  }
}

@media (min-width: 1200px) {
  .tabs-gallery-map.v4 #single-tab-property-street-view-map,
  .tabs-gallery-map.v4 #properties-google-maps {
    height: 680px;
  }
}

.property-virtual_tour iframe {
  max-width: 100%;
  border: 0;
  border-radius: 8px;
  overflow: hidden;
}

@media (min-width: 1200px) {
  .property-virtual_tour iframe {
    border-radius: 12px;
  }
}

.tabs-video-virtual {
  padding-top: 5px !important;
  overflow: hidden;
}

.tabs-video-virtual .property-section > h3 {
  display: none;
}

.columns-gap {
  overflow: hidden;
  clear: both;
  color: var(--homez-link-color);
  list-style: none;
  padding: 0;
  margin: 0;
}

.columns-gap li {
  float: left;
  width: 50%;
  margin: 0 0 10px;
  color: var(--homez-link-color);
}

@media (min-width: 1200px) {
  .columns-gap li {
    margin-bottom: 15px;
    width: 33.33%;
  }
}

.columns-gap li:before {
  content: '';
  width: 5px;
  height: 5px;
  background: var(--homez-link-color);
  border-radius: 50%;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
}

.columns-gap li > div {
  display: inline;
}

.list-detail li {
  width: 100%;
  margin: 0 0 12px;
}

@media (min-width: 576px) {
  .list-detail li {
    width: 50%;
  }
}

.list-detail li .text {
  width: 150px;
  font-weight: 600;
  color: var(--homez-heading-color);
}

.list-detail li .value {
  color: var(--homez-text-color);
  font-weight: 400;
}

.list-overview li {
  width: 50%;
  margin: 0 0 12px;
}

@media (min-width: 1200px) {
  .list-overview li {
    width: 33.33%;
    margin: 0 0 22px;
  }
}

.list-overview .icon {
  border: 1px solid #E9E9E9;
  flex-shrink: 0;
  border-radius: 8px;
  width: 50px;
  height: 50px;
  font-size: 20px;
}

@media (min-width: 1200px) {
  .list-overview .icon {
    border-radius: 12px;
  }
}

.list-overview .icon + .details {
  padding-left: 10px;
}

@media (min-width: 1200px) {
  .list-overview .icon + .details {
    padding-left: 15px;
  }
}

.list-overview .details {
  color: var(--homez-link-color);
  line-height: 1.65;
}

@media (min-width: 1200px) {
  .list-overview .details {
    font-size: 15px;
  }
}

.list-overview .text {
  font-weight: 600;
}

.circle-check {
  overflow: hidden;
  clear: both;
  color: var(--homez-link-color);
  padding: 0;
  list-style: none;
  margin: 0;
}

.circle-check li {
  float: left;
  width: 50%;
  margin: 0 0 10px;
  line-height: 1;
}

@media (min-width: 1200px) {
  .circle-check li {
    width: 33%;
    margin-bottom: 18px;
  }
}

.circle-check [type="radio"],
.circle-check [type="checkbox"] {
  display: none;
}

.circle-check [type="radio"] + label,
.circle-check [type="checkbox"] + label {
  cursor: pointer;
  font-weight: 400;
  color: var(--homez-link-color);
}

.circle-check [type="radio"] + label:before,
.circle-check [type="checkbox"] + label:before {
  text-align: center;
  line-height: 14px;
  content: '';
  width: 16px;
  height: 16px;
  border-radius: 6px;
  border: 1px solid var(--homez-link-color);
  display: inline-block;
  margin-right: 10px;
  vertical-align: text-top;
  margin-top: 1px;
}

.circle-check [type="radio"] + label:before {
  border-radius: 50%;
}

.circle-check [type="radio"]:checked + label:before {
  -webkit-box-shadow: 0 0 0 2px var(--homez-link-color) inset;
  box-shadow: 0 0 0 2px var(--homez-link-color) inset;
}

.circle-check [type="checkbox"]:checked + label:before {
  background: var(--homez-link-color);
  color: #fff;
  font-size: 8px;
  content: "\f00c";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.property-detail-floor-plans .panel-group {
  margin-bottom: 0;
}

.property-detail-floor-plans .panel {
  border: 0;
}

.property-detail-floor-plans .panel + .panel {
  margin-top: 20px;
}

.property-detail-floor-plans .panel > .panel-heading {
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  background-color: #fff;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 12px;
}

@media (min-width: 768px) {
  .property-detail-floor-plans .panel > .panel-heading {
    padding: 14px 20px;
  }
}

.property-detail-floor-plans .panel > .panel-heading > a {
  width: 100%;
  display: block;
}

.property-detail-floor-plans .panel > .panel-heading h3 {
  font-size: 0.875rem;
  margin: 0;
}

.property-detail-floor-plans .panel > .panel-heading.active {
  background: #F6F8F9;
}

.property-detail-floor-plans .panel .metas {
  margin-left: auto;
  display: flex;
  display: -webkit-flex;
  color: var(--homez-text-color);
}

.property-detail-floor-plans .panel .metas > * {
  margin-left: 25px;
}

.property-detail-floor-plans .panel .subtitle {
  color: var(--homez-link-color);
}

.property-detail-floor-plans .panel .expand-icon {
  font-size: 10px;
  margin-top: 3px;
}

.property-detail-floor-plans .panel .expand-icon:before {
  content: "\f10e";
  font-family: flaticon;
}

.property-detail-floor-plans .content-item {
  padding: 12px;
}

@media (min-width: 1200px) {
  .property-detail-floor-plans .content-item {
    padding: 20px;
  }
}

.property-detail-floor-plans .content-item .content {
  margin-top: 15px;
}

.detail-metas-top {
  margin-bottom: 25px;
}

.detail-metas-top > div {
  display: inline-block;
  margin-right: 3px;
  padding: 5px 10px;
  border-radius: 8px;
  background-color: #f7f7f7;
}

@media (min-width: 1200px) {
  .detail-metas-top > div {
    padding: 5px 20px;
    margin-right: 10px;
  }
}

.detail-metas-top .type-property {
  color: var(--homez-theme-color);
}

.video-embed-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .video-embed-wrapper {
    border-radius: 12px;
  }
}

.video-embed-wrapper:before {
  padding-top: 56.25%;
  content: "";
  display: block;
}

.video-embed-wrapper iframe {
  max-width: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.single-property-google-maps-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  .single-property-google-maps-wrapper {
    border-radius: 12px;
  }
}

.single-property-google-maps-wrapper .btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 2;
  background-color: #fff;
  padding: 5px 15px;
  color: var(--homez-theme-color);
}

.single-property-google-maps-wrapper .btn:hover, .single-property-google-maps-wrapper .btn:focus {
  color: var(--homez-theme-color);
  text-decoration: underline;
}

.single-property-google-maps-wrapper #single-property-street-view-map,
.single-property-google-maps-wrapper .single-property-map {
  height: 250px;
  position: relative;
  z-index: 1;
}

.single-property-google-maps-wrapper #single-property-street-view-map {
  display: none;
}

.property-detail-main > div:not(.item-wrapper) {
  padding: 0 0 20px;
  margin: 0 0 20px;
  border-bottom: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  .property-detail-main > div:not(.item-wrapper) {
    padding: 0 0 30px;
    margin: 0 0 30px;
  }
}

.property-detail-main > div:not(.item-wrapper):last-child {
  border-bottom: 0;
  margin-bottom: 0;
}

.property-detail-main > div:not(.item-wrapper) > h3, .property-detail-main > div:not(.item-wrapper) .title, .property-detail-main > div:not(.item-wrapper) .widget-title {
  font-size: 17px;
  margin: 0 0 15px;
}

@media (min-width: 768px) {
  .property-detail-main > div:not(.item-wrapper) > h3, .property-detail-main > div:not(.item-wrapper) .title, .property-detail-main > div:not(.item-wrapper) .widget-title {
    margin: 0 0 25px;
  }
}

.property-detail-main > div:not(.item-wrapper).loading {
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  max-height: 100%;
  background-color: #fff;
}

.property-detail-main > div:not(.item-wrapper).agency-detail-agents, .property-detail-main > div:not(.item-wrapper).agent-agency-detail-properties {
  padding-bottom: 5px;
}

.property-detail-main > div:not(.item-wrapper).agency-detail-agents .ajax-agents-pagination,
.property-detail-main > div:not(.item-wrapper).agency-detail-agents .ajax-properties-pagination, .property-detail-main > div:not(.item-wrapper).agent-agency-detail-properties .ajax-agents-pagination,
.property-detail-main > div:not(.item-wrapper).agent-agency-detail-properties .ajax-properties-pagination {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .property-detail-main > div:not(.item-wrapper).agency-detail-agents, .property-detail-main > div:not(.item-wrapper).agent-agency-detail-properties {
    padding-bottom: 15px;
  }
  .property-detail-main > div:not(.item-wrapper).agency-detail-agents .ajax-agents-pagination,
  .property-detail-main > div:not(.item-wrapper).agency-detail-agents .ajax-properties-pagination, .property-detail-main > div:not(.item-wrapper).agent-agency-detail-properties .ajax-agents-pagination,
  .property-detail-main > div:not(.item-wrapper).agent-agency-detail-properties .ajax-properties-pagination {
    margin-bottom: 30px;
  }
}

@media (min-width: 1200px) {
  .property-detail-main .comment-reply-title {
    margin-bottom: 30px !important;
  }
}

.property-detail-main .widget-title-wrapper {
  margin-bottom: 20px;
}

.property-detail-main .widget-title-wrapper h3 {
  margin: 0 !important;
}

@media (max-width: 767px) {
  .property-detail-main .widget-title-wrapper .ali-right {
    margin-top: 10px;
  }
}

.property-detail-main .commentform {
  padding: 0;
}

.property-detail-main .comment-list > li:last-child .the-comment {
  padding-bottom: 0;
  margin-bottom: 0;
  border: 0;
}

.description-inner-wrapper p:last-child {
  margin-bottom: 0;
}

.show-more-less-wrapper {
  margin-top: 5px;
}

.show-more-less-wrapper a {
  font-weight: 600;
  text-decoration: underline;
}

.show-more-less-wrapper a:hover, .show-more-less-wrapper a:focus {
  color: var(--homez-theme-color);
}

.sidebar-mobile .widget:last-child {
  margin-bottom: 0;
}

.property-detail-agent {
  overflow: hidden;
  clear: both;
}

@media (min-width: 1200px) {
  .property-detail-agent {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    justify-content: space-between;
    -webkit-justify-content: space-between;
  }
}

.property-detail-agent > div {
  width: 50%;
  float: left;
}

@media (max-width: 1199px) {
  .property-detail-agent > div {
    margin-bottom: 10px;
  }
}

@media (min-width: 1200px) {
  .property-detail-agent > div {
    width: auto;
  }
}

.property-detail-agent .title-info {
  font-weight: 500;
  color: var(--homez-heading-color);
}

.tabs-yelp {
  border-bottom: 1px solid #E9E9E9;
  margin: 0 0 20px;
}

@media (min-width: 1200px) {
  .tabs-yelp > li {
    margin-right: 20px;
  }
}

.tabs-yelp > li:last-child {
  margin-right: 0;
}

.tabs-yelp > li .nav-link {
  display: inline-block;
  font-weight: 600;
  color: #717171;
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: none;
  background: transparent !important;
  position: relative;
  padding: 0 5px 5px;
  border: 0;
}

@media (min-width: 1200px) {
  .tabs-yelp > li .nav-link {
    padding: 0 15px 18px;
  }
}

.tabs-yelp > li .nav-link:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  width: 0;
  height: 2px;
  background: var(--homez-link-color);
}

.tabs-yelp > li .nav-link.active {
  color: var(--homez-link-color);
}

.tabs-yelp > li .nav-link.active:before {
  width: 100%;
}

.yelp-list .yelp-list-sub {
  padding: 0;
  margin: 0;
  list-style: none;
}

.yelp-list .yelp-list-sub li {
  margin-bottom: 10px;
}

@media (min-width: 1200px) {
  .yelp-list .yelp-list-sub li {
    margin-bottom: 18px;
  }
}

.yelp-list .yelp-list-sub li:last-child {
  margin-bottom: 0;
}

.yelp-list .yelp-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid #C4C640;
  font-size: 13px;
}

.yelp-list .yelp-circle span {
  font-size: 14px;
  font-weight: 600;
}

.yelp-list .yelp-circle.perfect {
  border-color: #EB6753;
}

.yelp-list .yelp-list-inner .inner {
  padding-left: 12px;
}

.yelp-list .yelp-list-inner .inner-right {
  margin-left: auto;
}

.yelp-list .yelp-list-inner a {
  font-weight: 600;
}

.yelp-list .rating > img {
  display: none;
}

.yelp-list .average-rating {
  width: 77px;
  position: relative;
  height: 22px;
  line-height: 22px;
  letter-spacing: 5px;
  font-size: 10px;
}

.yelp-list .average-rating:after {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  content: "\f005\f005\f005\f005\f005";
  color: #e1e1e1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.yelp-list .average-rating [class*="average-inner"] {
  position: absolute;
  overflow: hidden;
  height: 22px;
  top: 0;
  left: 0;
  z-index: 1;
}

.yelp-list .average-rating [class*="average-inner"]:after {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  content: "\f005\f005\f005\f005\f005";
  color: #C4C640;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.yelp-list .average-inner-1 {
  width: 10%;
}

.yelp-list .average-inner-2 {
  width: 20%;
}

.yelp-list .average-inner-3 {
  width: 30%;
}

.yelp-list .average-inner-4 {
  width: 40%;
}

.yelp-list .average-inner-5 {
  width: 50%;
}

.yelp-list .average-inner-6 {
  width: 60%;
}

.yelp-list .average-inner-7 {
  width: 70%;
}

.yelp-list .average-inner-8 {
  width: 80%;
}

.yelp-list .average-inner-9 {
  width: 90%;
}

.yelp-list .average-inner-10 {
  width: 100%;
}

.property-yelp-places .yelp-list {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .property-yelp-places .yelp-list {
    margin-bottom: 1.875rem;
  }
}

.property-yelp-places .yelp-list:last-child {
  margin-bottom: 0;
}

.property-yelp-places .property-section-heading {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 25px;
}

.property-yelp-places .property-section-heading h3 {
  margin: 0;
  font-size: 17px;
}

.property-yelp-places .property-section-heading .yelp-logo {
  margin-left: auto;
}

.property-yelp-places .property-section-heading .yelp-logo span {
  vertical-align: middle;
  display: inline-block;
}

.property-walk-score .property-section-heading {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 20px;
}

.property-walk-score .property-section-heading h3 {
  margin: 0;
  font-size: 18px;
}

.property-walk-score .property-section-heading .walkscore-logo {
  margin-left: auto;
}

.walks-core-list {
  padding: 0;
  margin: 0;
  list-style: none;
  overflow: hidden;
  clear: both;
}

.walks-core-list li {
  margin-bottom: 12px;
}

@media (min-width: 1200px) {
  .walks-core-list li {
    margin-bottom: 20px;
  }
}

.walks-core-list .walkscore-icon {
  background-color: var(--homez-theme-color-007);
  width: 50px;
  height: 50px;
  text-align: center;
  font-size: 20px;
  color: var(--homez-theme-color);
  border-radius: 50%;
}

@media (min-width: 1200px) {
  .walks-core-list .walkscore-icon {
    width: 70px;
    height: 70px;
    font-size: 30px;
  }
}

.walks-core-list .inner-right {
  padding-left: 15px;
}

.walks-core-list .title-walks {
  font-size: 0.875rem;
  margin: 0 0 5px;
}

.walks-core-list address {
  display: inline-block;
  margin: 0;
}

.bottom-walkscore .text-underline {
  font-weight: 600;
}

#review_form_wrapper .rating-wrapper {
  margin-bottom: 10px;
}

@media (min-width: 1200px) {
  #review_form_wrapper .rating-wrapper {
    margin-bottom: 20px;
  }
}

.rating-wrapper {
  position: relative;
  max-width: 500px;
}

@media (min-width: 768px) {
  .rating-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
  }
}

.rating-wrapper .rating-inner {
  white-space: nowrap;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .rating-wrapper .rating-inner {
    margin-bottom: 18px;
    width: calc( 50% - 20px);
  }
  .rating-wrapper .rating-inner:nth-child(2n) {
    margin-left: 40px;
  }
}

.rating-wrapper .rating-inner:last-child {
  margin-bottom: 0;
}

.rating-wrapper .rating-inner .active {
  color: #C4C640;
}

.rating-wrapper .review-label img {
  margin-right: 5px;
}

.rating-wrapper .review-stars {
  font-size: 11px;
  letter-spacing: 5px;
  color: #e1e1e1;
}

.rating-wrapper .subtitle {
  display: inline-block;
  margin-right: 20px;
}

.rating-wrapper ul {
  padding: 0;
  margin: 0;
  display: inline-block;
  vertical-align: middle;
  list-style: none;
  overflow: hidden;
  cursor: pointer;
}

.rating-wrapper ul li {
  float: left;
}

.list-category-rating li {
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .list-category-rating {
    margin-bottom: 25px;
  }
  .list-category-rating li {
    width: calc(50% - 20px);
    margin-bottom: 18px;
  }
  .list-category-rating li:nth-child(2n) {
    margin-left: 40px;
  }
}

.list-category-rating .percent-wrapper {
  width: 150px;
  height: 5px;
  border-radius: 5px;
  background-color: #E6E9EC;
  position: relative;
}

.list-category-rating .percent-wrapper .percent {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: var(--homez-theme-color);
  border-radius: 5px;
}

.list-category-rating .value {
  text-align: right;
  min-width: 25px;
  color: var(--homez-link-color);
  margin-left: 15px;
}

.list-category-rating .category-label {
  width: calc(100% - 193px);
}

.list-category-rating .category-value {
  width: 193px;
}

.agent-content-wrapper .agent-thumbnail {
  width: 90px;
  height: 90px;
  overflow: hidden;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  justify-content: center;
  -ms-justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
  background-color: #fff;
  flex-shrink: 0;
}

.agent-content-wrapper .agent-content {
  flex-grow: 1;
  padding-left: 20px;
  font-size: 13px;
}

.agent-content-wrapper h3 {
  margin: 0 0 5px;
  font-size: 15px;
}

.contact-form-agent .agent-content-wrapper {
  margin-bottom: 1.875rem;
}

.ui-slider-horizontal {
  margin-top: 25px;
  background-color: #F7F7F7;
  width: 100%;
  height: 3px;
  position: relative;
  z-index: 1;
  width: calc(100% - 29px) !important;
  margin-right: 29px;
}

.ui-slider-horizontal:before {
  z-index: -1;
  content: '';
  position: absolute;
  top: 0;
  width: 29px;
  height: 100%;
  background-color: #F7F7F7;
  left: 100%;
}

.ui-slider-horizontal .ui-slider-range {
  background-color: var(--homez-link-color);
  height: 3px;
  position: absolute;
  top: 0;
  left: 0;
}

.ui-slider-horizontal .ui-slider-handle {
  outline: none !important;
  width: 29px;
  height: 29px;
  background: #fff;
  position: absolute;
  z-index: 1;
  cursor: pointer;
  top: 50%;
  border: 2px solid var(--homez-link-color);
  border-radius: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ui-slider-horizontal .ui-slider-handle:before {
  content: '';
  width: 7px;
  height: 9px;
  border-width: 0 1px;
  border-style: solid;
  border-color: var(--homez-link-color);
  display: block;
}

.ui-slider-horizontal .ui-slider-handle:after {
  display: block;
  content: '';
  width: 1px;
  height: 9px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: -1px;
  background: var(--homez-link-color);
}

.properties-filter-top-sidebar-wrapper {
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .properties-filter-top-sidebar-wrapper {
    margin-bottom: 55px;
  }
}

.properties-filter-top-sidebar-wrapper > .widget:last-child {
  margin-bottom: 0;
}

.properties-filter-top-sidebar-wrapper + .apus-breadscrumb .wrapper-breads {
  padding-top: 0;
}

.highlight {
  color: var(--homez-theme-color);
}

.leaflet-geocode-container i {
  color: var(--homez-link-color);
  display: inline-block;
  margin-right: 5px;
  font-size: 14px;
}

.action-location .leaflet-geocode-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 4;
  font-size: 14px;
}

.action-location .leaflet-geocode-container ul {
  list-style: none;
  background: #fff;
  padding: 15px;
  margin: 0;
  font-size: 12px;
  border-radius: 0 0 3px 3px;
  border-color: #E9E9E9;
  border-style: solid;
  border-width: 0 1px 1px;
}

.action-location .leaflet-geocode-container ul li {
  cursor: pointer;
  margin-bottom: 7px;
  padding-bottom: 7px;
  border-bottom: 1px solid #ececec;
}

.action-location .leaflet-geocode-container ul li:last-child {
  margin-bottom: 0;
  padding: 0;
  border: 0;
}

.action-location .leaflet-geocode-container .highlight {
  font-weight: 500;
  font-size: 14px;
}

.action-location.loading .find-me {
  animation: rotate_icon 1000ms linear 0s normal none infinite running;
  -webkit-animation: rotate_icon 1000ms linear 0s normal none infinite running;
}

.action-location.loading .find-me:before {
  content: "\f110" !important;
  font-weight: 900 !important;
  font-family: "Font Awesome 5 Free" !important;
}

.form-group.slider .form-group-inner {
  display: block;
}

.form-group.slider .inner {
  margin-top: 25px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  /* Safari 7.0+ */
  -ms-align-items: center;
  align-items: center;
}

.form-group.slider .inner > * {
  display: inline-block;
  text-align: center;
  min-width: 60px;
  padding: 5px 20px;
  border-radius: 8px;
  background-color: #F7F7F7;
  color: var(--homez-link-color);
  font-size: 13px;
  font-weight: 600;
}

.form-group.slider .inner > *:last-child {
  margin-left: auto;
}

form.form-search .nav-tabs [type="radio"] {
  display: none;
}

form.form-search .nav-tabs [type="radio"]:checked + span {
  color: var(--homez-link-color);
}

form.form-search .nav-tabs [type="radio"]:checked + span:before {
  width: 100%;
}

form.form-search .nav-tabs [type="radio"] + span {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-weight: 600;
  color: #717171;
  display: inline-block;
  padding: 14px 15px;
  position: relative;
  cursor: pointer;
}

form.form-search .nav-tabs [type="radio"] + span:before {
  left: 0;
  bottom: 0;
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  background: var(--homez-link-color);
}

form.form-search .heading-label {
  font-size: 0.875rem;
  margin: 0 0 12px;
  color: var(--homez-heading-color);
  font-weight: 600;
  display: block;
}

form.form-search .search-action {
  margin-top: 20px;
}

form.form-search .search-action a {
  text-decoration: underline;
}

form.form-search .search-action a:hover, form.form-search .search-action a:focus {
  color: var(--homez-theme-color);
}

form.form-search .form-group-inner {
  position: relative;
}

form.form-search .form-group-inner > i {
  width: 18px;
  font-size: 18px;
  z-index: 1;
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

form.form-search .form-group-inner > i + .twitter-typeahead .form-control,
form.form-search .form-group-inner > i ~ .select2-container,
form.form-search .form-group-inner > i + *:not(.twitter-typeahead) {
  padding-left: 45px;
}

form.form-search .btn-submit i {
  font-size: 18px;
  line-height: 1;
  vertical-align: text-bottom;
}

form.form-search .btn-submit.no-text i {
  margin: 0 !important;
}

@media (min-width: 768px) {
  form.form-search .advance-link + .btn-submit {
    margin-left: 25px;
  }
}

form.form-search .tax-viewmore-field.show-more .terms-list {
  max-height: 190px;
}

form.form-search .tax-viewmore-field.show-less .terms-list {
  max-height: none;
}

form.form-search .tax-viewmore-field:not([class*="show-"]) .toggle-filter-viewmore {
  display: none;
}

form.form-search .list-item.more-fields {
  display: none;
}

form.form-search .list-item.more-fields.active {
  display: block;
}

form.form-search .toggle-filter-list,
form.form-search .toggle-filter-viewmore {
  font-weight: 500;
  display: inline-block;
  color: var(--homez-theme-color);
}

form.form-search .toggle-filter-list .icon-more,
form.form-search .toggle-filter-viewmore .icon-more {
  vertical-align: middle;
  margin-right: 3px;
  display: inline-block;
  font-size: 14px;
}

form.form-search .toggle-filter-list:hover, form.form-search .toggle-filter-list:focus,
form.form-search .toggle-filter-viewmore:hover,
form.form-search .toggle-filter-viewmore:focus {
  text-decoration: underline;
}

form.form-search .action-location .clear-location {
  top: 50%;
  right: 0;
  width: 18px;
  height: 18px;
  line-height: 18px;
  margin-top: -9px;
  position: absolute;
  background-color: #dc3545;
  text-align: center;
  color: #fff;
  font-size: 8px;
  cursor: pointer;
  border-radius: 30px;
}

form.form-search .action-location .find-me {
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  right: 0;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
}

form.form-search .action-location .find-me:hover, form.form-search .action-location .find-me:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

form.form-search .action-location .find-me:before {
  content: "\f192";
  font-family: "Font Awesome 5 Free";
  font-weight: normal;
}

form.form-search .action-location .find-me.loading {
  animation: rotate 700ms linear 0s normal none infinite running;
  -webkit-animation: rotate 700ms linear 0s normal none infinite running;
}

form.form-search .action-location .find-me.loading:before {
  content: "\f110";
  font-weight: 900;
}

form.form-search .advance-search-btn {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--homez-link-color);
  text-decoration: none !important;
}

form.form-search .advance-search-btn i {
  display: inline-block;
  font-weight: normal;
  font-size: 0.875rem;
  vertical-align: middle;
  margin-right: 10px;
  color: var(--homez-link-color);
  width: 40px;
  height: 40px;
  background: #F7F7F7;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

form.form-search .advance-search-btn:hover i {
  background: #dedede;
}

form.form-search .advance-search-wrapper-fields {
  display: none;
}

form.form-search .circle-check li {
  width: 50%;
}

@media (min-width: 1200px) {
  form.form-search .circle-check li {
    width: 33.33%;
    margin-bottom: 15px;
  }
}

form.form-search .tt-menu {
  background: #fff;
  border-radius: 0 0 8px 8px;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  max-height: 245px;
  width: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  padding: 5px 20px;
}

form.form-search .twitter-typeahead {
  width: 100%;
  display: block;
}

form.form-search .twitter-typeahead:before {
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  content: '';
  position: absolute;
  top: 0;
  z-index: 2;
  display: block;
  right: 15px;
  width: 22px;
  height: 100%;
  background: url("../images/loading.gif") no-repeat scroll center center/18px auto;
}

form.form-search .twitter-typeahead.loading:before {
  opacity: 0.7;
  filter: alpha(opacity=70);
}

form.form-search .tt-dataset-search > .tt-selectable:first-child {
  display: none !important;
}

form.form-search .tt-dataset-search .tt-selectable {
  text-decoration: none !important;
  line-height: 1.4;
  font-size: 12px;
  padding: 12px 0;
  display: table;
  width: 100%;
  margin: 0;
  border-bottom: 1px solid #E9E9E9;
  color: var(--homez-text-color);
}

form.form-search .tt-dataset-search .tt-selectable:last-child {
  border-bottom: 0;
}

form.form-search .tt-dataset-search .tt-selectable img {
  max-width: 55px;
  border-radius: 6px;
}

form.form-search .tt-dataset-search .tt-selectable h4 {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: 13px;
  margin: 1px 0 5px;
  font-weight: 600;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

form.form-search .tt-dataset-search .tt-selectable .flex-grow-1 {
  padding-left: 12px;
}

form.form-search .tt-dataset-search .tt-selectable .property-price {
  font-size: 12px;
}

form.form-search .tt-dataset-search .tt-selectable .property-price i {
  display: none;
}

form.form-search .tt-dataset-search .tt-selectable strong {
  font-weight: 600;
  color: var(--homez-theme-color);
}

form.form-search .tt-dataset-search .tt-selectable .property-metas {
  margin-top: 2px;
}

form.form-search .tt-dataset-search .tt-selectable .property-metas > * {
  margin-right: 15px;
}

form.form-search .tt-dataset-search .tt-selectable .property-metas > *:last-child {
  margin-right: 0;
}

form.form-search .tt-dataset-search .tt-selectable .value-suffix {
  white-space: nowrap;
}

form.form-search .tt-dataset-search .tt-selectable:hover h4 {
  text-decoration: underline;
}

form.form-search.style1 .nav-tabs {
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #E9E9E9;
  background: #fff;
  display: inline-flex;
  padding: 0 20px;
}

@media (min-width: 1200px) {
  form.form-search.style1 .nav-tabs {
    border-radius: 12px 12px 0 0;
  }
}

form.form-search.style1 .nav-tabs li {
  margin: 0;
}

@media (min-width: 1200px) {
  form.form-search.style1 .nav-tabs li {
    margin: 0 0 -1px;
  }
}

form.form-search.style1 .search-form-inner {
  background: #fff;
  padding: 20px;
  border-radius: 0 8px 8px 8px;
}

@media (min-width: 1200px) {
  form.form-search.style1 .search-form-inner {
    border-radius: 0 12px 12px 12px;
  }
}

form.form-search.style1 .content-main-inner .from-to-text-wrapper,
form.form-search.style1 .content-main-inner .select2-container--default.select2-container .select2-selection--single,
form.form-search.style1 .content-main-inner .form-control {
  background: #F7F7F7 !important;
  border: 0;
  outline: none;
}

form.form-search.style1 .content-main-inner .from-to-text-wrapper:focus,
form.form-search.style1 .content-main-inner .select2-container--default.select2-container .select2-selection--single:focus,
form.form-search.style1 .content-main-inner .form-control:focus {
  background: #eaeaea !important;
}

form.form-search.style2 .nav-tabs {
  justify-content: center;
  margin: 0 0 20px;
  border: 0;
}

form.form-search.style2 .nav-tabs [type="radio"] + span {
  color: #fff;
}

form.form-search.style2 .nav-tabs [type="radio"] + span:before {
  background: #fff;
}

form.form-search.style2 .nav-tabs [type="radio"]:checked + span {
  color: #fff;
}

form.form-search.style2 .search-form-inner {
  padding: 10px;
  background: #fff;
  border-radius: 75px;
}

form.form-search.style2 .search-form-inner .from-to-text-wrapper,
form.form-search.style2 .search-form-inner .form-control,
form.form-search.style2 .search-form-inner .select2-container--default.select2-container .select2-selection--single {
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: none !important;
}

form.form-search.style2 .search-form-inner .list-fileds > div {
  border-style: solid;
  border-color: #E9E9E9;
  border-width: 0 0 1px;
}

@media (min-width: 768px) {
  form.form-search.style2 .search-form-inner .list-fileds > div {
    border-width: 0 1px 0 0;
  }
}

form.form-search.style2 .search-form-inner .list-fileds > div:last-child {
  border: 0;
}

form.form-search.style3 .nav-tabs {
  border-radius: 8px 8px 0 0;
  border: 0;
  background: #fff;
  display: inline-flex;
}

@media (min-width: 1200px) {
  form.form-search.style3 .nav-tabs {
    border-radius: 12px 12px 0 0;
  }
}

form.form-search.style3 .nav-tabs li {
  margin: 0;
}

form.form-search.style3 .nav-tabs li:first-child span {
  border-radius: 6px 0 0 0;
}

@media (min-width: 1200px) {
  form.form-search.style3 .nav-tabs li:first-child span {
    border-radius: 10px 0 0 0;
  }
}

form.form-search.style3 .nav-tabs li:last-child span {
  border-radius: 0 6px 0 0;
}

@media (min-width: 1200px) {
  form.form-search.style3 .nav-tabs li:last-child span {
    border-radius: 0 10px 0 0;
  }
}

form.form-search.style3 .nav-tabs [type="radio"] + span {
  padding: 14px 30px;
}

form.form-search.style3 .nav-tabs [type="radio"] + span:before {
  left: 20px;
}

form.form-search.style3 .nav-tabs [type="radio"]:checked + span:before {
  width: calc(100% - 40px);
}

form.form-search.style3 .search-form-inner {
  background: #fff;
  padding: 20px;
  border-radius: 0 8px 8px 8px;
}

@media (min-width: 1200px) {
  form.form-search.style3 .search-form-inner {
    border-radius: 0 12px 12px 12px;
  }
}

form.form-search.style3 .content-main-inner .from-to-text-wrapper,
form.form-search.style3 .content-main-inner .select2-container--default.select2-container .select2-selection--single,
form.form-search.style3 .content-main-inner .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered,
form.form-search.style3 .content-main-inner .form-control {
  border: 0;
  outline: none;
  padding: 0;
  height: auto;
}

form.form-search.style3 .content-main-inner .from-to-text-wrapper .select2-selection__clear,
form.form-search.style3 .content-main-inner .select2-container--default.select2-container .select2-selection--single .select2-selection__clear,
form.form-search.style3 .content-main-inner .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered .select2-selection__clear,
form.form-search.style3 .content-main-inner .form-control .select2-selection__clear {
  margin-right: 20px;
}

form.form-search.style3 .content-main-inner .from-to-text-wrapper .select2-selection__arrow,
form.form-search.style3 .content-main-inner .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow,
form.form-search.style3 .content-main-inner .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered .select2-selection__arrow,
form.form-search.style3 .content-main-inner .form-control .select2-selection__arrow {
  top: 0;
  right: 0;
}

form.form-search.style3 .content-main-inner .list-fileds {
  margin-left: -20px;
  margin-right: -20px;
}

form.form-search.style3 .content-main-inner .list-fileds > div {
  padding-left: 20px;
  padding-right: 20px;
  border-right: 1px solid #E9E9E9;
}

form.form-search.style3 .content-main-inner .list-fileds > div:last-child {
  border-right: 0;
}

form.form-search.style3 .content-main-inner .heading-label {
  margin-bottom: 5px;
}

form.form-search.style4 .nav-tabs {
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #E9E9E9;
  background: #fff;
  display: inline-flex;
  padding: 0 0.9375rem;
}

@media (min-width: 1200px) {
  form.form-search.style4 .nav-tabs {
    border-radius: 12px 12px 0 0;
  }
}

@media (min-width: 1200px) {
  form.form-search.style4 .nav-tabs {
    padding: 0 20px;
  }
}

form.form-search.style4 .nav-tabs li {
  margin: 0 0 -1px;
}

form.form-search.style4 div.search-form-inner {
  padding: 0.9375rem;
  border-radius: 0 8px 8px 8px;
  background: #fff;
}

@media (min-width: 1200px) {
  form.form-search.style4 div.search-form-inner {
    padding: 1.875rem;
  }
}

form.form-search.style5 .content-main-inner .from-to-text-wrapper,
form.form-search.style5 .content-main-inner .form-control {
  background: #fff;
  border-color: transparent;
  padding-top: 8px;
  padding-bottom: 8px;
  border-radius: 45px;
}

form.form-search.style5 .content-main-inner .select2-container--default.select2-container .select2-selection--single .select2-selection__rendered {
  padding-top: 2px;
}

form.form-search.style5 .content-main-inner .select2-container--default.select2-container .select2-selection--single {
  height: 45px;
  border-radius: 45px !important;
  border-color: transparent;
}

form.form-search.style5 .content-main-inner .select2-container--default.select2-container .select2-selection--single .select2-selection__arrow {
  top: 9px;
}

form.form-search.style5 .content-main-inner .advance-search-btn {
  display: inline-block;
  padding: 6px 20px;
  font-weight: normal;
  background: #fff;
  border-radius: 45px;
}

form.form-search.style5 .content-main-inner .btn-submit {
  padding: 8px 20px;
  border-radius: 45px;
  font-weight: normal;
  font-size: 0.875rem;
}

form.form-search.style5 .row.list-fileds {
  margin-right: -5px;
  margin-left: -5px;
}

form.form-search.style5 .row.list-fileds > div {
  padding-left: 5px;
  padding-right: 5px;
}

@media (min-width: 768px) {
  form.form-search.style5 .advance-link + .btn-submit {
    margin-left: 10px;
  }
}

form.form-search.horizontal .form-group-search .d-md-flex {
  justify-content: end;
}

@media (max-width: 767px) {
  form.form-search.horizontal .form-group-search .d-md-flex.no-text {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    justify-content: start;
  }
  form.form-search.horizontal .form-group-search .d-md-flex.no-text .advance-link + .btn-submit {
    margin: 0 0 0 20px;
    width: auto;
  }
}

form.form-search.horizontal .advance-search-btn i {
  color: inherit;
  font-size: 1rem;
  line-height: inherit;
  width: auto;
  height: auto;
  background: transparent !important;
}

form.form-search.horizontal .advance-search-btn:hover, form.form-search.horizontal .advance-search-btn:focus {
  color: var(--homez-theme-color);
}

form.form-search.horizontal .advance-search-wrapper {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 2;
}

form.form-search.horizontal .advance-search-wrapper .advance-search-wrapper-fields {
  padding: 20px 20px 0;
  background: #fff;
  border-radius: 8px;
}

form.form-search.vertical .search-form-inner {
  padding: 0;
  border-radius: 0;
}

form.form-search.vertical .circle-check li {
  width: 100%;
}

form.form-search.vertical .circle-check li:last-child {
  margin-bottom: 0;
}

form.form-search.vertical .form-group {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  form.form-search.vertical .form-group {
    margin-bottom: 25px;
  }
}

@media (min-width: 1200px) {
  form.form-search.vertical .form-group-amenity {
    margin-bottom: 0;
  }
  form.form-search.vertical .form-group-amenity .circle-check li {
    width: 50%;
    padding: 0 5px 0 0;
  }
  form.form-search.vertical .form-group-amenity .circle-check li:nth-child(2n) {
    padding: 0 0 0 5px;
  }
}

.apus-mfp-zoom-in.login-popup .mfp-content {
  max-width: 460px;
}

.apus-mfp-zoom-in .mfp-content {
  border-radius: 8px;
  max-width: 550px;
  margin: 30px auto;
  overflow: hidden;
  background: #fff;
  text-align: left;
}

@media (min-width: 1200px) {
  .apus-mfp-zoom-in .mfp-content {
    border-radius: 12px;
  }
}

.apus-mfp-zoom-in .mfp-content .advance-title {
  font-size: 20px;
  color: var(--homez-link-color);
  margin: 0;
}

.apus-mfp-zoom-in .mfp-content .advance-search-top {
  padding: 15px;
  border-bottom: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  .apus-mfp-zoom-in .mfp-content .advance-search-top {
    padding: 20px 30px;
  }
}

.apus-mfp-zoom-in .mfp-content .close-advance-popup {
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  width: 40px;
  height: 40px;
  color: var(--homez-link-color);
  background-color: #F7F7F7;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  display: inline-block;
}

.apus-mfp-zoom-in .mfp-content .close-advance-popup i {
  font-size: 13px;
}

.apus-mfp-zoom-in .mfp-content .close-advance-popup:hover, .apus-mfp-zoom-in .mfp-content .close-advance-popup:focus {
  background-color: #dc3545;
  color: #fff;
}

.apus-mfp-zoom-in .mfp-content .inner-search-advance {
  padding: 15px 15px 0;
}

@media (min-width: 1200px) {
  .apus-mfp-zoom-in .mfp-content .inner-search-advance {
    padding: 20px 30px 0;
  }
}

.apus-mfp-zoom-in .mfp-content .advance-search-bottom {
  border-top: 1px solid #E9E9E9;
  padding: 15px;
}

@media (min-width: 1200px) {
  .apus-mfp-zoom-in .mfp-content .advance-search-bottom {
    padding: 20px 30px;
  }
}

.apus-mfp-zoom-in .mfp-content .search-action {
  margin-top: 20px;
}

.apus-mfp-zoom-in .mfp-content .circle-check li {
  margin-bottom: 10px;
}

.apus-mfp-zoom-in .mfp-content .popup-property-contact {
  padding: 0.9375rem;
}

@media (min-width: 1200px) {
  .apus-mfp-zoom-in .mfp-content .popup-property-contact {
    padding: 1.875rem;
  }
}

.apus-mfp-zoom-in .mfp-content .popup-property-contact .close-advance-popup {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 1;
}

@media (min-width: 1200px) {
  .apus-mfp-zoom-in .mfp-content .popup-property-contact .close-advance-popup {
    right: 0.9375rem;
    top: 0.9375rem;
  }
}

.widget-properties-maps .properties-google-maps {
  min-height: 250px;
}

.results-filter-wrapper {
  margin-bottom: 20px;
}

.results-filter-wrapper .title {
  font-weight: 500;
  font-size: 18px;
  margin: 0 0 20px;
}

.results-filter-wrapper .inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 10px 0;
  padding-right: 20px;
  background: #fff;
}

.results-filter-wrapper .inner > a {
  margin-left: auto;
  color: #dc3545;
  font-weight: 500;
  white-space: nowrap;
}

.results-filter-wrapper .inner > a:hover, .results-filter-wrapper .inner > a:focus {
  color: #d32535;
}

.results-filter-wrapper .results-filter {
  overflow: hidden;
  clear: both;
  list-style: none;
  padding: 0;
  margin: 0;
}

.results-filter-wrapper .results-filter li {
  display: inline-block;
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
}

.results-filter-wrapper .results-filter li a {
  display: inline-block;
  font-size: 14px;
  padding: 0px 7px;
  border-radius: 2px;
  background-color: #f8f8f8;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.results-filter-wrapper .results-filter li a .close-value {
  color: #dc3545;
  margin-right: 5px;
  display: inline-block;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.results-filter-wrapper .results-filter li a:hover {
  background-color: #dc3545;
  color: #fff;
}

.results-filter-wrapper .results-filter li a:hover .close-value {
  color: #fff;
}

.p-fix-pagination {
  position: fixed;
  bottom: 0;
  z-index: 6;
  left: 0;
  width: 100%;
}

[class*="half-map"] .p-fix-pagination li > span,
[class*="half-map"] .p-fix-pagination li > a {
  background-color: #fff;
}

[class*="half-map"] .p-fix-pagination li > span.current,
[class*="half-map"] .p-fix-pagination li > a.current {
  background-color: var(--homez-theme-color);
}

.layout-type-half-map,
.layout-type-half-map-v3,
.layout-type-half-map-v2 {
  position: relative;
}

.layout-type-half-map #properties-google-maps,
.layout-type-half-map-v3 #properties-google-maps,
.layout-type-half-map-v2 #properties-google-maps {
  position: fixed !important;
  right: 0;
  height: 100vh;
}

@media (min-width: 992px) {
  .layout-type-half-map #properties-google-maps,
  .layout-type-half-map-v3 #properties-google-maps,
  .layout-type-half-map-v2 #properties-google-maps {
    width: 40%;
  }
}

@media (min-width: 1200px) {
  .layout-type-half-map #properties-google-maps,
  .layout-type-half-map-v3 #properties-google-maps,
  .layout-type-half-map-v2 #properties-google-maps {
    width: 58%;
  }
}

@media (min-width: 992px) {
  .layout-type-half-map .first_class,
  .layout-type-half-map-v3 .first_class,
  .layout-type-half-map-v2 .first_class {
    width: 60%;
  }
}

@media (min-width: 1200px) {
  .layout-type-half-map .first_class,
  .layout-type-half-map-v3 .first_class,
  .layout-type-half-map-v2 .first_class {
    width: 42%;
  }
}

@media (min-width: 992px) {
  .layout-type-half-map .second_class,
  .layout-type-half-map-v3 .second_class,
  .layout-type-half-map-v2 .second_class {
    width: 40%;
  }
}

@media (min-width: 1200px) {
  .layout-type-half-map .second_class,
  .layout-type-half-map-v3 .second_class,
  .layout-type-half-map-v2 .second_class {
    width: 58%;
  }
}

.layout-type-half-map .content-listing,
.layout-type-half-map-v3 .content-listing,
.layout-type-half-map-v2 .content-listing {
  padding: 0.9375rem;
}

@media (min-width: 1200px) {
  .layout-type-half-map .content-listing,
  .layout-type-half-map-v3 .content-listing,
  .layout-type-half-map-v2 .content-listing {
    padding: 1.875rem;
  }
}

.layout-type-half-map .content-listing {
  padding: 0;
}

.layout-type-half-map .properties-listing-wrapper {
  padding: 0 0.9375rem;
}

@media (min-width: 1200px) {
  .layout-type-half-map .properties-listing-wrapper {
    padding: 0 1.875rem;
  }
}

@media (min-width: 1200px) {
  .layout-type-half-map .properties-pagination-wrapper {
    padding-bottom: 1.875rem;
  }
}

.properties-pagination-wrapper .pagination {
  padding: 0;
}

@media (min-width: 992px) {
  .layout-type-half-map-v3 {
    flex-direction: row-reverse;
  }
  .layout-type-half-map-v3 #properties-google-maps {
    right: initial;
    left: 0;
  }
}

@media (min-width: 992px) {
  .layout-type-half-map-v3 .properties-filter-top-half-map3 {
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
  }
}

.properties-filter-top-half-map3 .widget:last-child {
  margin-bottom: 0;
}

.properties-filter-top-half-map {
  z-index: 2;
}

.properties-filter-top-half-map .widget:last-child {
  margin-bottom: 0;
}

.layout-type-top-map #properties-google-maps {
  height: 350px;
}

@media (min-width: 1200px) {
  .layout-type-top-map #properties-google-maps {
    height: 570px;
  }
}

.layout-type-top-map .p-fix-pagination {
  position: static;
}

.mobile-groups-button {
  padding: 20px 0 15px;
  position: relative;
  z-index: 2;
}

.mobile-groups-button button + button {
  margin-left: 5px;
}

.properties-filter-sidebar-wrapper {
  position: fixed;
  z-index: 8;
  max-width: 85%;
  width: 360px;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: #fff;
  padding: 0.9375rem;
  overflow-y: auto;
  scrollbar-width: thin;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  opacity: 0;
  filter: alpha(opacity=0);
}

@media (min-width: 1200px) {
  .properties-filter-sidebar-wrapper {
    padding: 1.875rem;
  }
}

.properties-filter-sidebar-wrapper.active {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
  opacity: 1;
  filter: alpha(opacity=100);
}

.properties-filter-sidebar-wrapper + .over-dark-filter {
  background-color: rgba(24, 26, 32, 0.5);
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: fixed;
  z-index: 7;
  width: 100vw;
  height: 100vh;
  cursor: not-allowed;
  top: 0;
  left: 0;
  visibility: hidden;
}

.properties-filter-sidebar-wrapper.active + .over-dark-filter {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.properties-filter-sidebar-wrapper .close-filter {
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: absolute;
  top: 10px;
  right: 10px;
  line-height: 30px;
  width: 30px;
  height: 30px;
  color: var(--homez-link-color);
  background-color: #F7F7F7;
  text-align: center;
  border-radius: 50%;
}

@media (min-width: 1200px) {
  .properties-filter-sidebar-wrapper .close-filter {
    line-height: 40px;
    width: 40px;
    height: 40px;
  }
}

.properties-filter-sidebar-wrapper .close-filter:hover, .properties-filter-sidebar-wrapper .close-filter:focus {
  background-color: #dc3545;
  color: #fff;
}

.properties-filter-sidebar-wrapper .circle-check li {
  width: 50%;
}

.properties-filter-sidebar-wrapper .widget-property-search-form {
  height: 100%;
}

.properties-filter-sidebar-wrapper .widget-property-search-form .title {
  margin-right: -20px;
  margin-left: -20px;
  padding: 10px 20px;
  background-color: #F6F8F9;
}

@media (min-width: 1200px) {
  .properties-filter-sidebar-wrapper .widget-property-search-form .title {
    margin-right: -30px;
    margin-left: -30px;
    padding: 17px 30px;
  }
}

.compare-tables {
  border: 0;
  margin: 0 1px;
}

.compare-tables .thumb {
  width: 200px;
  position: relative;
  overflow: hidden;
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .compare-tables .thumb {
    width: 270px;
  }
}

.compare-tables .thumb .btn-remove-property-compare {
  position: absolute;
  z-index: 1;
  top: 15px;
  right: 15px;
  width: 35px;
  height: 35px;
}

.compare-tables .property-title {
  font-size: 15px;
  margin: 0;
}

.compare-tables .property-price {
  font-size: 15px;
  font-weight: 600;
}

.compare-tables .property-price .prefix-text,
.compare-tables .property-price .suffix-text {
  font-weight: 400;
}

.compare-tables .compare-top {
  background: #F7F7F7;
}

.compare-tables .property-address {
  color: #717171;
  font-size: 13px;
  font-weight: 400;
}

.compare-tables thead th {
  max-width: 300px;
  border: 0;
  vertical-align: top;
  padding: 10px 15px;
}

@media (min-width: 1200px) {
  .compare-tables thead th {
    padding: 25px 15px;
  }
  .compare-tables thead th:first-child {
    min-width: 260px;
  }
}

.compare-tables tbody td {
  border-width: 1px 0;
  border-style: solid;
  border-color: #E9E9E9;
  padding: 10px 15px;
}

@media (min-width: 1200px) {
  .compare-tables tbody td {
    padding: 20px 15px;
  }
}

.compare-tables tbody td:first-child {
  font-weight: 600;
  font-size: 15px;
  color: var(--homez-link-color);
  text-align: right;
  border-left: 1px solid #E9E9E9;
}

.compare-tables tbody td:last-child {
  border-right: 1px solid #E9E9E9;
}

.compare-tables tbody h3 {
  font-size: 16px;
  margin: 0;
}

.compare-tables .property-public-facilities .property-public-facility-wrapper {
  width: 100%;
  margin: 0 0 10px;
}

.compare-tables .property-public-facilities .property-public-facility-wrapper:last-child {
  margin-bottom: 0;
}

.compare-tables .valuation-item .progress {
  margin-bottom: 10px;
}

.compare-tables .valuation-item:last-child .progress {
  margin-bottom: 0;
}

.wrapper-compare {
  scrollbar-width: thin;
  overflow-x: auto;
  margin-bottom: 1.875rem;
}

@media (min-width: 1200px) {
  .wrapper-compare {
    margin-bottom: 60px;
  }
}

.not-found-compare {
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #dc3545;
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .not-found-compare {
    margin-bottom: 50px;
    font-size: 20px;
  }
}

.property-submission-form-wrapper .submit-property-heading {
  padding: 0;
  margin: 0;
  list-style: none;
  background: #fff;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  border-bottom: 1px solid #E9E9E9;
  overflow-x: auto;
  position: relative;
  z-index: 0;
}

.property-submission-form-wrapper .submit-property-heading > li {
  display: inline-block;
  margin: 0;
  width: 50%;
  text-align: center;
}

.property-submission-form-wrapper .submit-property-heading > li > a {
  display: inline-block;
  position: relative;
  font-weight: 500;
  padding: 13px;
  white-space: nowrap;
}

@media (min-width: 1200px) {
  .property-submission-form-wrapper .submit-property-heading > li > a {
    padding: 13px 20px;
  }
}

.property-submission-form-wrapper .submit-property-heading > li > a:before {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  position: absolute;
  bottom: 0;
  left: 0;
  content: '';
  width: 0;
  height: 2px;
  background: var(--homez-theme-color);
  display: block;
}

.property-submission-form-wrapper .submit-property-heading > li.active > a {
  color: var(--homez-theme-color);
}

.property-submission-form-wrapper .submit-property-heading > li.active > a:before {
  width: 100%;
}

.property-submission-form-wrapper form.cmb-form > .submit-button-wrapper {
  clear: both;
  overflow: hidden;
  text-align: right;
}

.property-submission-form-wrapper form.cmb-form > .submit-button-wrapper [type="submit"] {
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .property-submission-form-wrapper form.cmb-form > .submit-button-wrapper [type="submit"] {
    min-width: 240px;
    text-align: center;
  }
}

div.cmb2-wrap:after, div.cmb2-wrap:before {
  content: " ";
  display: table;
  clear: both;
}

div.cmb2-wrap input.cmb2-text-small,
div.cmb2-wrap input.cmb2-timepicker {
  width: 100%;
}

div.cmb2-wrap .cmb2-list {
  list-style: none;
  padding: 0;
}

div.cmb2-wrap .cmb-row {
  margin-bottom: 20px;
}

div.cmb2-wrap .cmb-row[class*="property-description"] {
  width: 100%;
}

div.cmb2-wrap .cmb2-metabox-description {
  margin-top: 5px;
}

div.cmb2-wrap .cmb-multicheck-toggle {
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
  color: var(--homez-theme-color) !important;
}

div.cmb2-wrap select,
div.cmb2-wrap [type="email"],
div.cmb2-wrap [type="text"],
div.cmb2-wrap [type="number"],
div.cmb2-wrap [type="url"],
div.cmb2-wrap textarea {
  margin: 0;
  display: block;
  width: 100%;
  height: 48px;
  padding: 0.8125rem 1.29rem;
  font-size: 0.875rem;
  line-height: 1.9;
  color: var(--homez-text-color);
  border: 1px solid #E9E9E9;
  background-color: #fff;
  background-image: none;
  border-radius: 8px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

@media (min-width: 1200px) {
  div.cmb2-wrap select,
  div.cmb2-wrap [type="email"],
  div.cmb2-wrap [type="text"],
  div.cmb2-wrap [type="number"],
  div.cmb2-wrap [type="url"],
  div.cmb2-wrap textarea {
    height: calc(1.9em + (1.625rem + 2px));
  }
}

div.cmb2-wrap select::-webkit-input-placeholder,
div.cmb2-wrap [type="email"]::-webkit-input-placeholder,
div.cmb2-wrap [type="text"]::-webkit-input-placeholder,
div.cmb2-wrap [type="number"]::-webkit-input-placeholder,
div.cmb2-wrap [type="url"]::-webkit-input-placeholder,
div.cmb2-wrap textarea::-webkit-input-placeholder {
  /* Edge */
  opacity: 1;
  filter: alpha(opacity=100);
}

div.cmb2-wrap select:-ms-input-placeholder,
div.cmb2-wrap [type="email"]:-ms-input-placeholder,
div.cmb2-wrap [type="text"]:-ms-input-placeholder,
div.cmb2-wrap [type="number"]:-ms-input-placeholder,
div.cmb2-wrap [type="url"]:-ms-input-placeholder,
div.cmb2-wrap textarea:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  opacity: 1;
  filter: alpha(opacity=100);
}

div.cmb2-wrap select::placeholder,
div.cmb2-wrap [type="email"]::placeholder,
div.cmb2-wrap [type="text"]::placeholder,
div.cmb2-wrap [type="number"]::placeholder,
div.cmb2-wrap [type="url"]::placeholder,
div.cmb2-wrap textarea::placeholder {
  opacity: 1;
  filter: alpha(opacity=100);
}

div.cmb2-wrap select:focus,
div.cmb2-wrap [type="email"]:focus,
div.cmb2-wrap [type="text"]:focus,
div.cmb2-wrap [type="number"]:focus,
div.cmb2-wrap [type="url"]:focus,
div.cmb2-wrap textarea:focus {
  background: #fff;
  outline: 1px solid var(--homez-link-color);
  border-color: var(--homez-link-color);
}

div.cmb2-wrap textarea {
  height: 150px;
  resize: none;
}

div.cmb2-wrap .select2-container--default.select2-container .select2-selection--multiple {
  border-width: 0 0 2px;
  border-radius: 0 !important;
}

div.cmb2-wrap .select2-container--default.select2-container .select2-selection--multiple .select2-selection__rendered {
  padding: 5px 0;
}

div.cmb2-wrap .wp-editor-container {
  border: 0;
  margin-top: 10px;
}

div.cmb2-wrap .wp-editor-container .mce-top-part::before {
  -webkit-box-shadow: none;
  box-shadow: none;
}

div.cmb2-wrap .wp-editor-container .mce-toolbar .mce-btn-group {
  border: 0;
  padding: 0;
}

div.cmb2-wrap .wp-editor-container div.mce-toolbar-grp {
  border: 0;
  background-color: #fff;
}

div.cmb2-wrap .wp-editor-container div.mce-toolbar-grp > div {
  padding: 0;
}

div.cmb2-wrap [class*="virtual-tour"] pre {
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  background-color: #fff;
}

div.cmb2-wrap [class*="virtual-tour"] pre pre {
  border: 0;
  padding: 0 40px;
}

div.cmb2-wrap [class*="virtual-tour"] pre .CodeMirror {
  height: 150px;
}

div.cmb2-wrap [class*="virtual-tour"] .CodeMirror-gutters {
  border: 0;
}

div.cmb2-wrap [class*="virtual-tour"] .CodeMirror-activeline-background {
  background-color: transparent;
}

div.cmb2-wrap [class*="virtual-tour"] .CodeMirror-gutter {
  width: auto !important;
}

div.cmb2-wrap [type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
  -webkit-appearance: textfield;
  -ms-appearance: textfield;
  -o-appearance: textfield;
}

div.cmb2-wrap .cmb-th {
  width: 100%;
  padding: 0;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 7px;
}

div.cmb2-wrap .cmb-th label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--homez-heading-color);
  padding: 0;
  margin: 0;
}

div.cmb2-wrap .cmb-th + .cmb-td {
  width: 100%;
  padding: 0;
}

div.cmb2-wrap .cmb-td {
  position: relative;
}

div.cmb2-wrap .cmb-td .cmb-ajax-search-spinner {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 15px;
  margin: 0;
}

div.cmb2-wrap [class*="property-title"],
div.cmb2-wrap [class*="property-description"] {
  width: 100% !important;
}

div.cmb2-wrap [class*="property-video"] {
  max-width: 600px;
}

div.cmb2-wrap [class*="taxonomy-location-wrapper"] {
  width: 100%;
}

@media (min-width: 768px) {
  div.cmb2-wrap [class*="taxonomy-location-wrapper"] {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    margin-left: -10px;
    margin-right: -10px;
    width: calc(100% + 20px);
  }
  div.cmb2-wrap [class*="taxonomy-location-wrapper"] > div {
    padding-right: 10px;
    padding-left: 10px;
  }
}

div.cmb2-wrap [class*="taxonomy-location-wrapper"] > div {
  width: 100%;
}

@media (max-width: 767px) {
  div.cmb2-wrap [class*="taxonomy-location-wrapper"] > div {
    margin-bottom: 10px;
  }
  div.cmb2-wrap [class*="taxonomy-location-wrapper"] > div:last-child {
    margin-bottom: 0;
  }
}

div.cmb2-wrap .pw-map-search-wrapper {
  z-index: 1;
}

div.cmb2-wrap .pw-map-search-wrapper .find-me-location {
  top: 18px;
  color: var(--homez-text-color);
  margin-right: 10px;
}

div.cmb2-wrap .pw-map-search-wrapper .leaflet-geocode-container ul li {
  cursor: pointer;
}

div.cmb2-wrap [id*="map_location-map"] {
  margin: 20px 0;
  width: 100%;
  z-index: 1;
  border-radius: 8px;
}

@media (min-width: 1200px) {
  div.cmb2-wrap [id*="map_location-map"] {
    border-radius: 12px;
  }
}

div.cmb2-wrap [id*="map_location-map"] ~ input {
  width: calc(50% - 10px);
  float: left;
}

div.cmb2-wrap .label-can-drag {
  display: inline-block;
  margin: 0;
  background-color: #fff;
  padding: 10px 20px;
  border: 1px solid var(--homez-link-color);
  color: var(--homez-link-color);
  font-weight: 600;
  font-size: 15px;
  border-radius: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  cursor: pointer;
}

@media (min-width: 1200px) {
  div.cmb2-wrap .label-can-drag {
    padding: 16px 40px;
    border-radius: 12px;
  }
}

div.cmb2-wrap .label-can-drag span:after {
  font-family: "flaticon_realton";
  font-weight: normal;
  content: "\f151";
  display: inline-block;
  margin-left: 8px;
}

div.cmb2-wrap .label-can-drag:active, div.cmb2-wrap .label-can-drag:hover {
  background-color: var(--homez-link-color);
  border-color: var(--homez-link-color);
  color: #fff;
}

div.cmb2-wrap .label-can-drag .form-group {
  margin: 0;
}

div.cmb2-wrap .wp-realestate-uploaded-files {
  margin-right: -10px;
  margin-left: -10px;
}

div.cmb2-wrap .wp-realestate-uploaded-files:before, div.cmb2-wrap .wp-realestate-uploaded-files:after {
  content: '';
  display: table;
  clear: both;
}

div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file {
  margin-bottom: 20px;
  position: relative;
  padding-right: 10px;
  padding-left: 10px;
  width: 120px;
  float: left;
}

@media (min-width: 1200px) {
  div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file {
    width: 260px;
  }
}

div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file .wp-realestate-remove-uploaded-file {
  position: absolute;
  top: 10px;
  left: 10px;
  display: inline-block;
  text-indent: -999em;
  width: 30px;
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  text-align: center;
  color: var(--homez-link-color);
  background: #fff;
  font-size: 15px;
  border-radius: 8px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

@media (min-width: 1200px) {
  div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file .wp-realestate-remove-uploaded-file {
    width: 45px;
    height: 45px;
    line-height: 45px;
    font-size: 20px;
    border-radius: 12px;
  }
}

div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file .wp-realestate-remove-uploaded-file:before {
  text-indent: 0;
  font-weight: 400;
  font-family: 'flaticon_realton';
  content: "\f149";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file .wp-realestate-remove-uploaded-file:hover {
  color: #fff;
  background: #dc3545;
}

div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file .wp-realestate-uploaded-file-name code {
  display: inline-block;
}

div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file-preview {
  display: block;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

@media (min-width: 1200px) {
  div.cmb2-wrap .wp-realestate-uploaded-files .wp-realestate-uploaded-file-preview {
    border-radius: 12px;
  }
}

div.cmb2-wrap .cmb2-checkbox-list {
  padding: 0;
  margin: 0 0 -10px;
  list-style: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
}

@media (min-width: 1200px) {
  div.cmb2-wrap .cmb2-checkbox-list {
    margin-bottom: -20px;
  }
}

div.cmb2-wrap .cmb2-checkbox-list li {
  width: 50%;
  margin: 0 0 10px;
  line-height: 1;
}

@media (min-width: 992px) {
  div.cmb2-wrap .cmb2-checkbox-list li {
    width: 33%;
  }
}

@media (min-width: 1200px) {
  div.cmb2-wrap .cmb2-checkbox-list li {
    width: 25%;
    margin: 0 0 20px;
  }
}

div.cmb2-wrap .cmb2-checkbox-list [type="checkbox"] {
  display: none;
}

div.cmb2-wrap .cmb2-checkbox-list [type="checkbox"]:checked + label:before {
  content: "\f00c";
  color: #fff;
  background: var(--homez-link-color);
}

div.cmb2-wrap .cmb2-checkbox-list label {
  cursor: pointer;
  font-weight: 400;
}

div.cmb2-wrap .cmb2-checkbox-list label:before {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  font-size: 8px;
  text-align: center;
  line-height: 14px;
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid var(--homez-link-color);
  margin-right: 10px;
  vertical-align: text-top;
}

div.cmb2-wrap .select2-selection__choice {
  display: inline-block;
  padding: 5px 7px !important;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 8px !important;
}

div.cmb2-wrap .select2-selection__choice .select2-selection__choice__remove {
  color: #dc3545 !important;
}

div.cmb2-wrap .before-group-row .submit-button-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
}

div.cmb2-wrap .before-group-row .submit-button-wrapper .btn {
  padding: 5px 15px;
}

div.cmb2-wrap .before-group-row .submit-button-wrapper .job-submission-next-btn {
  margin-left: auto;
}

div.cmb2-wrap .before-group-row {
  overflow: hidden;
  clear: both;
  background-color: #fff;
  margin-bottom: 20px;
  padding: 0 20px 20px;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

@media (min-width: 1200px) {
  div.cmb2-wrap .before-group-row {
    border-radius: 12px;
    padding: 0 1.875rem 1.875rem;
    margin-bottom: 1.875rem;
  }
}

div.cmb2-wrap .columns-1 .before-group-row-inner .cmb-row:last-child {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  div.cmb2-wrap .columns-2 .before-group-row-inner {
    margin-left: -0.9375rem;
    margin-right: -0.9375rem;
  }
  div.cmb2-wrap .columns-2 .before-group-row-inner > div {
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    width: 50%;
    float: left;
  }
}

div.cmb2-wrap .columns-2 .before-group-row-inner > [data-fieldtype="title"],
div.cmb2-wrap .columns-2 .before-group-row-inner > .submit-button-wrapper {
  width: 100%;
}

@media (min-width: 768px) {
  div.cmb2-wrap .columns-3 .before-group-row-inner {
    margin-left: -0.9375rem;
    margin-right: -0.9375rem;
  }
  div.cmb2-wrap .columns-3 .before-group-row-inner > div {
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    width: 33.33%;
    float: left;
  }
}

div.cmb2-wrap .columns-3 .before-group-row-inner > [data-fieldtype="title"],
div.cmb2-wrap .columns-3 .before-group-row-inner > .submit-button-wrapper {
  width: 100%;
}

div.cmb2-wrap .cmb2-metabox-title {
  font-size: 17px;
  color: var(--homez-heading-color);
  margin: 0 -20px;
  padding: 10px 20px;
  border-bottom: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  div.cmb2-wrap .cmb2-metabox-title {
    padding: 15px 1.875rem;
    margin: 0 -30px;
  }
}

div.cmb2-wrap .cmb-type-group .inside {
  padding: 20px 15px 15px;
  margin-left: -15px;
  margin-right: -15px;
  width: calc(100% + 15px);
  max-width: none;
  border-width: 0 1px 1px !important;
  border-color: #E9E9E9 !important;
  border-style: solid !important;
  border-radius: 0 0 8px 8px;
}

div.cmb2-wrap .cmb-type-group .inside label {
  font-size: 14px;
}

div.cmb2-wrap .cmb-type-group .cmb-group-name {
  font-size: 14px;
  font-weight: 700;
  color: var(--homez-heading-color);
  margin: 0;
}

div.cmb2-wrap .cmb-type-group .cmb-add-group-row {
  background: #fff;
  color: #198754;
  border: 1px solid #198754;
  font-size: 0.875rem;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  padding: 10px 20px;
  border-radius: 8px;
}

div.cmb2-wrap .cmb-type-group .cmb-add-group-row:hover, div.cmb2-wrap .cmb-type-group .cmb-add-group-row:focus {
  color: #fff;
  background: #198754;
  border-color: #198754;
}

div.cmb2-wrap .cmb-type-group .cmb-row:not(:last-of-type) {
  border: 0;
}

div.cmb2-wrap .cmb-type-group .cmb-repeatable-grouping {
  border: 0 !important;
}

div.cmb2-wrap .cmb-type-group .cmb-repeatable-grouping .cmb-group-title {
  cursor: pointer;
  padding-top: 14px;
  padding-bottom: 14px;
  background: #fff;
  font-size: -0.125rem;
  font-weight: 500;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  margin-left: -15px;
  margin-right: -15px;
}

div.cmb2-wrap .cmb-type-group .cmb-repeatable-grouping:not(.closed) .cmb-group-title {
  border-radius: 8px 8px 0 0;
}

div.cmb2-wrap .cmb-type-group .cmb-remove-row {
  padding-top: 10px;
}

div.cmb2-wrap .cmb-type-group .cmb-remove-row .move-down,
div.cmb2-wrap .cmb-type-group .cmb-remove-row .move-up {
  display: inline-block;
  padding: 5px;
  background-color: var(--homez-theme-color);
  color: #fff;
  border-radius: 4px;
  margin: 0 2px !important;
}

div.cmb2-wrap .cmb-type-group .cmb-remove-row .move-down .dashicons,
div.cmb2-wrap .cmb-type-group .cmb-remove-row .move-up .dashicons {
  margin: 0;
}

div.cmb2-wrap .cmb-type-group .alignright,
div.cmb2-wrap .cmb-type-group .alignleft {
  margin: 0;
}

div.cmb2-wrap .cmb-type-group .dashicons-before.cmb-remove-group-row {
  margin-top: 5px;
}

div.cmb2-wrap .cmb-type-group .cmbhandle {
  margin-top: 10px;
}

@media (min-width: 768px) {
  div.cmb2-wrap .cmb-type-group .cmb-row[data-fieldtype="select"],
  div.cmb2-wrap .cmb-type-group .cmb-row[data-fieldtype="text"] {
    float: left;
    width: 50%;
  }
}

div.cmb2-wrap .cmb-type-group .cmb-remove-field-row,
div.cmb2-wrap .cmb-type-group .cmb-repeat-group-field {
  padding: 0 15px;
  border: 0 !important;
}

div.cmb2-wrap .cmb-type-group .cmb-remove-field-row {
  margin-bottom: 0;
}

div.cmb2-wrap .cmb-type-group .cmb-remove-group-row-button {
  border: 1px solid #dc3545;
  background: #fff;
  color: #dc3545;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 8px;
}

div.cmb2-wrap .cmb-type-group .cmb-remove-group-row-button:hover {
  color: #fff;
  background: #dc3545;
  border-color: #dc3545;
}

.box-inner-white {
  background: #fff;
}

.box-inner-white .inner {
  padding: 15px;
}

@media (min-width: 1200px) {
  .box-inner-white .inner {
    padding: 1.875rem;
  }
}

.warpper-action-property a {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: var(--homez-link-color);
  background: #fff;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin-left: 2px;
  font-size: 15px;
}

.warpper-action-property a:first-child {
  margin-left: 0;
}

.warpper-action-property a:hover, .warpper-action-property a:focus {
  color: var(--homez-link-color);
  background: #F7F7F7;
}

.property-submission-preview-form-wrapper .wrapper-action-property {
  padding: 0 15px;
}

.property-submission-preview-form-wrapper .breadcrumbs-simple .breadcrumb {
  background-color: transparent;
}

.submission-form-wrapper {
  margin: 30px 0;
  text-align: center;
  font-weight: 600;
  font-size: 22px;
  color: var(--homez-theme-color);
}

@media (min-width: 1200px) {
  .submission-form-wrapper {
    margin: 50px 0;
  }
}

body.page-template-page-dashboard {
  background: #F7F7F7;
}

body.page-template-page-dashboard #apus-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 1;
}

@media (min-width: 992px) {
  body.page-template-page-dashboard.left-main #apus-footer {
    padding-left: 300px;
  }
  body.page-template-page-dashboard.right-main #apus-footer {
    padding-right: 300px;
  }
}

.inner-dashboard.container-fluid .main-page {
  padding: 20px 20px 0;
}

@media (min-width: 992px) {
  .inner-dashboard.container-fluid.left-main {
    padding: 0 0 0 300px;
  }
  .inner-dashboard.container-fluid.left-main .sidebar-wrapper {
    left: 0;
  }
  .inner-dashboard.container-fluid.main-right {
    padding: 0 300px 0 0;
  }
  .inner-dashboard.container-fluid.main-right .sidebar-wrapper {
    right: 0;
  }
  .inner-dashboard.container-fluid .main-page {
    width: 100%;
    padding: 30px;
  }
  .inner-dashboard.container-fluid .first-row {
    margin: 0;
  }
  .inner-dashboard.container-fluid .first-row > .sidebar-wrapper {
    padding: 0;
    width: 300px;
    height: 100vh;
    overflow-y: auto;
    scrollbar-width: thin;
    position: fixed;
    z-index: 2;
    top: 110px;
    background: #fff;
  }
  .inner-dashboard.container-fluid .first-row > .sidebar-wrapper .widget {
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}

@media (min-width: 1200px) {
  .inner-dashboard.container-fluid .main-page {
    padding: 60px 60px 30px;
  }
}

@media (max-width: 991px) {
  .inner-dashboard .first-row > .sidebar-wrapper {
    height: auto !important;
  }
}

.dashboard-box h4 {
  font-size: 15px;
  margin: 0 0 7px;
  font-weight: 400;
}

.dashboard-box .properties-count {
  font-size: 25px;
  font-weight: 600;
  line-height: 1.1;
}

@media (min-width: 1200px) {
  .dashboard-box .properties-count {
    font-size: 36px;
  }
}

.dashboard-box .inner-left {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  background-color: #F7F7F7;
  color: var(--homez-link-color);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 25px;
  text-align: center;
}

@media (min-width: 1200px) {
  .dashboard-box .inner-left {
    font-size: 30px;
    width: 80px;
    height: 80px;
  }
}

.dashboard-box:hover .inner-left {
  background: var(--homez-theme-color-007);
}

.box-white-dashboard {
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .box-white-dashboard {
    padding: 1.875rem;
    margin-bottom: 1.875rem;
    border-radius: 12px;
  }
}

.box-white-dashboard .title {
  font-size: 16px;
  margin: 0 0 15px;
}

@media (min-width: 1200px) {
  .box-white-dashboard .title {
    margin-bottom: 20px;
  }
}

.box-white-dashboard .pagination-links {
  padding-bottom: 0;
}

.box-white-dashboard .not-found, .box-white-dashboard.not-found {
  font-weight: 600;
  font-size: 1rem;
  color: #dc3545;
}

.box-white-dashboard.pb-0 .pagination {
  padding-top: 0;
}

@media (min-width: 1200px) {
  .search-form-stats {
    margin-top: 20px;
  }
}

.comment-list.list-reviews > li:last-child > .the-comment {
  border-bottom: 0;
  padding-bottom: 0;
  margin-bottom: 0;
}

.list-message-small {
  padding: 0;
  margin: 0;
  list-style: none;
}

.list-message-small > li {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .list-message-small > li {
    margin-bottom: 1.875rem;
  }
}

.list-message-small > li:last-child {
  margin-bottom: 0;
}

.list-message-small > li.unread .user-name {
  color: #dc3545;
}

.list-message-small .message-item-small {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  text-decoration: none;
}

.list-message-small .message-item-small:hover .message-title, .list-message-small .message-item-small:focus .message-title {
  text-decoration: underline;
}

.list-message-small .avatar {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  align-items: center;
  -webkit-align-items: center;
}

.list-message-small .avatar img {
  margin: 0;
}

.list-message-small .content {
  padding-left: 15px;
  overflow: hidden;
  width: calc(100% - 50px);
}

.list-message-small .user-name {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 2px;
}

.list-message-small .message-title {
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-message-small .message-time {
  font-size: 12px;
  font-weight: 400;
}

.title-profile {
  font-size: 25px;
  margin: 0 0 20px;
}

@media (min-width: 1200px) {
  .title-profile {
    font-size: 30px;
    margin-bottom: 40px;
  }
}

.max-650 {
  max-width: 650px;
}

.box-dashboard-message {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .box-dashboard-message {
    margin-bottom: 1.875rem;
  }
}

.message-section-wrapper .list-message-inner {
  position: relative;
}

@media (min-width: 1200px) {
  .message-section-wrapper .list-message-inner {
    height: calc(100% - 116px);
  }
}

@media (min-width: 1200px) {
  .message-section-wrapper .message-inner {
    height: 770px;
  }
}

.message-section-wrapper .list-message-wrapper {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  padding: 20px;
}

@media (min-width: 1200px) {
  .message-section-wrapper .list-message-wrapper {
    border-radius: 12px;
  }
}

@media (max-width: 1199px) {
  .message-section-wrapper .list-message-wrapper {
    margin-bottom: 20px;
  }
}

.message-section-wrapper .list-message-wrapper .list-message-inner {
  margin-left: -20px;
  margin-right: -20px;
}

@media (min-width: 1200px) {
  .message-section-wrapper .list-message-wrapper {
    padding: 1.875rem;
  }
  .message-section-wrapper .list-message-wrapper .list-message-inner {
    margin-left: -1.875rem;
    margin-right: -1.875rem;
  }
}

.message-section-wrapper .list-message-wrapper .loadmore-action {
  padding-top: 5px;
  text-align: center;
}

.message-section-wrapper .list-message-wrapper .loadmore-message-btn {
  font-weight: 700;
  font-size: 14px;
}

.message-section-wrapper .replies-content {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
}

@media (min-width: 1200px) {
  .message-section-wrapper .replies-content {
    border-radius: 12px;
    display: flex;
    -webkit-display: flex;
    flex-direction: column;
    -webkit-flex-direction: column;
    /* Safari 6.1+ */
  }
  .message-section-wrapper .replies-content > div {
    width: 100%;
  }
}

.message-section-wrapper .replies-content .content-box-white {
  position: relative;
  padding: 20px;
  height: calc(100% - 71px);
  overflow: hidden;
}

@media (min-width: 1200px) {
  .message-section-wrapper .replies-content .content-box-white {
    padding: 1.875rem;
    padding-bottom: 100px;
    height: calc(100% - 81px);
  }
}

@media (min-width: 1200px) {
  .message-section-wrapper .replies-content .content-box-white .reply-message-form-wrapper {
    z-index: 1;
    position: absolute;
    bottom: 0;
    left: 1.875rem;
    right: 1.875rem;
  }
}

.message-section-wrapper .replies-content .recipient-info .message-item {
  max-width: 55%;
}

.message-section-wrapper .recipient-info {
  padding: 0 20px;
}

@media (min-width: 1200px) {
  .message-section-wrapper .recipient-info {
    padding: 0 1.875rem;
  }
}

.message-section-wrapper .recipient-info .recipient-info-inner {
  border-bottom: 1px solid #E9E9E9;
  padding: 10px 0;
}

@media (min-width: 1200px) {
  .message-section-wrapper .recipient-info .recipient-info-inner {
    padding: 20px 0;
  }
}

.message-section-wrapper .recipient-info .message-title {
  color: #717171;
  font-size: 13px;
}

.message-section-wrapper .recipient-info .delete-message-btn {
  color: var(--homez-link-color);
  text-decoration: underline !important;
  margin-top: 10px;
}

@media (min-width: 1200px) {
  .message-section-wrapper .recipient-info .delete-message-btn {
    margin-top: 12px;
  }
}

.message-section-wrapper .recipient-info .delete-message-btn:hover, .message-section-wrapper .recipient-info .delete-message-btn:focus {
  color: #dc3545;
}

.message-section-wrapper .search-message-form {
  padding-bottom: 15px;
}

.message-section-wrapper .search-message-form .search-wrapper-message {
  margin-bottom: 20px;
}

.list-options-action {
  list-style: none;
  padding: 0;
  margin: 0 0 5px;
  font-size: 14px;
  line-height: 1;
  text-align: center;
}

.list-options-action > li {
  display: inline-block;
  margin-right: 10px;
}

.list-options-action > li:last-child {
  margin-right: 0;
}

.list-options-action label {
  font-weight: 600;
  color: var(--homez-link-color);
  cursor: pointer;
}

.list-options-action label:hover, .list-options-action label:focus {
  color: var(--homez-theme-color);
}

.list-options-action [type="radio"] {
  display: none;
}

.list-options-action [type="radio"]:checked + label {
  color: var(--homez-theme-color);
}

.list-message {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-message li {
  padding: 10px 20px;
}

@media (min-width: 1200px) {
  .list-message li {
    padding: 15px 30px;
  }
}

.list-message li.unread .user-name {
  color: #dc3545;
}

.list-message li.active {
  background-color: var(--homez-theme-color-007);
}

.message-item {
  clear: both;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
}

.message-item div.avatar {
  line-height: 50px;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
}

.message-item div.avatar img {
  margin: 0;
}

.message-item .content {
  padding-left: 10px;
  width: calc(100% - 50px);
}

.message-item .user-name {
  font-size: 0.875rem;
  margin: 0 0 2px;
  text-transform: capitalize;
}

.message-item .message-time {
  color: #717171;
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
}

.message-item .message-title {
  color: #717171;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reply-message-form-wrapper .wrapper-form {
  padding: 10px 0 0;
}

@media (min-width: 1200px) {
  .reply-message-form-wrapper .wrapper-form {
    padding: 25px 200px 25px 0;
  }
}

.reply-message-form-wrapper .wrapper-form:before {
  content: '';
  background-color: #E9E9E9;
  width: 100%;
  height: 1px;
  top: 0;
  left: 0;
  position: absolute;
}

.reply-message-form-wrapper .reply-message-form {
  position: relative;
}

.reply-message-form-wrapper .reply-message-form textarea {
  height: 50px;
  background: transparent;
  border: none;
  resize: none;
  padding: 12px 0;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  outline: none;
}

.reply-message-form-wrapper .reply-message-form .reply-message-btn {
  text-transform: uppercase;
}

@media (min-width: 1200px) {
  .reply-message-form-wrapper .reply-message-form .reply-message-btn {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 0;
  }
}

.list-replies {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-replies li {
  margin-bottom: 25px;
  overflow: hidden;
  clear: both;
}

.list-replies .avatar {
  line-height: 50px;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  float: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
}

.list-replies .avatar img {
  margin: 0;
}

.list-replies .post-date {
  color: #717171;
  font-size: 13px;
}

.list-replies .post-content {
  margin-top: 15px;
  display: inline-block;
  padding: 10px 15px;
  border-radius: 8px;
  background: #F1FCFA;
  color: var(--homez-link-color);
  line-height: 1.9;
}

@media (min-width: 1200px) {
  .list-replies .post-content {
    max-width: 70%;
    padding: 15px 25px;
    border-radius: 12px;
  }
}

.list-replies .name-author {
  margin: 0 10px;
  font-size: 0.875rem;
}

.list-replies .user-reply .post-content {
  background: #F1FCFA;
}

.list-replies .yourself-reply {
  text-align: right;
}

.list-replies .yourself-reply .post-content {
  background: var(--homez-theme-color-007);
}

.list-replies .yourself-reply .info-header,
.list-replies .yourself-reply .info-author {
  flex-direction: row-reverse;
}

.list-replies-inner {
  position: relative;
  height: 100%;
}

.list-replies-inner .loadmore-action {
  text-align: center;
  position: absolute;
  top: -5px;
  left: 0;
  width: 100%;
  z-index: 1;
}

.list-replies-inner .loadmore-replied-btn {
  color: var(--homez-text-color);
}

.list-replies-inner .loadmore-replied-btn:hover, .list-replies-inner .loadmore-replied-btn:focus {
  color: var(--homez-link-color);
}

.user-name {
  font-size: 20px;
  margin: 0 0 10px;
}

@media (min-width: 1200px) {
  .user-name {
    margin: 0 0 20px;
  }
}

@media (max-width: 1199px) {
  .message-section-wrapper .message-inner .list-message-wrapper {
    position: relative;
    display: none;
  }
  .list-message-inner {
    position: relative;
    max-height: 200px;
  }
  .list-replies-inner {
    overflow-x: auto;
    max-height: 300px;
  }
  .toggle-message-btn {
    border: none;
    padding: 0 !important;
    font-weight: 700;
    color: #198754;
    margin: 0 0 20px;
  }
  .toggle-message-btn i {
    margin-left: 5px;
  }
  .filter-options {
    margin-left: 15px;
    margin-right: 15px;
  }
}

@media (min-width: 1200px) {
  .toggle-message-btn {
    display: none;
  }
}

.replies-content,
.list-message-wrapper {
  position: relative;
}

.replies-content:before,
.list-message-wrapper:before {
  display: block;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9) url("../images/loading.gif") no-repeat center 180px/30px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  z-index: 1;
  visibility: hidden;
}

.replies-content.loading:before,
.list-message-wrapper.loading:before {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.agent-team .agent-thumbnail {
  width: 70px;
}

.agent-team .agent-thumbnail + .right-inner {
  padding-left: 20px;
  width: calc(100% - 70px);
}

.agent-team .entry-title {
  margin: 0 0 3px;
  font-size: 16px;
}

.agent-team .agent-info,
.agent-team .agent-information {
  width: 100%;
}

.agent-team .property-location a {
  color: var(--homez-text-color);
}

.agent-team .property-location a:hover, .agent-team .property-location a:focus {
  color: var(--homez-theme-color);
}

.agency-agents-list-inner .agent-team {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #E9E9E9;
}

.agency-agents-list-inner .agent-team:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: 0;
}

.agency-add-agents-form {
  position: relative;
}

.agency-add-agents-form:before {
  display: block;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9) url("../images/loading.gif") no-repeat center 100px/30px auto;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  z-index: 1;
  visibility: hidden;
}

.agency-add-agents-form.loading:before {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

.agency-add-agents-form .team-agent-wrapper {
  margin-bottom: 25px;
}

.team-agent-inner,
.team-agent-list-inner {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  -o-align-items: center;
}

.team-agent-inner .team-agent-img,
.team-agent-inner .team-agent-list-img,
.team-agent-list-inner .team-agent-img,
.team-agent-list-inner .team-agent-list-img {
  width: 80px;
  padding-right: 12px;
}

.team-agent-inner .team-agent-img img,
.team-agent-inner .team-agent-list-img img,
.team-agent-list-inner .team-agent-img img,
.team-agent-list-inner .team-agent-list-img img {
  border-radius: 6px;
}

.team-agent-inner .team-agent-content,
.team-agent-inner .team-agent-list-content,
.team-agent-list-inner .team-agent-content,
.team-agent-list-inner .team-agent-list-content {
  width: calc(100% - 80px);
  font-weight: 500;
}

.team-agent-inner .team-agent-remove,
.team-agent-list-inner .team-agent-remove {
  color: #fff;
  margin-left: 10px;
  background-color: #dc3545;
  opacity: 0.9;
  filter: alpha(opacity=90);
  display: inline-block;
  padding: 2px 7px;
  font-size: 10px;
  cursor: pointer;
  border-radius: 2px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

.team-agent-inner .team-agent-remove:hover, .team-agent-inner .team-agent-remove:focus,
.team-agent-list-inner .team-agent-remove:hover,
.team-agent-list-inner .team-agent-remove:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

@media (min-width: 768px) {
  .profile-form-wrapper .cmb-row[class*="agency-title"],
  .profile-form-wrapper .cmb-row[class*="agency-email"],
  .profile-form-wrapper .cmb-row[class*="agency-website"],
  .profile-form-wrapper .cmb-row[class*="agency-phone"],
  .profile-form-wrapper .cmb-row[class*="agency-fax"],
  .profile-form-wrapper .cmb-row[class*="agency-whatsapp"],
  .profile-form-wrapper .cmb-row[class*="agency-skype"],
  .profile-form-wrapper .cmb-row[class*="agency-opening-hours"],
  .profile-form-wrapper .cmb-row[class*="agency-languages"],
  .profile-form-wrapper .cmb-row[class*="agency-license"],
  .profile-form-wrapper .cmb-row[class*="agency-tax-number"],
  .profile-form-wrapper .cmb-row[class*="agency-location"],
  .profile-form-wrapper .cmb-row[class*="agency-address"],
  .profile-form-wrapper .cmb-row[class*="agent-job"],
  .profile-form-wrapper .cmb-row[class*="agent-email"],
  .profile-form-wrapper .cmb-row[class*="agent-website"],
  .profile-form-wrapper .cmb-row[class*="agent-phone"],
  .profile-form-wrapper .cmb-row[class*="agent-whatsapp"],
  .profile-form-wrapper .cmb-row[class*="agent-languages"],
  .profile-form-wrapper .cmb-row[class*="agent-location"],
  .profile-form-wrapper .cmb-row[class*="agent-address"],
  .profile-form-wrapper .cmb-row[class*="agent-fax"] {
    width: calc(50% - 15px);
    float: left;
  }
  .profile-form-wrapper .cmb-row[class*="agency-address"],
  .profile-form-wrapper .cmb-row[class*="agency-tax-number"],
  .profile-form-wrapper .cmb-row[class*="agency-languages"],
  .profile-form-wrapper .cmb-row[class*="agency-skype"],
  .profile-form-wrapper .cmb-row[class*="agency-fax"],
  .profile-form-wrapper .cmb-row[class*="agency-website"],
  .profile-form-wrapper .cmb-row[class*="agent-email"],
  .profile-form-wrapper .cmb-row[class*="agent-phone"],
  .profile-form-wrapper .cmb-row[class*="agent-whatsapp"],
  .profile-form-wrapper .cmb-row[class*="agent-location"] {
    margin-left: 30px;
  }
}

.change-profile-form .sub {
  font-size: 16px;
  margin: 0 0 20px;
}

#wp-private-message-popup-message,
#wp-realestate-popup-message {
  position: fixed;
  z-index: 99;
  top: 40%;
  right: 0;
  font-size: 14px;
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
}

#wp-private-message-popup-message .alert,
#wp-realestate-popup-message .alert {
  margin: 0;
  font-weight: 500;
}

.top-dashboard-search {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .top-dashboard-search {
    margin-bottom: 1.875rem;
  }
}

@media (max-width: 767px) {
  .top-dashboard-search {
    display: block !important;
  }
  .top-dashboard-search > div {
    width: 100%;
  }
  .top-dashboard-search > div.sortby-form {
    margin-top: 10px;
  }
}

table.property-table {
  margin: 0;
  border: none;
}

table.property-table thead td {
  background-color: #F7F7F7;
  color: var(--homez-link-color);
  white-space: nowrap;
  font-size: 15px;
  font-weight: 600;
}

table.property-table thead td:first-child {
  border-radius: 8px 0 0 8px;
}

@media (min-width: 1200px) {
  table.property-table thead td:first-child {
    border-radius: 12px 0 0 12px;
  }
}

table.property-table thead td:last-child {
  border-radius: 0 8px 8px 0;
}

@media (min-width: 1200px) {
  table.property-table thead td:last-child {
    border-radius: 0 12px 12px 0;
  }
}

table.property-table td {
  border: 0;
  border-bottom: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  table.property-table td {
    padding: 16px 25px;
  }
}

@media (min-width: 1200px) {
  table.property-table tbody td {
    padding: 25px;
  }
}

table.property-table tr:last-child td {
  border-bottom: 0 !important;
}

table.property-table .date {
  white-space: nowrap;
}

table.property-table td.title,
table.property-table td.property-table-info-content-title,
table.property-table td.id_property {
  font-size: 15px;
  color: var(--homez-link-color);
  font-weight: 600;
  white-space: nowrap;
}

table.property-table .value {
  font-weight: 600;
}

table.property-table .success {
  color: #198754;
}

table.property-table .pending {
  color: #ffc107;
}

table.property-table .alert-query .text {
  color: var(--homez-text-color);
  font-weight: 400;
}

table.property-table .alert-query .value {
  font-weight: 600;
  color: var(--homez-link-color);
}

table.property-table .alert-query,
table.property-table .package-info-wrapper {
  min-width: 200px;
}

.inner-user-property-packaged {
  margin-bottom: 20px;
}

.inner-user-property-packaged [type="radio"] {
  display: none;
}

.inner-user-property-packaged [type="radio"]:checked + label {
  border-color: var(--homez-theme-color);
}

.inner-user-property-packaged label {
  cursor: pointer;
  display: block;
  border: 1px solid #E9E9E9;
  border-radius: 8px;
  padding: 20px;
}

.inner-user-property-packaged .value {
  display: block;
  font-weight: 600;
  font-size: 1rem;
  margin: 0 0 5px;
}

.filter-agency-form .button,
.filter-agent-form .button {
  width: 100%;
}

.filter-agency-form label,
.filter-agent-form label {
  display: none;
}

.filter-agency-form .action-location,
.filter-agent-form .action-location {
  position: relative;
}

.filter-agency-form .action-location .clear-location,
.filter-agent-form .action-location .clear-location {
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
  position: absolute;
  top: 14px;
  right: 14px;
  cursor: pointer;
  background-color: #fff;
  color: #dc3545;
  z-index: 2;
}

.filter-agency-form .action-location .find-me,
.filter-agent-form .action-location .find-me {
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
  position: absolute;
  top: 12px;
  right: 14px;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.filter-agency-form .action-location .find-me:hover, .filter-agency-form .action-location .find-me:focus,
.filter-agent-form .action-location .find-me:hover,
.filter-agent-form .action-location .find-me:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

.filter-agency-form .action-location .find-me:before,
.filter-agent-form .action-location .find-me:before {
  content: "\f110";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.filter-agency-form .action-location .find-me.loading,
.filter-agent-form .action-location .find-me.loading {
  animation: rotate 700ms linear 0s normal none infinite running;
  -webkit-animation: rotate 700ms linear 0s normal none infinite running;
}

.agencies-filter-top-sidebar-wrapper,
.agents-filter-top-sidebar-wrapper {
  padding: 0;
}

.agencies-filter-top-sidebar-wrapper .filter-agency-form,
.agencies-filter-top-sidebar-wrapper .filter-agent-form,
.agents-filter-top-sidebar-wrapper .filter-agency-form,
.agents-filter-top-sidebar-wrapper .filter-agent-form {
  margin-left: -0.9375rem;
  margin-right: -0.9375rem;
}

.agencies-filter-top-sidebar-wrapper .filter-agency-form:after,
.agencies-filter-top-sidebar-wrapper .filter-agent-form:after,
.agents-filter-top-sidebar-wrapper .filter-agency-form:after,
.agents-filter-top-sidebar-wrapper .filter-agent-form:after {
  clear: both;
  content: " ";
  display: table;
}

.agencies-filter-top-sidebar-wrapper .filter-agency-form > div,
.agencies-filter-top-sidebar-wrapper .filter-agent-form > div,
.agents-filter-top-sidebar-wrapper .filter-agency-form > div,
.agents-filter-top-sidebar-wrapper .filter-agent-form > div {
  float: left;
  width: 100%;
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
}

@media (min-width: 992px) {
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .form-group-title,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .form-group-title,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .form-group-title,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .form-group-title {
    width: 32%;
  }
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location {
    width: 50%;
  }
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location .form-group-inner,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location .form-group-inner,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location .form-group-inner,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location .form-group-inner {
    margin-left: -0.9375rem;
    margin-right: -0.9375rem;
  }
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location .search_distance_wrapper,
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location .action-location,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location .search_distance_wrapper,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location .action-location,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location .search_distance_wrapper,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .form-group-center-location .action-location,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location .search_distance_wrapper,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .form-group-center-location .action-location {
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    width: 50%;
    float: left;
  }
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .form-group-submit,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .form-group-submit,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .form-group-submit,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .form-group-submit {
    width: 18%;
  }
}

@media (min-width: 992px) {
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .action-location .clear-location,
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .action-location .find-me,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .action-location .clear-location,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .action-location .find-me,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .action-location .clear-location,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .action-location .find-me,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .action-location .clear-location,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .action-location .find-me {
    right: 1.875rem;
  }
}

@media (max-width: 991px) {
  .agencies-filter-top-sidebar-wrapper .filter-agency-form .search_distance_wrapper,
  .agencies-filter-top-sidebar-wrapper .filter-agent-form .search_distance_wrapper,
  .agents-filter-top-sidebar-wrapper .filter-agency-form .search_distance_wrapper,
  .agents-filter-top-sidebar-wrapper .filter-agent-form .search_distance_wrapper {
    margin: 15px 0 10px;
  }
}

#compare-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 5;
  width: 340px;
  height: 100vh;
  max-width: 85%;
  min-height: 100vh;
  background-color: #fff;
  padding: 40px 20px 70px;
  border-left: 3px solid var(--homez-theme-color);
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  display: none;
}

#compare-sidebar.active {
  display: block;
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
}

#compare-sidebar.open {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

#compare-sidebar .property-list-simple {
  margin-bottom: 20px;
}

#compare-sidebar .title {
  font-size: 20px;
  margin: 0;
  height: 50px;
}

#compare-sidebar .compare-sidebar-inner {
  height: calc(100% - 50px);
}

#compare-sidebar .compare-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background-color: #fff;
}

#compare-sidebar .compare-list {
  height: 100%;
}

#compare-sidebar .compare-sidebar-btn {
  position: absolute;
  top: 40%;
  right: 100%;
  white-space: nowrap;
  display: inline-block;
  padding: 7px 20px;
  font-weight: 600;
  color: #fff;
  background-color: var(--homez-theme-color);
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  opacity: 0.8;
  filter: alpha(opacity=80);
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transform-origin: 100% 100%;
  -moz-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}

#compare-sidebar .compare-sidebar-btn:hover, #compare-sidebar .compare-sidebar-btn:focus {
  opacity: 1;
  filter: alpha(opacity=100);
}

ul.messages {
  list-style: none;
  padding: 0;
  margin: 0;
}

.valuation-item .progress {
  height: 10px;
  margin-top: 5px;
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .valuation-item .progress {
    margin-bottom: 25px;
  }
}

.valuation-item .progress-bar {
  line-height: 10px;
  background-color: #1BC4BD;
}

.valuation-item .valuation-label {
  font-weight: 600;
  color: var(--homez-link-color);
}

.valuation-item:last-child .progress {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .property-public-facilities .property-public-facility-wrapper {
    margin-top: 10px;
    width: 50%;
    float: left;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
  }
  .property-public-facilities > .clearfix {
    margin-top: -10px;
    margin-left: -0.9375rem;
    margin-right: -0.9375rem;
  }
}

.property-public-facility {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}

.property-public-facility .property-public-facility-title {
  min-width: 110px;
  font-weight: 600;
  color: var(--homez-link-color);
}

.energy-inner-top .list {
  color: var(--homez-link-color);
}

.energy-inner-top .list li {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  -ms-align-items: center;
  margin-bottom: 10px;
}

@media (min-width: 1200px) {
  .energy-inner-top .list li {
    margin-bottom: 15px;
  }
}

.energy-inner-top .list li:last-child {
  margin-bottom: 0;
}

.energy-inner-top .list .value {
  margin-left: auto;
}

.energy-inner-top + .energy-inner {
  margin-top: 80px;
}

.energy-group {
  position: relative;
  color: #fff;
  padding: 2px 0;
}

.energy-group .indicator-energy {
  position: absolute;
  text-align: left;
  white-space: nowrap;
  background: #999999;
  bottom: 100%;
  font-size: 12px;
  left: 0;
  padding: 4px 10px;
  margin-bottom: 18px;
}

.energy-group .indicator-energy:before {
  display: block;
  background-color: transparent;
  width: 10px;
  height: 10px;
  content: '';
  position: absolute;
  border-width: 5px;
  border-style: solid;
  border-color: #999999 transparent transparent #999999;
  left: 0;
  top: 100%;
}

.energy-inner {
  position: relative;
}

.energy-inner > div {
  width: 100%;
  text-align: center;
  word-break: break-word;
}

.energy-aplus {
  background-color: #58a05f;
}

.energy-a {
  background-color: #88b45f;
}

.energy-b {
  background-color: #c6d35e;
}

.energy-c {
  background-color: #fdf05b;
}

.energy-d {
  background-color: #e4b84e;
}

.energy-e {
  background-color: #c8743c;
}

.energy-e ~ .energy-group .indicator-energy {
  left: auto;
  right: 0;
}

.energy-e ~ .energy-group .indicator-energy:before {
  left: auto;
  right: 0;
  border-color: #999999 #999999 transparent transparent;
}

.energy-f {
  background-color: #ba3633;
}

.energy-g {
  background-color: #ca403d;
}

.energy-h {
  background-color: #cc4845;
}

@media (max-width: 767px) {
  .energy-c .indicator-energy,
  .energy-d .indicator-energy,
  .energy-e .indicator-energy {
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
  }
  .energy-c .indicator-energy:before,
  .energy-d .indicator-energy:before,
  .energy-e .indicator-energy:before {
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    border-color: #999999 transparent transparent;
  }
}

.floor-item {
  background: transparent;
  border: 0;
  margin-bottom: 20px;
}

.floor-item:last-child {
  margin-bottom: 0;
}

.floor-item .accordion-button {
  border-bottom: 1px solid #E9E9E9;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-decoration: none;
  background: transparent !important;
  padding: 0 0 20px 25px;
  font-size: 0.875rem;
  font-weight: normal;
}

.floor-item .accordion-button:after {
  position: absolute;
  top: 5px;
  left: 0;
  background: none;
  content: "\f108";
  font-size: 16px;
  line-height: 1;
  font-family: flaticon_realton !important;
  font-weight: 400;
  color: var(--homez-link-color);
  width: auto;
  height: auto;
}

.floor-item .accordion-button h3 {
  font-size: 0.875rem;
  margin: 0;
}

.floor-item .content-accordion {
  padding: 20px 0;
  text-align: center;
}

.floor-item .metas {
  color: var(--homez-link-color);
}

.floor-item .metas .subtitle {
  font-weight: 600;
}

.floor-item .metas > div + div {
  margin-left: 10px;
}

@media (min-width: 1200px) {
  .floor-item .metas > div + div {
    margin-left: 20px;
  }
}

@media (max-width: 767px) {
  .floor-item .metas {
    margin-top: 5px;
    justify-content: start !important;
  }
}

.print-detail {
  padding: 15px 1.875rem;
  margin: auto;
  max-width: 1000px;
  background-color: #fff;
}

.print-detail .title-inner,
.print-detail h3 {
  font-size: 20px;
  margin: 0 0 20px;
}

.print-detail > div {
  margin-bottom: 40px;
}

.print-detail .print-header-top {
  margin-bottom: 10px;
}

.print-logo {
  max-width: 280px;
  display: block;
  margin: auto;
  text-align: center;
}

.print-logo .tag-line {
  display: inline-block;
  margin-top: 10px;
}

.print-header-middle {
  padding: 15px 0;
  margin: 0 !important;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
}

.print-header-middle h1 {
  margin: 0;
  font-size: 30px;
}

.print-header-middle .print-header-middle-right {
  margin-left: auto;
}

.print-main-image {
  position: relative;
}

.print-main-image .qr-image {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}

.print-block .title {
  font-size: 16px;
  margin: 0;
}

.print-block .agent-media {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

.print-block .agent-media .media-image-left {
  width: 120px;
  padding-right: 15px;
}

.print-block .agent-media .media-image-left .media-body-right {
  width: calc(100% - 120px);
}

.print-gallery .print-gallery-image {
  margin-bottom: 1.875rem;
}

.form-theme label {
  font-weight: 600;
  color: var(--homez-link-color);
  margin: 0 0 7px;
}

.form-theme .form-group {
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .form-theme .form-group {
    margin-bottom: 25px;
  }
}

.form-theme .form-group.tax-radios-field, .form-theme .form-group.tax-checklist-field {
  margin-bottom: 10px;
}

.form-theme textarea.form-control {
  height: 150px;
  resize: none;
}

.form-theme.loading:before {
  content: '';
  background-position: center;
  background-repeat: no-repeat;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  max-height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  position: absolute;
  top: 0;
  left: -1px;
  z-index: 3;
  width: calc(100% + 2px);
  height: 100%;
}

.apus-mortgage-calculator .form-group {
  position: relative;
}

@media (min-width: 1200px) {
  .apus-mortgage-calculator .form-group {
    margin-bottom: 25px;
  }
}

@media (min-width: 1200px) {
  .mortgage-calculator-form .btn {
    min-width: 200px;
    text-align: center;
  }
  .mortgage-calculator-form .wrapper-submit {
    padding-top: 5px;
  }
}

.calculator-chart-percent {
  height: 12px;
  border-radius: 12px;
}

.calculator-chart-percent > div {
  height: 12px;
}

.monthly-payment-wrap {
  margin-bottom: 5px;
}

.monthly-payment-wrap .monthly-payment-val {
  font-weight: 600;
  font-size: 18px;
}

.list-result-calculator {
  margin-top: 1.875rem;
}

.list-result-calculator li {
  margin-bottom: 15px;
  width: 100%;
}

@media (min-width: 768px) {
  .list-result-calculator li {
    width: calc(50% - 30px);
  }
}

.list-result-calculator li:last-child {
  margin-bottom: 0;
}

.list-result-calculator li:before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #82DDD0;
  margin-right: 12px;
}

.list-result-calculator li:nth-child(2):before {
  background-color: #80A1CC;
}

.list-result-calculator li:nth-child(3):before {
  background-color: #F5DD86;
}

.list-result-calculator .name-result + span {
  color: var(--homez-link-color);
  margin-left: auto;
}

.apus-mortgage-inner-bottom {
  border-top: 1px solid #E9E9E9;
  margin-top: 0.9375rem;
  padding-top: 0.9375rem;
}

@media (min-width: 1200px) {
  .apus-mortgage-inner-bottom {
    margin-top: 1.875rem;
    padding-top: 1.875rem;
  }
}

.top-header-detail-property {
  margin: 20px auto;
}

@media (min-width: 1200px) {
  .top-header-detail-property {
    margin: 60px auto 1.875rem;
  }
}

.top-header-detail-property .property-title {
  margin: 0;
  font-size: 22px;
}

@media (min-width: 1200px) {
  .top-header-detail-property .property-title {
    font-size: 30px;
  }
}

.top-header-detail-property .property-detail-middle {
  margin-top: 12px;
  font-size: 15px;
  line-height: 1.4;
}

.top-header-detail-property .property-detail-middle i {
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
}

.top-header-detail-property .property-detail-middle > * {
  display: inline-block;
  margin-right: 10px;
  padding-right: 10px;
  border-right: 1px solid #E9E9E9;
}

.top-header-detail-property .property-detail-middle > *:last-child {
  padding: 0;
  border: 0;
  margin: 0;
}

.top-header-detail-property .property-metas {
  margin-top: 10px;
}

@media (min-width: 768px) {
  .top-header-detail-property .property-metas {
    margin-top: 20px;
  }
}

@media (max-width: 767px) {
  .top-header-detail-property .property-action-detail {
    margin-top: 10px;
  }
}

.top-header-detail-property .status-property-label {
  font-weight: 600;
  color: var(--homez-theme-color);
}

.top-header-detail-property .status-property-label:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--homez-theme-color);
  margin-right: 5px;
}

.single-property-wrapper .description-inner {
  position: relative;
}

.single-property-wrapper .description-inner:not([class*="show"]) .show-more-less-wrapper {
  display: none;
}

.single-property-wrapper .description-inner.show-less .show-more {
  display: none;
}

.single-property-wrapper .description-inner.show-more .show-less {
  display: none;
}

.property-single-v1 .property-detail-main > div {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  border: 0;
}

@media (min-width: 1200px) {
  .property-single-v1 .property-detail-main > div {
    border-radius: 12px;
    padding: 1.875rem;
  }
}

.property-single-v2 .property-detail-main > div {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  border: 0;
}

@media (min-width: 1200px) {
  .property-single-v2 .property-detail-main > div {
    border-radius: 12px;
    padding: 1.875rem;
  }
}

.property-single-v2 .property-detail-header-wrapper {
  background: #fff;
  padding-bottom: 10px;
}

.property-single-v2 .property-detail-header-wrapper .title {
  display: none;
}

@media (min-width: 1200px) {
  .property-single-v2 .list-overview li {
    width: 16.5%;
  }
}

.property-single-v2 .property-detail-gallery {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .property-single-v2 .property-detail-gallery {
    margin-bottom: 1.875rem;
  }
}

.top-header-detail-property.v2 .property-metas {
  line-height: 1.4;
}

.top-header-detail-property.v2 .property-metas > * {
  display: inline-block;
  margin-right: 10px;
  padding-right: 10px;
  border-right: 1px solid #E9E9E9;
}

.top-header-detail-property.v2 .property-metas > *:last-child {
  padding: 0;
  border: 0;
  margin: 0;
}

.property-single-v3 .sidebar .widget {
  border: 1px solid #E9E9E9;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

body.single-property-v3 {
  background: #fff;
}

body.single-property-v3 .wrapper-posts-related {
  background: #F7F7F7;
  padding: 1.875rem 0;
}

@media (min-width: 768px) {
  body.single-property-v3 .wrapper-posts-related {
    padding: 50px 0;
  }
}

@media (min-width: 1200px) {
  body.single-property-v3 .wrapper-posts-related {
    padding: 85px 0 100px;
  }
}

body.single-property-v4 {
  background: #fff;
}

body.single-property-v4 .sidebar .widget {
  border: 1px solid #E9E9E9;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

@media (min-width: 1200px) {
  .property-single-v5 .top-header-info {
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 100%;
    z-index: 2;
  }
  .property-single-v5 .top-header-info, .property-single-v5 .top-header-info a, .property-single-v5 .top-header-info .property-title, .property-single-v5 .top-header-info .property-price {
    color: #fff;
  }
  .property-single-v5 .top-header-info .action-item [class*="btn"] {
    border-color: #fff;
    background: transparent;
    color: #fff;
  }
  .property-single-v5 .top-header-info .action-item [class*="btn"][class*="added"], .property-single-v5 .top-header-info .action-item [class*="btn"][class*="remove"], .property-single-v5 .top-header-info .action-item [class*="btn"]:hover, .property-single-v5 .top-header-info .action-item [class*="btn"]:focus {
    border-color: #fff;
    color: #fff;
    background: rgba(255, 255, 255, 0.2);
  }
}

.property-single-v5 .item-wrapper > div {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .property-single-v5 .item-wrapper > div {
    border-radius: 12px;
    padding: 1.875rem;
    margin-bottom: 1.875rem;
  }
}

.property-single-v5 .item-wrapper > div > h3, .property-single-v5 .item-wrapper > div .title, .property-single-v5 .item-wrapper > div .widget-title {
  font-size: 17px;
  margin: 0 0 15px;
}

@media (min-width: 768px) {
  .property-single-v5 .item-wrapper > div > h3, .property-single-v5 .item-wrapper > div .title, .property-single-v5 .item-wrapper > div .widget-title {
    margin: 0 0 25px;
  }
}

@media (min-width: 768px) {
  .property-single-v5 .contact-form-wrapper .action-bottom {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .property-single-v5 .contact-form-wrapper .action-bottom > * {
    margin: 0;
    width: calc(50% - 10px) !important;
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .property-single-v5 .list-detail li {
    width: 100%;
  }
}

body.single-property-v5 .wrapper-posts-related {
  background: #fff;
  padding: 1.875rem 0;
}

@media (min-width: 768px) {
  body.single-property-v5 .wrapper-posts-related {
    padding: 50px 0;
  }
}

@media (min-width: 1200px) {
  body.single-property-v5 .wrapper-posts-related {
    padding: 85px 0 100px;
  }
}

.property-single-v6 .property-detail-main > div:not(.tabs-gallery-map) {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  box-shadow: 0 1px 4px 0 rgba(24, 26, 32, 0.07);
  border: 0;
}

@media (min-width: 1200px) {
  .property-single-v6 .property-detail-main > div:not(.tabs-gallery-map) {
    border-radius: 12px;
    padding: 1.875rem;
  }
}

body.single-property-v7 {
  background: #fff;
}

body.single-property-v7 .sidebar .widget {
  border: 1px solid #E9E9E9;
  -webkit-box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
  box-shadow: 0 10px 40px 0 rgba(24, 26, 32, 0.05);
}

.property-single-v7 .property-detail-header-wrapper {
  background: var(--homez-second-color);
  padding-bottom: 10px;
}

.property-single-v7 .property-detail-header-wrapper .title {
  display: none;
}

.property-single-v7 .property-detail-header-wrapper .property-detail-gallery {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  .property-single-v7 .property-detail-header-wrapper .property-detail-gallery {
    margin-bottom: 35px;
  }
}

@media (min-width: 1200px) {
  .property-single-v7 .list-overview li {
    width: 16.5%;
  }
}

.property-single-v7 .list-overview .icon {
  border: 0;
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

.property-single-v7 .list-overview .details {
  color: #fff;
}

.property-single-v7 .top-header-detail-property, .property-single-v7 .top-header-detail-property a, .property-single-v7 .top-header-detail-property .property-title, .property-single-v7 .top-header-detail-property .property-price {
  color: #fff;
}

.property-single-v7 .action-item [class*="btn"] {
  border-color: #fff;
  background: transparent;
  color: #fff;
}

.property-single-v7 .action-item [class*="btn"][class*="added"], .property-single-v7 .action-item [class*="btn"][class*="remove"], .property-single-v7 .action-item [class*="btn"]:hover, .property-single-v7 .action-item [class*="btn"]:focus {
  border-color: #fff;
  color: var(--homez-link-color);
  background: #fff;
}

.property-single-v8 .property-detail-main > div:not(.tabs-gallery-map) {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #E9E9E9;
}

@media (min-width: 1200px) {
  .property-single-v8 .property-detail-main > div:not(.tabs-gallery-map) {
    border-radius: 12px;
    padding: 1.875rem;
  }
}

.property-single-v8 .property-detail-header-wrapper {
  background: #F7F7F7;
  padding-bottom: 1.875rem;
}

body.single-property-v8 {
  background: #fff;
}

body.single-property-v8 .sidebar .widget {
  border: 1px solid #E9E9E9;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.list-detail-v9 > div {
  margin-bottom: 1.875rem;
}

.list-detail-v9 > div:last-child {
  margin-bottom: 0;
}

@media (min-width: 1200px) {
  .property-single-v9 .top-single-property {
    max-width: 390px;
    position: absolute;
    z-index: 2;
    top: 20px;
    left: 20px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
  }
}

@media (min-width: 1200px) and (min-width: 1200px) {
  .property-single-v9 .top-single-property {
    border-radius: 12px;
    padding: 1.875rem;
  }
}

@media (min-width: 1200px) {
  .property-single-v9 .top-single-property .form-theme textarea.form-control {
    height: 140px;
  }
}

.property-single-v9 .content-property-detail {
  margin-bottom: 1.875rem;
}

body.single-property-v10 {
  background: #fff;
}

body.single-property-v10 .sidebar .widget {
  background: #F7F7F7;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.property-single-v10 {
  margin-top: 1.875rem;
}

.property-single-v10 .top-header-detail-property {
  margin: 1.875rem 0;
}

.apus-mortgage-calculator-widget .form-group {
  position: relative;
}

.apus-mortgage-calculator-widget .form-group .unit {
  font-size: 13px;
  color: #717171;
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.apus-mortgage-calculator-widget .apus_mortgage_results {
  margin-top: 20px;
  text-align: center;
  font-weight: 600;
}

.sidebar-property.sticky-top {
  z-index: 1;
}

.box-white-mobile {
  border-radius: 8px;
  padding: 20px;
  background: #fff;
}

@media (min-width: 1200px) {
  .main-left-space {
    padding-right: 75px;
  }
}

.widget-agents .slick-carousel .slick-dots {
  padding: 10px 0 0;
}

.message-notification {
  font-size: 22px;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  position: relative;
  background: #F7F7F7;
  color: var(--homez-link-color);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}

.message-notification .unread-count {
  font-size: 11px;
  padding: 4px 8px;
  font-weight: 600;
  line-height: 1;
  border-radius: 50%;
  position: absolute;
  top: -5px;
  left: -7px;
}

.message-notification:hover, .message-notification:focus {
  background: #e5e5e5;
}

.property-submission-preview-form-wrapper .property-single-layout > .container {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
}

/* 12. responsive */
/*
*  Responsive
*/
@media (min-width: 1200px) {
  .row-margin-left > .elementor-container {
    overflow: hidden;
    width: calc( 1200px + ((100vw - 1200px) / 2));
    max-width: calc( 1200px + ((100vw - 1200px) / 2)) !important;
    left: calc( (100vw - 1200px) / 4);
    padding-right: calc( (100vw - 1200px) / 2);
  }
  .hidden-dots .slick-dots {
    display: none;
  }
  .xl-28 {
    width: 28%;
  }
  .xl-72 {
    width: 72%;
  }
  .col-xl-c5 {
    width: 20%;
  }
}

@media (max-width: 1199px) {
  .form-control {
    height: 48px;
  }
}

@media (max-width: 767px) {
  .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
  .row > [class*="col-"]:not(.elementor-column) {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .nav-tabs {
    overflow-x: auto;
    white-space: nowrap;
  }
  .nav-tabs li {
    float: none;
    display: inline-block;
  }
  .ordering-display-mode-wrapper {
    margin-top: 5px;
  }
  .form-search.horizontal .list-fileds > div {
    margin-bottom: 0.9375rem;
  }
  .form-search.horizontal .list-fileds > div:last-child {
    margin-bottom: 0;
  }
  .form-search.horizontal .list-fileds .btn-submit {
    width: 100%;
    margin-top: 0.9375rem;
  }
}

@media (max-width: 479px) {
  .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > *,
  .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container, .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select, .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input {
    width: 100% !important;
    margin: 0 0 5px !important;
  }
  .apus-filter .subtitle {
    display: none;
  }
  div.cmb2-wrap [id*="map_location-map"] ~ input {
    margin: 0 0 5px !important;
    width: 100%;
  }
  .comment-list .comment-reply-link .text-reply::before {
    display: none;
  }
  .woocommerce-page table.cart td.actions .coupon {
    margin: 0;
  }
  .woocommerce-page table.cart td.actions .coupon .input-text {
    margin: 0 0 1rem;
    width: 100%;
  }
}

@media (max-width: 782px) {
  .admin-bar.header_transparent #apus-header {
    top: 46px;
  }
  .admin-bar .header-mobile {
    top: 46px;
  }
}

@media (min-width: 783px) {
  .admin-bar .header-mobile {
    top: 32px;
  }
}

@media (max-width: 600px) {
  .admin-bar .header-mobile.sticky-header {
    top: 0;
  }
}
