<?php
$Title_page = 'إتصل بنا';
  
include('header.php'); 
include('navbar.php');

echo '
<section class="p-0">
  <iframe class="home8-map contact-page" loading="lazy" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4426002.18112006!2d47.079486760711816!3d24.56838601170367!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x15e7b33fe7952a41%3A0x5960504bc21ab69b!2z2KfZhNiz2LnZiNiv2YrYqQ!5e0!3m2!1sar!2seg!4v1706403761468!5m2!1sar!2seg"></iframe>
</section>
<section>
  <div class="container">
    <div class="row d-flex align-items-end">
      <div class="col-lg-5 position-relative">
        <div class="home8-contact-form default-box-shadow1 bdrs12 bdr1 p30 mb30-md bgc-white">
          <h4 class="form-title mb25">'.$Title_page.'</h4>
          ';
          if (isset($_POST['name'])){
            echo '<div class="space"></div>';
            $var1  = $_POST['name']  ;
            $var2  = $_POST['email']  ;
            $var3  = $_POST['phone']  ;
            $var4  = $_POST['message']  ;
            if (empty($var1) || empty($var2) || empty($var3) || empty($var4) ){
            echo Show_Alert('danger' , 'يجب عليك ملئ كل الحقول.');
            }else{
                $stmt = $db->prepare("INSERT INTO contactus ( name , email , phone , message , ip , timestamp) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6 )");  
                $stmt->execute(array( 'user_1' => $var1  , 'user_2' => $var2 , 'user_3' => $var3 , 'user_4' => $var4 , 'user_5' => get_client_ip() , 'user_6' => time() )) ;
                echo Show_Alert('success' , 'تم إرسال رسالتك الى الإداره. سيتم الرد عليها خلال 24 ساعه.');
            }		
        }
          echo '
          <form method="post" class="form-style1">
            <div class="row">
              <div class="col-lg-12">
                <div class="mb20">
                  <label class="heading-color ff-heading fw600 mb10">الأسم بالكامل</label>
                  <input name="name" type="text" class="form-control" placeholder="الأسم بالكامل">
                </div>
              </div>
              <div class="col-lg-12">
                <div class="mb20">
                  <label class="heading-color ff-heading fw600 mb10">رقم الجوال</label>
                  <input type="text" name="phone" class="form-control" placeholder="رقم الجوال">
                </div>
              </div>
              <div class="col-md-12">
                <div class="mb20">
                  <label class="heading-color ff-heading fw600 mb10">البريد الإلكتروني</label>
                  <input type="email" name="email" class="form-control" placeholder=""البريد الإلكتروني"">
                </div>
              </div>
              <div class="col-md-12">
                <div class="mb10">
                  <label class="heading-color ff-heading fw600 mb10">الرسالة</label>
                  <textarea  name="message" cols="30" rows="4" placeholder="الرسالة"></textarea>
                </div>
              </div>
              <div class="col-md-12">
                <div class="d-grid">
                  <button class="ud-btn btn-thm" id="submit" type="submit">إرسال الرسالة <i class="fal fa-arrow-right-long"></i></button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div> 
    </div>
  </div>
</section>
';
include('footer.php');  