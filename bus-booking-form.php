<?php
$Title_page = 'إكمال حجز الباص';
include_once('webset.php');
include_once('header.php');
include_once('navbar.php');

// التحقق من وجود البيانات
if (!isset($_GET['data'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_data = json_decode(urldecode($_GET['data']), true);
if (!$booking_data) {
    header('Location: bus-booking.php');
    exit;
}

// جلب تفاصيل الرحلة
$trip_id = $booking_data['trip_id'];
$stmt = $db->prepare("SELECT * FROM bus_trips WHERE id = ? AND status = 1");
$stmt->execute([$trip_id]);
$trip = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$trip) {
    header('Location: bus-booking.php');
    exit;
}

echo '
<section class="booking-form-section" style="padding: 60px 0; background: #f8f9fa;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="booking-form-container" style="background: white; border-radius: 20px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">

                    <!-- عنوان الصفحة -->
                    <div class="text-center mb-5">
                        <h2 style="color: #333; font-weight: bold; margin-bottom: 10px;">إكمال حجز الباص</h2>
                        <p style="color: #666;">أكمل بياناتك لتأكيد الحجز</p>
                    </div>

                    <!-- تفاصيل الرحلة -->
                    <div class="trip-summary" style="background: #f8f9fa; border-radius: 15px; padding: 25px; margin-bottom: 30px;">
                        <h4 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                            <span style="margin-left: 10px;">🚌</span>
                            تفاصيل الرحلة
                        </h4>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <strong>المسار:</strong> '.$trip['from_city'].' ← '.$trip['to_city'].'
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <strong>التاريخ:</strong> '.date('Y-m-d', strtotime($booking_data['travel_date'])).'
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <strong>وقت المغادرة:</strong> '.date('h:i A', strtotime($trip['departure_time'])).'
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <strong>عدد المسافرين:</strong> '.$booking_data['passenger_count'].' مسافر
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <strong>المحطة:</strong> '.$trip['from_station'].'
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <strong>الإجمالي:</strong> <span style="color: #28a745; font-weight: bold; font-size: 18px;">'.$booking_data['total_price'].' ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج البيانات الشخصية -->
                    <form id="bookingForm" method="post" action="process-bus-booking.php">
                        <input type="hidden" name="trip_id" value="'.$trip_id.'">
                        <input type="hidden" name="travel_date" value="'.$booking_data['travel_date'].'">
                        <input type="hidden" name="seats_count" value="'.$booking_data['passenger_count'].'">
                        <input type="hidden" name="total_amount" value="'.$booking_data['total_price'].'">

                        <h4 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                            <span style="margin-left: 10px;">👤</span>
                            البيانات الشخصية
                        </h4>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">الاسم الأول *</label>
                                <input type="text" class="form-control" name="first_name" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">اسم العائلة *</label>
                                <input type="text" class="form-control" name="last_name" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">رقم الجوال *</label>
                                <input type="tel" class="form-control" name="phone" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="05xxxxxxxx">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" name="email" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">رقم الهوية *</label>
                                <input type="text" class="form-control" name="id_number" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" maxlength="10">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" style="font-weight: 600; color: #333;">الجنسية *</label>
                                <select class="form-select" name="nationality" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;">
                                    <option value="">اختر الجنسية</option>
                                    <option value="سعودي">سعودي</option>
                                    <option value="مقيم">مقيم</option>
                                    <option value="زائر">زائر</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label" style="font-weight: 600; color: #333;">ملاحظات إضافية</label>
                            <textarea class="form-control" name="notes" rows="3" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px;" placeholder="أي ملاحظات أو طلبات خاصة..."></textarea>
                        </div>

                        <!-- شروط الخدمة -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms" style="color: #666;">
                                    أوافق على <a href="terms-and-conditions.php" target="_blank" style="color: #ff9500;">الشروط والأحكام</a> و <a href="privacy-policy.php" target="_blank" style="color: #ff9500;">سياسة الخصوصية</a>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="bus-booking.php" class="btn btn-outline-secondary w-100" style="border-radius: 10px; padding: 12px; font-weight: bold;">
                                    العودة للبحث
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button type="submit" class="btn btn-primary w-100" style="background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%); border: none; border-radius: 10px; padding: 12px; font-weight: bold;">
                                    تأكيد الحجز
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<style>
.detail-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.detail-item:last-child {
    border-bottom: none;
}

.form-control:focus, .form-select:focus {
    border-color: #ff9500;
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 149, 0, 0.3);
}

@media (max-width: 768px) {
    .booking-form-container {
        margin: 20px;
        padding: 20px !important;
    }
}
</style>

<script>
document.getElementById("bookingForm").addEventListener("submit", function(e) {
    const submitBtn = this.querySelector("button[type=submit]");
    submitBtn.innerHTML = "جاري التأكيد...";
    submitBtn.disabled = true;
});
</script>
';

include('footer.php');
?>

