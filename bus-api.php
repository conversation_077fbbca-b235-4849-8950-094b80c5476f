<?php
include('webset.php');

// تعيين header للاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// تسجيل الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 0);

// دالة لتسجيل الأخطاء
function logError($message, $data = null) {
    $log_entry = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $log_entry .= " - Data: " . json_encode($data);
    }
    error_log($log_entry . "\n", 3, "bus_errors.log");
}

try {
    // فحص طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب يجب أن تكون POST');
    }
    
    // قراءة البيانات
    $raw_input = file_get_contents('php://input');
    if (empty($raw_input)) {
        throw new Exception('لا توجد بيانات مرسلة');
    }
    
    // تحليل JSON
    $input = json_decode($raw_input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('خطأ في تحليل JSON: ' . json_last_error_msg());
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'get_cities':
            handleGetCities($input);
            break;

        case 'get_go':
            handleGetgo($input);
            break;

        case 'get_stations':
            handleGetStations($input);
            break;

        case 'search_trips':
            handleSearchTrips($input);
            break;

        // دوال جديدة للنظام المطور
        case 'get_all_cities':
            handleGetAllCities($input);
            break;

        case 'get_destination_cities':
            handleGetDestinationCities($input);
            break;

        case 'get_departure_stations':
            handleGetDepartureStations($input);
            break;

        case 'get_arrival_stations':
            handleGetArrivalStations($input);
            break;

        case 'search_new_trips':
            handleSearchNewTrips($input);
            break;

        default:
            throw new Exception('عملية غير مدعومة: ' . $action);
    }
    
} catch (Exception $e) {
    logError("API Error: " . $e->getMessage(), $input ?? null);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}


function handleGetgo($input) {
    global $db;
    
    $trip_type = $input['trip_type'] ?? 'to_mecca';
    
    try {
        if ($trip_type === 'to_mecca') {
            // جلب المدن التي لها رحلات ذهاب إلى مكة
            $sql = "SELECT DISTINCT * FROM bus_trips WHERE trip_type = 'to_mecca' ORDER BY from_city";
            $stmt = $db->prepare($sql);
        } else {
            // جلب المدن التي لها رحلات عودة من مكة
            $sql = "SELECT DISTINCT * FROM bus_trips WHERE trip_type = 'from_mecca' ORDER BY to_city";
            $stmt = $db->prepare($sql);
        }
        
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $cities = [];
        foreach ($results as $row) {
            $cities[] = $trip_type === 'to_mecca' ? $row['to_station'] : $row['from_station'];
        }
        
        echo json_encode([
            'success' => true,
            'stations' => $cities
        ]);

    } catch (PDOException $e) {
        error_log("Database error in handleGetCities: " . $e->getMessage());
        throw new Exception('خطأ في جلب المدن: ' . $e->getMessage());
    } catch (Exception $e) {
        error_log("General error in handleGetCities: " . $e->getMessage());
        throw new Exception('خطأ في جلب المدن: ' . $e->getMessage());
    }
}

// دالة جلب المدن
function handleGetCities($input) {
    global $db;
    
    $trip_type = $input['trip_type'] ?? 'to_mecca';
    
    try {
        if ($trip_type === 'to_mecca') {
            // جلب المدن التي لها رحلات ذهاب إلى مكة
            $sql = "SELECT DISTINCT from_city FROM bus_trips WHERE trip_type = 'to_mecca' ORDER BY from_city";
            $stmt = $db->prepare($sql);
        } else {
            // جلب المدن التي لها رحلات عودة من مكة
            $sql = "SELECT DISTINCT to_city FROM bus_trips WHERE trip_type = 'from_mecca' ORDER BY to_city";
            $stmt = $db->prepare($sql);
        }
        
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $cities = [];
        foreach ($results as $row) {
            $cities[] = $trip_type === 'to_mecca' ? $row['from_city'] : $row['to_city'];
        }
        
        echo json_encode([
            'success' => true,
            'cities' => $cities
        ]);

    } catch (PDOException $e) {
        error_log("Database error in handleGetCities: " . $e->getMessage());
        throw new Exception('خطأ في جلب المدن: ' . $e->getMessage());
    } catch (Exception $e) {
        error_log("General error in handleGetCities: " . $e->getMessage());
        throw new Exception('خطأ في جلب المدن: ' . $e->getMessage());
    }
}

// دالة جلب المحطات
function handleGetStations($input) {
    global $db;
    
    $city = $input['city'] ?? '';
    $trip_type = $input['trip_type'] ?? 'to_mecca';
    
    if (empty($city)) {
        throw new Exception('المدينة مطلوبة');
    }
    
    try {
        if ($trip_type === 'to_mecca') {
            $stmt = $db->prepare("SELECT DISTINCT from_station FROM bus_trips WHERE trip_type = 'to_mecca' AND from_city = ? ORDER BY from_station");
        } else {
            $stmt = $db->prepare("SELECT DISTINCT to_station FROM bus_trips WHERE trip_type = 'from_mecca' AND to_city = ? ORDER BY to_station");
        }
        
        $stmt->execute([$city]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $stations = [];
        foreach ($results as $row) {
            $stations[] = $trip_type === 'to_mecca' ? $row['from_station'] : $row['to_station'];
        }
        
        echo json_encode([
            'success' => true,
            'stations' => $stations
        ]);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب المحطات: ' . $e->getMessage());
    }
}

// دالة البحث عن الرحلات
function handleSearchTrips($input) {
    global $db;
    
    $trip_type = $input['trip_type'] ?? '';
    $from_city = $input['from_city'] ?? '';
    $from_station = $input['from_station'] ?? '';
    $travel_date = $input['travel_date'] ?? '';
    $passenger_count = intval($input['passenger_count'] ?? 1);
    
    // التحقق من البيانات المطلوبة
    if (empty($trip_type) || empty($from_city) || empty($from_station) || empty($travel_date)) {
        throw new Exception('جميع البيانات مطلوبة');
    }
    
    try {
        // تحديد يوم الأسبوع للتاريخ المحدد
        $day_of_week = date('w', strtotime($travel_date)); // 0 = الأحد, 1 = الاثنين, إلخ
        
        if ($trip_type === 'to_mecca') {
            $sql = "SELECT * FROM bus_trips
                    WHERE trip_type = 'to_mecca'
                    AND from_city = ?
                    AND from_station = ?
                    AND (available_days = '0' OR available_days LIKE ?)
                    ORDER BY departure_sort_time";
        } else {
            $sql = "SELECT * FROM bus_trips
                    WHERE trip_type = 'from_mecca'
                    AND to_city = ?
                    AND to_station = ?
                    AND (available_days = '0' OR available_days LIKE ?)
                    ORDER BY departure_sort_time";
        }
        
        $stmt = $db->prepare($sql);
        $day_pattern = '%"' . $day_of_week . '"%';
        $stmt->execute([$from_city, $from_station, $day_pattern]);
        $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // فلترة الرحلات بناءً على الأيام المتاحة
        $available_trips = [];
        foreach ($trips as $trip) {
            $available_days = $trip['available_days'];
            
            // إذا كانت القيمة 0، فالرحلة متاحة يومياً
            if ($available_days === '0') {
                $available_trips[] = $trip;
                continue;
            }
            
            // فحص إذا كان اليوم متاح
            $days_array = json_decode($available_days, true);
            if (is_array($days_array) && in_array(strval($day_of_week), $days_array)) {
                $available_trips[] = $trip;
            }
        }
        
        // إضافة معلومات المقاعد المتاحة
        foreach ($available_trips as &$trip) {
            // حساب المقاعد المحجوزة لهذا التاريخ
            $stmt = $db->prepare("SELECT COALESCE(SUM(seats_count), 0) as booked_seats
                                 FROM bus_bookings
                                 WHERE trip_id = ? AND travel_date = ? AND booking_status != 'cancelled'");
            $stmt->execute([$trip['id'], $travel_date]);
            $booked_seats = $stmt->fetchColumn();
            
            $trip['available_seats'] = max(0, $trip['total_seats'] - $booked_seats);
            
            // التحقق من توفر المقاعد المطلوبة
            if ($trip['available_seats'] < $passenger_count) {
                $trip['available_seats'] = 0; // لا توجد مقاعد كافية
            }
        }
        
        // إزالة الرحلات التي لا تحتوي على مقاعد كافية
        $available_trips = array_filter($available_trips, function($trip) {
            return $trip['available_seats'] > 0;
        });
        
        echo json_encode([
            'success' => true,
            'trips' => array_values($available_trips),
            'count' => count($available_trips)
        ]);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في البحث عن الرحلات: ' . $e->getMessage());
    }
}

// ===== دوال جديدة للنظام المطور =====

// دالة جلب جميع المدن المتاحة للسفر
function handleGetAllCities($input) {
    global $db;

    try {
        $sql = "SELECT DISTINCT from_city FROM bus_trips WHERE status = 1 ORDER BY from_city";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $cities = [];
        foreach ($results as $row) {
            $cities[] = $row['from_city'];
        }

        echo json_encode([
            'success' => true,
            'cities' => $cities
        ]);

    } catch (Exception $e) {
        throw new Exception('خطأ في جلب المدن: ' . $e->getMessage());
    }
}

// دالة جلب مدن الوصول بناءً على مدينة السفر
function handleGetDestinationCities($input) {
    global $db;

    $from_city = $input['from_city'] ?? '';

    if (empty($from_city)) {
        throw new Exception('مدينة السفر مطلوبة');
    }

    try {
        $sql = "SELECT DISTINCT to_city FROM bus_trips WHERE from_city = ? AND status = 1 ORDER BY to_city";
        $stmt = $db->prepare($sql);
        $stmt->execute([$from_city]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $cities = [];
        foreach ($results as $row) {
            $cities[] = $row['to_city'];
        }

        echo json_encode([
            'success' => true,
            'cities' => $cities
        ]);

    } catch (Exception $e) {
        throw new Exception('خطأ في جلب مدن الوصول: ' . $e->getMessage());
    }
}

// دالة جلب محطات السفر بناءً على مدينة السفر ومدينة الوصول
function handleGetDepartureStations($input) {
    global $db;

    $from_city = $input['from_city'] ?? '';
    $to_city = $input['to_city'] ?? '';

    if (empty($from_city) || empty($to_city)) {
        throw new Exception('مدينة السفر ومدينة الوصول مطلوبتان');
    }

    try {
        $sql = "SELECT DISTINCT from_station FROM bus_trips WHERE from_city = ? AND to_city = ? AND status = 1 ORDER BY from_station";
        $stmt = $db->prepare($sql);
        $stmt->execute([$from_city, $to_city]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stations = [];
        foreach ($results as $row) {
            $stations[] = $row['from_station'];
        }

        echo json_encode([
            'success' => true,
            'stations' => $stations
        ]);

    } catch (Exception $e) {
        throw new Exception('خطأ في جلب محطات السفر: ' . $e->getMessage());
    }
}

// دالة جلب محطات الوصول بناءً على مدينة السفر ومدينة الوصول ومحطة السفر
function handleGetArrivalStations($input) {
    global $db;

    $from_city = $input['from_city'] ?? '';
    $to_city = $input['to_city'] ?? '';
    $from_station = $input['from_station'] ?? '';

    if (empty($from_city) || empty($to_city) || empty($from_station)) {
        throw new Exception('جميع البيانات مطلوبة');
    }

    try {
        $sql = "SELECT DISTINCT to_station FROM bus_trips WHERE from_city = ? AND to_city = ? AND from_station = ? AND status = 1 ORDER BY to_station";
        $stmt = $db->prepare($sql);
        $stmt->execute([$from_city, $to_city, $from_station]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stations = [];
        foreach ($results as $row) {
            $stations[] = $row['to_station'];
        }

        echo json_encode([
            'success' => true,
            'stations' => $stations
        ]);

    } catch (Exception $e) {
        throw new Exception('خطأ في جلب محطات الوصول: ' . $e->getMessage());
    }
}

// دالة البحث الجديدة عن الرحلات
function handleSearchNewTrips($input) {
    global $db;

    $from_city = $input['from_city'] ?? '';
    $to_city = $input['to_city'] ?? '';
    $from_station = $input['from_station'] ?? '';
    $to_station = $input['to_station'] ?? '';
    $travel_date = $input['travel_date'] ?? '';
    $passenger_count = intval($input['passenger_count'] ?? 1);

    // التحقق من البيانات المطلوبة
    if (empty($from_city) || empty($to_city) || empty($from_station) || empty($to_station) || empty($travel_date)) {
        throw new Exception('جميع البيانات مطلوبة');
    }

    try {
        // تحديد يوم الأسبوع للتاريخ المحدد
        $day_of_week = date('w', strtotime($travel_date)); // 0 = الأحد, 1 = الاثنين, إلخ

        $sql = "SELECT * FROM bus_trips
                WHERE from_city = ?
                AND to_city = ?
                AND from_station = ?
                AND to_station = ?
                AND status = 1
                AND (available_days = '0' OR available_days LIKE ?)
                ORDER BY departure_sort_time";

        $stmt = $db->prepare($sql);
        $day_pattern = '%"' . $day_of_week . '"%';
        $stmt->execute([$from_city, $to_city, $from_station, $to_station, $day_pattern]);
        $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // فلترة الرحلات بناءً على الأيام المتاحة
        $available_trips = [];
        foreach ($trips as $trip) {
            $available_days = $trip['available_days'];

            // إذا كانت القيمة 0، فالرحلة متاحة يومياً
            if ($available_days === '0') {
                $available_trips[] = $trip;
                continue;
            }

            // فحص إذا كان اليوم متاح
            $days_array = json_decode($available_days, true);
            if (is_array($days_array) && in_array(strval($day_of_week), $days_array)) {
                $available_trips[] = $trip;
            }
        }

        // إضافة معلومات المقاعد المتاحة
        foreach ($available_trips as &$trip) {
            // حساب المقاعد المحجوزة لهذا التاريخ
            $stmt = $db->prepare("SELECT COALESCE(SUM(seats_count), 0) as booked_seats
                                 FROM bus_bookings
                                 WHERE trip_id = ? AND travel_date = ? AND booking_status != 'cancelled'");
            $stmt->execute([$trip['id'], $travel_date]);
            $booked_seats = $stmt->fetchColumn();

            $trip['available_seats'] = max(0, $trip['total_seats'] - $booked_seats);

            // التحقق من توفر المقاعد المطلوبة
            if ($trip['available_seats'] < $passenger_count) {
                $trip['available_seats'] = 0; // لا توجد مقاعد كافية
            }
        }

        // إزالة الرحلات التي لا تحتوي على مقاعد كافية
        $available_trips = array_filter($available_trips, function($trip) {
            return $trip['available_seats'] > 0;
        });

        echo json_encode([
            'success' => true,
            'trips' => array_values($available_trips),
            'count' => count($available_trips)
        ]);

    } catch (Exception $e) {
        throw new Exception('خطأ في البحث عن الرحلات: ' . $e->getMessage());
    }
}
?>
