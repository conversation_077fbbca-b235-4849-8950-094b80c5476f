<?php
include('webset.php');

echo "<h2>إعداد نظام الصفحات المخصصة للرحلات</h2>";

try {
    // التحقق من وجود جدول bus_cities
    $stmt = $db->query("SHOW TABLES LIKE 'bus_cities'");
    $table_exists = $stmt->rowCount() > 0;
    
    if (!$table_exists) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>إنشاء جدول bus_cities...</h4>";
        
        // إنشاء الجدول
        $create_table_sql = "
        CREATE TABLE bus_cities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL COMMENT 'اسم الصفحة',
            link VARCHAR(100) NOT NULL UNIQUE COMMENT 'الرابط المخصص للصفحة',
            title VARCHAR(300) NOT NULL COMMENT 'عنوان الصفحة الرئيسي',
            title_s VARCHAR(200) NOT NULL COMMENT 'العنوان المختصر',
            descr TEXT COMMENT 'وصف الصفحة',
            tags VARCHAR(500) COMMENT 'الكلمات المفتاحية',
            photo VARCHAR(200) COMMENT 'صورة الصفحة',
            parent INT DEFAULT 0 COMMENT 'الصفحة الأب',
            status TINYINT DEFAULT 1 COMMENT 'حالة الصفحة (1=نشط، 0=غير نشط)',
            
            -- إعدادات الرحلة المخصصة
            trip_type ENUM('to_mecca', 'from_mecca') NOT NULL COMMENT 'نوع الرحلة',
            from_city VARCHAR(100) COMMENT 'المدينة المحددة مسبقاً (من)',
            to_city VARCHAR(100) COMMENT 'المدينة المحددة مسبقاً (إلى)',
            from_station VARCHAR(200) COMMENT 'المحطة المحددة مسبقاً (من)',
            to_station VARCHAR(200) COMMENT 'المحطة المحددة مسبقاً (إلى)',
            
            -- إعدادات SEO
            meta_description TEXT COMMENT 'وصف الميتا',
            meta_keywords VARCHAR(500) COMMENT 'كلمات الميتا المفتاحية',
            
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $db->exec($create_table_sql);
        echo "<p style='color: green;'>✅ تم إنشاء جدول bus_cities بنجاح</p>";
        echo "</div>";
        
        // إدراج بيانات تجريبية
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>إدراج بيانات تجريبية...</h4>";
        
        $sample_data = [
            // صفحات الذهاب إلى مكة
            [
                'name' => 'رحلات من الرياض إلى مكة',
                'link' => 'riyadh-to-mecca',
                'title' => 'رحلات باصات من الرياض إلى مكة المكرمة',
                'title_s' => 'رحلات الرياض مكة',
                'descr' => 'احجز رحلتك من الرياض إلى مكة المكرمة بأفضل الأسعار وأعلى مستوى من الراحة والأمان',
                'tags' => 'رحلات الرياض مكة، باصات الرياض مكة، حجز باص الرياض مكة',
                'trip_type' => 'to_mecca',
                'from_city' => 'الرياض',
                'to_city' => 'مكة المكرمة',
                'meta_description' => 'احجز رحلات الباصات من الرياض إلى مكة المكرمة بأسعار مناسبة ومواعيد متعددة يومياً',
                'meta_keywords' => 'رحلات الرياض مكة، باصات الرياض، حجز باص مكة'
            ],
            [
                'name' => 'رحلات من جدة إلى مكة',
                'link' => 'jeddah-to-mecca',
                'title' => 'رحلات باصات من جدة إلى مكة المكرمة',
                'title_s' => 'رحلات جدة مكة',
                'descr' => 'رحلات سريعة ومريحة من جدة إلى مكة المكرمة بأسعار اقتصادية',
                'tags' => 'رحلات جدة مكة، باصات جدة مكة، حجز باص جدة مكة',
                'trip_type' => 'to_mecca',
                'from_city' => 'جدة',
                'to_city' => 'مكة المكرمة',
                'meta_description' => 'رحلات باصات من جدة إلى مكة المكرمة - مواعيد متعددة وأسعار مناسبة',
                'meta_keywords' => 'رحلات جدة مكة، باصات جدة، حجز باص مكة'
            ],
            [
                'name' => 'رحلات من الدمام إلى مكة',
                'link' => 'dammam-to-mecca',
                'title' => 'رحلات باصات من الدمام إلى مكة المكرمة',
                'title_s' => 'رحلات الدمام مكة',
                'descr' => 'رحلات مريحة من الدمام إلى مكة المكرمة مع خدمة VIP',
                'tags' => 'رحلات الدمام مكة، باصات الدمام مكة، حجز باص الدمام مكة',
                'trip_type' => 'to_mecca',
                'from_city' => 'الدمام',
                'to_city' => 'مكة المكرمة',
                'meta_description' => 'احجز رحلتك من الدمام إلى مكة المكرمة - رحلات مريحة وآمنة',
                'meta_keywords' => 'رحلات الدمام مكة، باصات الدمام، حجز باص مكة'
            ],
            [
                'name' => 'رحلات من المدينة إلى مكة',
                'link' => 'medina-to-mecca',
                'title' => 'رحلات باصات من المدينة المنورة إلى مكة المكرمة',
                'title_s' => 'رحلات المدينة مكة',
                'descr' => 'رحلات مباشرة من المدينة المنورة إلى مكة المكرمة',
                'tags' => 'رحلات المدينة مكة، باصات المدينة مكة، حجز باص المدينة مكة',
                'trip_type' => 'to_mecca',
                'from_city' => 'المدينة المنورة',
                'to_city' => 'مكة المكرمة',
                'meta_description' => 'رحلات من المدينة المنورة إلى مكة المكرمة - خدمة يومية',
                'meta_keywords' => 'رحلات المدينة مكة، باصات المدينة، حجز باص مكة'
            ],
            
            // صفحات العودة من مكة
            [
                'name' => 'رحلات من مكة إلى الرياض',
                'link' => 'mecca-to-riyadh',
                'title' => 'رحلات باصات من مكة المكرمة إلى الرياض',
                'title_s' => 'رحلات مكة الرياض',
                'descr' => 'رحلات العودة من مكة المكرمة إلى الرياض بمواعيد مرنة',
                'tags' => 'رحلات مكة الرياض، باصات مكة الرياض، حجز باص مكة الرياض',
                'trip_type' => 'from_mecca',
                'from_city' => 'مكة المكرمة',
                'to_city' => 'الرياض',
                'meta_description' => 'احجز رحلة العودة من مكة المكرمة إلى الرياض - مواعيد متعددة',
                'meta_keywords' => 'رحلات مكة الرياض، باصات مكة، حجز باص الرياض'
            ],
            [
                'name' => 'رحلات من مكة إلى جدة',
                'link' => 'mecca-to-jeddah',
                'title' => 'رحلات باصات من مكة المكرمة إلى جدة',
                'title_s' => 'رحلات مكة جدة',
                'descr' => 'رحلات سريعة من مكة المكرمة إلى جدة',
                'tags' => 'رحلات مكة جدة، باصات مكة جدة، حجز باص مكة جدة',
                'trip_type' => 'from_mecca',
                'from_city' => 'مكة المكرمة',
                'to_city' => 'جدة',
                'meta_description' => 'رحلات من مكة المكرمة إلى جدة - رحلات سريعة ومريحة',
                'meta_keywords' => 'رحلات مكة جدة، باصات مكة، حجز باص جدة'
            ],
            [
                'name' => 'رحلات من مكة إلى الدمام',
                'link' => 'mecca-to-dammam',
                'title' => 'رحلات باصات من مكة المكرمة إلى الدمام',
                'title_s' => 'رحلات مكة الدمام',
                'descr' => 'رحلات العودة من مكة المكرمة إلى الدمام',
                'tags' => 'رحلات مكة الدمام، باصات مكة الدمام، حجز باص مكة الدمام',
                'trip_type' => 'from_mecca',
                'from_city' => 'مكة المكرمة',
                'to_city' => 'الدمام',
                'meta_description' => 'احجز رحلة العودة من مكة المكرمة إلى الدمام',
                'meta_keywords' => 'رحلات مكة الدمام، باصات مكة، حجز باص الدمام'
            ],
            [
                'name' => 'رحلات من مكة إلى المدينة',
                'link' => 'mecca-to-medina',
                'title' => 'رحلات باصات من مكة المكرمة إلى المدينة المنورة',
                'title_s' => 'رحلات مكة المدينة',
                'descr' => 'رحلات من مكة المكرمة إلى المدينة المنورة',
                'tags' => 'رحلات مكة المدينة، باصات مكة المدينة، حجز باص مكة المدينة',
                'trip_type' => 'from_mecca',
                'from_city' => 'مكة المكرمة',
                'to_city' => 'المدينة المنورة',
                'meta_description' => 'رحلات من مكة المكرمة إلى المدينة المنورة - خدمة يومية',
                'meta_keywords' => 'رحلات مكة المدينة، باصات مكة، حجز باص المدينة'
            ]
        ];
        
        $stmt = $db->prepare("INSERT INTO bus_cities (name, link, title, title_s, descr, tags, trip_type, from_city, to_city, meta_description, meta_keywords, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)");
        
        $inserted_count = 0;
        foreach ($sample_data as $data) {
            try {
                $stmt->execute([
                    $data['name'], $data['link'], $data['title'], $data['title_s'],
                    $data['descr'], $data['tags'], $data['trip_type'], $data['from_city'],
                    $data['to_city'], $data['meta_description'], $data['meta_keywords']
                ]);
                $inserted_count++;
            } catch (Exception $e) {
                // تجاهل الأخطاء المتعلقة بالتكرار
            }
        }
        
        echo "<p style='color: green;'>✅ تم إدراج $inserted_count صفحة مخصصة</p>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✅ جدول bus_cities موجود بالفعل</h4>";
        
        // عرض إحصائيات الجدول
        $stmt = $db->query("SELECT COUNT(*) as total FROM bus_cities");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        $stmt = $db->query("SELECT COUNT(*) as active FROM bus_cities WHERE status = 1");
        $active = $stmt->fetch(PDO::FETCH_ASSOC)['active'];
        
        $stmt = $db->query("SELECT COUNT(*) as to_mecca FROM bus_cities WHERE trip_type = 'to_mecca'");
        $to_mecca = $stmt->fetch(PDO::FETCH_ASSOC)['to_mecca'];
        
        $stmt = $db->query("SELECT COUNT(*) as from_mecca FROM bus_cities WHERE trip_type = 'from_mecca'");
        $from_mecca = $stmt->fetch(PDO::FETCH_ASSOC)['from_mecca'];
        
        echo "<p>إجمالي الصفحات: <strong>$total</strong></p>";
        echo "<p>الصفحات النشطة: <strong>$active</strong></p>";
        echo "<p>صفحات الذهاب إلى مكة: <strong>$to_mecca</strong></p>";
        echo "<p>صفحات العودة من مكة: <strong>$from_mecca</strong></p>";
        echo "</div>";
    }
    
    // عرض الصفحات الموجودة
    $stmt = $db->query("SELECT id, name, link, title_s, trip_type, from_city, to_city, status FROM bus_cities ORDER BY trip_type, id");
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($pages) > 0) {
        echo "<h3>الصفحات المخصصة المتاحة:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>اسم الصفحة</th>";
        echo "<th style='padding: 10px;'>الرابط</th>";
        echo "<th style='padding: 10px;'>نوع الرحلة</th>";
        echo "<th style='padding: 10px;'>المسار</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>معاينة</th>";
        echo "</tr>";
        
        foreach ($pages as $page) {
            $trip_type_text = $page['trip_type'] == 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة';
            $status_text = $page['status'] == 1 ? 'نشط' : 'غير نشط';
            $status_color = $page['status'] == 1 ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td style='padding: 8px; text-align: center;'>" . $page['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($page['name']) . "</td>";
            echo "<td style='padding: 8px;'><code>" . htmlspecialchars($page['link']) . "</code></td>";
            echo "<td style='padding: 8px;'>" . $trip_type_text . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($page['from_city']) . " → " . htmlspecialchars($page['to_city']) . "</td>";
            echo "<td style='padding: 8px; color: $status_color; font-weight: bold;'>" . $status_text . "</td>";
            echo "<td style='padding: 8px;'><a href='bus-route.php?route=" . $page['link'] . "' target='_blank' style='color: #007bff;'>معاينة</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>📋 الخطوات التالية:</h4>";
    echo "<ol>";
    echo "<li>✅ تم إعداد جدول الصفحات المخصصة</li>";
    echo "<li>✅ تم إدراج البيانات التجريبية</li>";
    echo "<li>🔗 يمكنك الآن استخدام الروابط المخصصة مثل: <code>bus-route.php?route=mecca-to-riyadh</code></li>";
    echo "<li>⚙️ استخدم <a href='custom-routes-manager.php' style='color: #007bff;'>صفحة الإدارة</a> لإضافة وتعديل الصفحات</li>";
    echo "<li>🎨 كل صفحة لها تصميم مخصص وفلاتر بحث متقدمة</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='bus-route.php?route=mecca-to-riyadh' style='display: inline-block; padding: 10px 20px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار: مكة → الرياض</a>";
    echo "<a href='bus-route.php?route=riyadh-to-mecca' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار: الرياض → مكة</a>";
    echo "<a href='custom-routes-manager.php' style='display: inline-block; padding: 10px 20px; background: #ff9500; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة الصفحات</a>";
    echo "<a href='bus-booking.php' style='display: inline-block; padding: 10px 20px; background: #6f42c1; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
</style>
