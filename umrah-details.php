<?php

$hotel = json_decode($UmrahData[0]['hotelid']); 
$k = [] ; $d = []; $k_photos = []; $d_photos = [];
if(isset($hotel->k)){
    $k = getAllFrom('*' , 'hotels' , 'WHERE id= "'.$hotel->k.'" ', '');
    if(count($k) <= 0){
        http_response_code(404);
		header("Location:".$Site_URL."/error404"); exit();
    }
    $k_photos = json_decode($k[0]['photos']);
    for ($i=count($k_photos); $i <= 5 ; $i++) { 
        array_push($k_photos  , $default_image);
    } 

}



if(isset($hotel->d) && $hotel->d != 0){
    $d = getAllFrom('*' , 'hotels' , 'WHERE id= "'.$hotel->d.'" ', '');
    if(count($d) <= 0){
        http_response_code(404);
		header("Location:".$Site_URL."/error404"); exit();
    }

    $d_photos = json_decode($d[0]['photos']);
    for ($i=count($d_photos); $i <= 5 ; $i++) { 
        array_push($d_photos  , $default_image);
    } 
}

$mvist = $hotel->d == 0 ? 'd-none' : '';

$Title_page = $UmrahData[0]['title'] != '' ? $UmrahData[0]['title'] : 'حملة عمرة '.$k[0]['name'].' من '.$UmrahData[0]['from_to'];
$Page_Description = 'حملة عمرة '.$k[0]['name'].' من '.$UmrahData[0]['from_to'].' بتاريخ '.date("Y-m-d" , $UmrahData[0]['dateCreated']) . ' لمدة '.$UmrahData[0]['duration'].' أيام';


$stmt = $db->prepare("UPDATE offers SET views  = :var1 WHERE  id = :var0 ");  
$stmt->execute(array(
    'var1'  => $UmrahData[0]['views'] +1 ,
    'var0'  => $UmrahData[0]['id']
));

$vip = $UmrahData[0]['vip'] ? 'باص VIP' : 'باص أساسي';


$busType = $UmrahData[0]['vip'] ? 'باص 3 صفوف vip' : 'باص أساسي 4 صفوف'; // تحديد نوع الباص بناءً على القيمة

$reviews = getAllFrom('*', 'reviews', 'WHERE hotel_id = "'.$hotel->k.'"', 'ORDER BY created_at DESC');




 
include('header.php');
include('navbar.php');
 
 
echo '
<section class="pt20 pb90 bgc-f7">
    <div class="container">
    <div class="row wow fadeInUp" data-wow-delay="100ms">
        <div class="col-lg-12">
       
        </div> 
    </div>
    <div class="row mb30 mt5 wow fadeInUp" data-wow-delay="300ms">
       
        <div class="col-sm-12">
        </div>
        <div class="col-sm-6">
            <div class="single-property-content mb0-md">
                <h2 class="sp-lg-title">'.$Title_page.'</h2>
                <div class="pd-meta mb15 d-md-flex align-items-center">
                    <a class="text fz15 mb-0 bdrr1 pl10 bdrrn-sm" href="javascript:void(0)"><span class="icon fal fa-map-marker-alt" style="COLOR: #ff9b00;padding: 3px;"></span>من '.$UmrahData[0]['from_to'].'</a>
                    <a class="text fz15 mb-0 bdrr1 pr10 pl10 bdrrn-sm" href="javascript:void(0)"><span class="icon fal fa-calendar-alt" style="COLOR: #ff9b00;padding: 3px;"></span>'.$UmrahData[0]['duration'].' أيام</a>
                    <a class="text fz15 mb-0 pr10 pl10 bdrrn-sm" href="javascript:void(0)"><span class="fa fal fa-bus-alt" style="COLOR: #ff9b00;padding: 3px;"></span>'.$vip.'</a>
                </div>
            </div>



            <div class="accordion-style3 bdrb1 pb25">
                <div class="accordion" id="accordionExample3">
                    <!-- مربع معلومات الرحلة -->
                    <div class="accordion-item border-none bgc-transparent">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                معلومات الرحلة
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample3">
                            <div class="accordion-body">
                                <div class="row">
                                    ';
                                    $offerInfo = json_decode($UmrahData[0]['info']);
                                    if (is_array($offerInfo) && count($offerInfo) > 0) {
                                        foreach ($offerInfo as $info) {
                                            echo '
                                            <div class="col-sm-6 col-lg-4">
                                                <div class="overview-element mb10 d-flex align-items-center">
                                                    <span class="icon ' . htmlspecialchars($info->ico) . '"></span>
                                                    <div class="mr15">
                                                        <h6 class="mb-0">' . htmlspecialchars($info->key) . '</h6>
                                                        <p class="text mb-0 fz15">' . htmlspecialchars($info->val) . '</p>
                                                    </div>
                                                </div>
                                            </div>';
                                        }
                                    } else {
                                        echo '<p>لا توجد معلومات متاحة عن الرحلة حاليًا.</p>';
                                    }
                                    echo '
                                </div>
                            </div>
                        </div>
                    </div>
                    
            
                    <div class="accordion-item border-none bgc-transparent">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                معلومات الفندق
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionExample3">
                            <div class="accordion-body">
                                <div class="row">
                                    ';
                                    $k_Info = isset($k[0]['info']) && $k[0]['info'] != '' ? json_decode($k[0]['info']) : [];
                                    if (is_array($k_Info) && count($k_Info) > 0) {
                                        foreach ($k_Info as $info) {
                                            echo '
                                            <div class="col-sm-6 col-lg-4">
                                                <div class="overview-element mb25 d-flex align-items-center">
                                                    <span class="icon ' . htmlspecialchars($info->ico) . '"></span>
                                                    <div class="mr15">
                                                        <h6 class="mb-0">' . htmlspecialchars($info->key) . '</h6>
                                                        <p class="text mb-0 fz15">' . htmlspecialchars($info->val) . '</p>
                                                    </div>
                                                </div>
                                            </div>';
                                        }
                                    } else {
                                        echo '<p>لا توجد معلومات متاحة عن الفندق حاليًا.</p>';
                                    }
                                    echo '
                                </div>
                            </div>
                        </div>
                    </div>
            
                    <div class="accordion-item border-none bgc-transparent">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                ضمان الخدمة
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionExample3">
                            <div class="accordion-body">
                                <div class="pd-list mt-2">
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>تغيير الفندق فوراً عند وجود أي إزعاج</p>
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>خدمة عملاء متاحة 24/7 لضمان راحتك</p>
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>مرونة كاملة لتعديل خططك أثناء الرحلة</p>
                                    <p class="text mb-0"><i class="fas fa-circle fz6 align-middle pe-2"></i>إلغاء مجاني دون رسوم</p>
                                </div>
                            </div>
                        </div>
                    </div>
            
                    <div class="accordion-item border-none bgc-transparent">
                        <h2 class="accordion-header" id="headingFour">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                السعر يشمل / لا يشمل
                            </button>
                        </h2>
                        <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#accordionExample3">
                            <div class="accordion-body">
                                <!-- السعر يشمل -->
                                <h5>السعر يشمل:</h5>
                                <div class="pd-list mt-2">
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>الانتقالات من والى مكة</p>
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>الاقامة فى فندق مكة</p>
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>الانتقالات من الفندق للحرم</p>
                                </div>
            
                                <!-- السعر لا يشمل -->
                                <h5>السعر لا يشمل:</h5>
                                <div class="pd-list mt-2">
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>لا تشمل الوجبات</p>
                                    <p class="text mb-2"><i class="fas fa-circle fz6 align-middle pe-2"></i>المزارات في مكة</p>
                                </div>
                            </div>
                        </div>
                    </div>
<div class="accordion-item border-none bgc-transparent">
    <h2 class="accordion-header" id="headingFive">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive"style="
    background: #f37a12;    color: #fff;
    border-radius: 10px;">
            موقع الباصات
        </button>














    </h2>
    <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#accordionExample3">
        <div class="accordion-body">
            <div class="pd-list mt-2">
                <p class="text mb-0">
                    <i class="fas fa-map-marker-alt fz6 align-middle pe-2"></i>
                    '.$UmrahData[0]['location'].'
                </p>
            </div>
        </div>
    </div>
</div>
                </div>
            </div>
            
            




        </div>
        <div class="col-sm-6">
            <div class="row">
                <div class="col-4 ps-sm-0">
                    <div class="sp-img-content">
                        <a class="popup-img preview-img-2 sp-img mb10" href="'.$Site_URL.'/'.$k_photos[0].'">
                        <img class="w-100" src="'.$Site_URL.'/'.$k_photos[0].'" alt="'.$k[0]['name'].'_1">
                        </a>
                    </div>
                    </div>
                <div class="col-4 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-2 sp-img mb10" href="'.$Site_URL.'/'.$k_photos[1].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[1].'" alt="'.$k[0]['name'].'_2">
                    </a>
                </div>
                </div>
                <div class="col-4 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-3 sp-img mb10" href="'.$Site_URL.'/'.$k_photos[2].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[2].'" alt="'.$k[0]['name'].'_3">
                    </a>
                </div>
                </div>
                
                <div class="col-4 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-4 sp-img" href="'.$Site_URL.'/'.$k_photos[3].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[3].'" alt="'.$k[0]['name'].'_4">
                    </a>
                </div>
                </div>
                <div class="col-4 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-5 sp-img" href="'.$Site_URL.'/'.$k_photos[4].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$k_photos[4].'" alt="'.$k[0]['name'].'_5">
                    </a>
                </div>
                
                </div>
                <div class="col-4 ps-sm-0">
                    <div class="sp-img-content">
                        <a class="popup-img preview-img-2 sp-img mb10" href="'.$Site_URL.'/'.$d_photos[0].'">
                        <img class="w-100" src="'.$Site_URL.'/'.$d_photos[0].'" alt="'.$d[0]['name'].'_1">
                        </a>
                    </div>
                    </div>
                <div class="col-4 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-2 sp-img mb10" href="'.$Site_URL.'/'.$d_photos[1].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$d_photos[1].'" alt="'.$d[0]['name'].'_2">
                    </a>
                </div>
                </div>
                <div class="col-4 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-3 sp-img mb10" href="'.$Site_URL.'/'.$d_photos[2].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$d_photos[2].'" alt="'.$d[0]['name'].'_3">
                    </a>
                </div>
                </div>
                <div class="col-4 ps-sm-0">
                <div class="sp-img-content">
                    <a class="popup-img preview-img-4 sp-img" href="'.$Site_URL.'/'.$d_photos[3].'">
                    <img class="w-100" src="'.$Site_URL.'/'.$d_photos[3].'" alt="'.$d[0]['name'].'_4">
                    </a>
                </div>
                </div>

            </div>
            <a href="'.$Site_URL.'/'.$k_photos[4].'" class="all-tag popup-img orange-btn">عرض جميع الصور</a>

        </div>
        
        ';
        $k_Info = $k[0]['info'] != '' ? json_decode($k[0]['info']) : [];
        if(count($k_Info) > 0){
        echo '

   
        ';
        }

        if(count($d) > 0){
        echo '

        ';
            $d_Info = $d[0]['info'] != '' ? json_decode($d[0]['info']) : [];
            if(count($d_Info) > 0){
            echo '
       
            
            ';
            }
        }
        echo ' 
        
    </div>


<div class="col-lg-12">
    <div class="d-flex align-items-center mb-3 p-2 custom-background-box2" 
         style="border: 1px solid #ddd; border-radius: 5px; cursor: pointer;">
        <div class="me-2 icon-container">
            <img src="img/search.png" alt="رمز البحث" class="search-icon"> 
        </div>
        <div class="flex-grow-1 text-container">
            <span class="highlight-text"> لديك كوبون خصم 30 ريال </span>
        </div>
        <div class="ms-2">
            <button class="search-button">
              MO30
            </button>
        </div>
    </div>
</div>


        <div class="col-md-12">
            <h4 class="title fz17 mb30">أختيارات الحجز</h4>
        </div>
        <div class="col-md-12">
      
            ';
             
            $price = $UmrahData[0]['aprice'] != '' ? json_decode($UmrahData[0]['aprice']) : [];
            for ($i=0; $i < count($price) ; $i++) { 
                $borb = $i < count($price)-1 ? 'bdrb1' : '';

                $n = count($price[$i]->info) > 0 ? preg_replace('/[^0-9]/', '', $price[$i]->info[0]->name) : 0;
                $io = '';
                for ($z=1; $z <= $n ; $z++) { 
                    $io .= '<span class="icon '.($z == $n ? "ml15" : '').' mb10-sm '.$price[$i]->icon.'"></span>';
                }
                echo '
                <div class="booking walkscore d-sm-flex align-items-center mb20 '.$borb.'" id="offer-'.$i.'">
                            <div class="details-booking">
                               <p class="text-room dark-color fw600 mb-1 title">'.$io.' '.$price[$i]->title.' </p>
                                <p class="text mb-1">'.$price[$i]->desc.'</p>
                                
                                <div class="modal-body">
                                    <div class="form-label dark-color">السعر يشمل</div>

    ';
    for ($x = 0; $x < count($price[$i]->info); $x++) { 
        $pad = $x == 0 ? '' : 'pr10';
        $por = $x < count($price[$i]->info) - 1 ? 'bdrr1' : '';
        
        // تحقق مما إذا كانت المعلومات تحتوي على "ريال"
        if (strpos($price[$i]->info[$x]->name, 'ريال') !== false) {
            
            echo ' <a class="text fz15 mb-0 '.$por.' '.$pad.' pl10 bdrrn-sm price-highlight" href="javascript:void(0)">
                    </a><hr>


                    
             <div class="row">

     <div class="col-6">
                ';
                // إضافة عرض السعر في الكلاس aaaaaaaa
                for ($x = 0; $x < count($price[$i]->info); $x++) { 
                    if (strpos($price[$i]->info[$x]->name, 'ريال') !== false) {
                        echo '<a class="text fz15 mb-0 pl10 price-highlight" href="javascript:void(0)">
                                <strong style="color: #4a8f46; font-weight: bold; font-size: 18px;">'.$price[$i]->info[$x]->name.'</strong>
                              </a>
                                <p style="margin: 0; font-size: 12px; color: #008234;">  <i class="fa fal fa-bus-alt"></i> ' . $busType . ' </p>
 
                              ';
                              
                    }
                }

                echo ' 
                </div>
<div class="col-6">
<div class="gonow">
     <a  href="'.$Site_URL.'/confirm/'.base64_encode(json_encode(['id'=> $UmrahData[0]['id'] , 'info' => $i ] , JSON_UNESCAPED_UNICODE )).'" class="'.($UmrahData[0]['active'] == 1 ? '' : 'dis').'"> <i class="far fa-check-circle"></i> احجز </a>
        </div>
        </div>
     </div>      


                    
                    ';
        } else {
            echo ' <a class="listing_info-ofer '.$por.' '.$pad.' pl10 bdrrn-sm" href="javascript:void(0)">
                        <span class="fa '.$price[$i]->info[$x]->icon.'"></span>'.$price[$i]->info[$x]->name.'
                    </a>';
        }
    }
    echo ' 
    </div>


                                                             </div>

                   
                </div>
                
                ';
            }
            echo ' 
        </div>
    </div>
</section>

<div class="modal fade" id="book-trip" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staticBackdropLabel">'.$Title_page.'</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
            <input id="bt_hamla" type="hidden" class="form-control" value="'.$Title_page.'">
            <div class="row">
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">الإسم</label>
                            <input id="bt_name" type="text" class="form-control" placeholder="الإسم">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">رقم الجوال</label>
                            <input id="bt_phone" type="text" class="form-control" placeholder="رقم الجوال">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">عدد الأفراد</label>
                            <input id="bt_people" type="number" class="form-control" value="1" min="1" max="20">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">تاريخ الرحلة</label>
                            <input type="text" id="date_timepicker_end" value="'.date("Y-m-d").'" class="form-control" placeholder="تاريخ الرحلة">
                        </div>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="ui-content mb20">
                        <div class="form-style1">
                            <label class="form-label dark-color">الحجز</label>
                            <div class="bootselect-multiselect">
                                <select id="bt_book" class="form-control">
                                ';
                                for ($i=0; $i < count($price) ; $i++) { 
                                    echo '<option value="'.$i.'">'.$price[$i]->title.'</option>';
                                }
                                echo ' 
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="checkbox-style1">
                        <label class="custom_checkbox">زيارة المدينة +15 ريال
                            <input id="bt_mvist" type="checkbox">
                            <span class="checkmark"></span>
                        </label> 
                    </div>
                </div>
            </div>
      </div>
      <div class="modal-footer">
        <a onclick="ConfirmBookTrip('.$UmrahData[0]['id'].' , '.$i.')" href="javascript:void(0)" class="ud-btn udx-btn btn-thm-border"> <i class="far fa-check-circle"></i> تأكيد الحجز</a>
      </div>
    </div>
  </div>
</div> 

<div class="modal fade" id="res-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staticBackdropLabel">تأكيد الحجز</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
        <div class="modal-body" id="res">

        </div>
      </div>
    </div>
</div> 
    <div class="container">

<div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 mt30 overflow-hidden position-relative">
    <div class="reviews-container">
        <div class="row">
            <div class="col-lg-4 col-12"> 
                <div class="totel-reviwe"> 
                <h4 class="title fz17 mb30">تقييمات عن '.$k[0]['name'].'</h4>
                    <h5 style=" color: #ffc107;">التقييم الإجمالي</h5>
                    <p style="font-size: 24px; font-weight: bold;">
                        ';
                        $totalRating = 0;
                        foreach ($reviews as $review) {
                            $totalRating += $review['rating'];
                        }
                        $averageRating = count($reviews) > 0 ? $totalRating / count($reviews) : 0;
                        echo number_format($averageRating, 1) . '/10';
                    echo '
                    </p>
                    <p>عدد التقييمات: '.count($reviews).'</p>
                </div>
            </div>

            <div class="col-lg-8 col-12"> 
                <div class="row"> 
                ';
                if (count($reviews) > 0) {
                    foreach ($reviews as $index => $review) {
                        // إظهار 5 تقييمات فقط
                        if ($index < 6) {
                            echo '
                            <div class="col-lg-6 col-12 mb-3"> 
                                <div class="review-item">
                                    <div class="row">
                                        <div class="col-6 d-flex align-items-center">
                                         <div class="avatar-circle">
                                          '.strtoupper(mb_substr($review['reviewer_name'], 0, 1, 'UTF-8')).'
                                            </div>
                                           <span style="font-weight: bold;">'.$review['reviewer_name'].'</span>
                                        </div>
                                        <div class="col-6 text-end">
                                            <span style="font-weight: bold; color: #007bff;">'.$review['rating'].'/10</span>
                                            <p style="margin: 0;">مراجعه من <span style="color: #1c44a1;font-weight: 600;">'.$review['review_source'].'</span></p>
                                        </div>
                                    </div>
                                    <p><strong><i class="fa-light fa-face-smile" style="color: green;"></i></strong> '.$review['pros_comment'].'</p>
                                    <p><strong><i class="fa-light fa-face-frown" style="color: red;"></i></strong> '.$review['cons_comment'].'</p>
                                </div>
                            </div>
                            ';
                        }
                    }
                } else {
                    echo '<p>لا توجد تقييمات لهذا الفندق.</p>';
                }
                echo '
                </div> 
            </div>
        </div>
    </div>
   
</div>
</div>
';

include('footer.php');