<?php
ob_start();
$Title_page = 'مكتبة الصور' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');

if ( is_dir ("../files/") === false ){
	mkdir("../files/");
}
//---------------------------------------------------
?>
<style> 
	.dropzone {
	    min-height: 100px;
	    border: 1px dashed #b3b3b3;
	    background: #f3f3f33b;
	    padding: 0;
	    margin-bottom: 10px;
	}
	.dropzone h5{margin-top: 42px; color: #b3b3b3; font-weight: normal;display: inline-block;}
	#for_photos{
		width: 100%;
		display: inline-block;
		margin-bottom: 20px;
		padding: 10px;
		overflow: auto;
	}
	.postimg{
		width: 100%;
		height: 200px;
		padding: 2px;
		border: 1px solid #eee;
		border-radius: 5px;
		margin-bottom: 10px;
	}
	.md_img{
		width: 220px;
		height: 145px;
		display: inline-block;
		margin: 5px;
		border-radius: 5px;
		padding: 2px;
		border: 2px solid #484b54;
		position: relative;
		background: #fff;
	}
	.md_img a {
		position: absolute;
		top: 0;
		left: 0;
		color: #fff !important;
		background: #0000005c;
		width: 70px;
		font-weight: bold;
		height: 70px;
		line-height: 70px;
		font-size: 40px;
		text-align: center;
		border-radius: 50px;
		border: 1px solid #00000075;
		cursor: pointer;
		opacity: 0.3;
		right: 0;
		bottom: 0;
		margin: auto;
	}
	.md_img a:hover , .md_img a:focus , .md_img a:active{
		color: #E91E63 !important;
		background: #0000005c;
		border: 2px solid #e91e638c;
		opacity: 0.8;
	}
	.md_img img{
		width: 100%;
		max-width: 100%;
		max-height: 100%;
		border-radius: 2px;
	}
	.bwh{
		color: #fff !important;
		font-weight: bold;
		font-size: 16px;
	}
	.bwh .fa{
		margin-left: 5px;
	}
	textarea{
		min-height: 150px;
	}
    .form-group {
        margin-bottom: 0px;
        margin-top: 25px;
    }
	.mt0{
		margin-top: 5px;
	}
</style>


<?php
if (isset($_GET['do']) && $_GET['do'] == 'delimg' && isset($_GET['id'])  ){
	try{
		$path = '../files/'.$_GET['id'];
		unlink($path);
	}catch(Exception $e){}
	header('Location: photos.php'); exit();
    //$ch = getAllFrom('*' , 'uploaded_files' , 'WHERE id = "'.$_GET['id'].'" ', '');
	//if (count($ch) > 0 ){
        //$path = '../'.$ch[0]['link'];
        //unlink($path);
       // DeleteColum( 'uploaded_files', 'WHERE id = '.$_GET['id'] );	
        //header('Location: photos.php'); exit();

    //}
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
		     <h3>هل انت متأكد من انك تريد حذف هذه الصورة ؟</h3>
		     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>

		     <center>
		     	<a class="btn btn-danger btn-md" href="photos.php?do=delimg&confirm=1&id=<?php echo $_GET['id'] ;?>">تاكيد</a>
		     	<a class="btn btn-success btn-md" href="photos.php">رجوع</a>
		     </center>
		 
	</div>	
	</div>
</div>
</div>
<?php
		
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
                <h4>رفع صورة جديدة</h4>
				<p>نوصي برفع الصور بصيغة webp يمكنك الدخول على هذا <a style="color: #1f41c9;" href="https://image.online-convert.com/ar/convert-to-webp" target="_blank">الموقع</a> و تحويل الصور</p><br>
                <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
                    <input type="file" name="photo" id="photo" required style="display: none;" />
                    <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
                    <input type="hidden" name="Image_For" value="Uploads">
					
                </form>
                    <label for="photo" class="btn btn-gradient-primary btn-round  waves-effect waves-light" ><i class="icoz mdi mdi-camera"></i> إختر الصوره</label>
                    <label for="Uploads" class="btn btn-gradient-primary btn-round  waves-effect waves-light" ><i class="icoz mdi mdi-cloud-upload"></i> رفع الصوره</label>
			</div>
		</div><hr>
	</div>			
</div>
<div class="row">
<div class="col-md-12">
    <center>
    <?php
 
    //$ph = getAllFrom('*' , 'uploaded_files' , 'ORDER BY id DESC', '');
	$dir_name = "../files/";
	$ph = Get_ImagesToFolder($dir_name);
    if (count($ph) > 0){
        echo '<div class="row">';
        for ($i=0; $i <= count($ph)-1 ; $i++) { 
            echo '<div class="col-md-3 col-xs-6"><div class="form-group"><input onclick="this.select();
            document.execCommand(\'copy\');" type="text" class="form-control ltr" value="files/'.$ph[$i]	.'"></div><div class="form-group mt0"><input onclick="this.select();
            document.execCommand(\'copy\');" type="text" class="form-control ltr" value="'.$Site_URL.'/files/'.$ph[$i]	.'"></div><div class="md_img"><img src="'.$Site_URL.'/files/'.$ph[$i]	.'"><a href="photos.php?do=del&id='.$ph[$i] .'"><i class="fa fa-trash" aria-hidden="true"></i></a></div></div>';
        }       
        echo '</div>';
    }else{
        echo  Show_Alert('warning' , 'لا يوجد صور. ');
    }
    ?>
    </center>
</div>
</div>

<?php
}
include('footer.php'); 
ob_end_flush();
?>