<?php
include('webset.php');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: bus-booking.php');
    exit;
}

// التحقق من البيانات المطلوبة
$required_fields = ['trip_id', 'travel_date', 'seats_count', 'total_amount', 'first_name', 'last_name', 'phone', 'email', 'id_number', 'nationality'];

foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        $_SESSION['error'] = 'جميع البيانات مطلوبة';
        header('Location: bus-booking.php');
        exit;
    }
}

// تنظيف البيانات
$trip_id = intval($_POST['trip_id']);
$travel_date = $_POST['travel_date'];
$seats_count = intval($_POST['seats_count']);
$total_amount = floatval($_POST['total_amount']);
$first_name = trim($_POST['first_name']);
$last_name = trim($_POST['last_name']);
$phone = trim($_POST['phone']);
$email = trim($_POST['email']);
$id_number = trim($_POST['id_number']);
$nationality = trim($_POST['nationality']);
$notes = trim($_POST['notes'] ?? '');

try {
    // بدء المعاملة
    $db->beginTransaction();

    // التحقق من صحة الرحلة
    $stmt = $db->prepare("SELECT * FROM bus_trips WHERE id = ? AND status = 1");
    $stmt->execute([$trip_id]);
    $trip = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$trip) {
        throw new Exception('الرحلة غير موجودة أو غير متاحة');
    }

    // التحقق من توفر المقاعد
    $stmt = $db->prepare("SELECT COALESCE(SUM(seats_count), 0) as booked_seats
                         FROM bus_bookings
                         WHERE trip_id = ? AND travel_date = ? AND booking_status != 'cancelled'");
    $stmt->execute([$trip_id, $travel_date]);
    $booked_seats = $stmt->fetchColumn();

    $available_seats = $trip['total_seats'] - $booked_seats;

    if ($available_seats < $seats_count) {
        throw new Exception('عذراً، لا توجد مقاعد كافية متاحة. المقاعد المتاحة: ' . $available_seats);
    }

    // التحقق من صحة التاريخ
    $travel_timestamp = strtotime($travel_date);
    if ($travel_timestamp < strtotime('today')) {
        throw new Exception('لا يمكن الحجز لتاريخ سابق');
    }

    // التحقق من صحة السعر
    $expected_total = $trip['seat_price'] * $seats_count;
    if (abs($total_amount - $expected_total) > 0.01) {
        throw new Exception('خطأ في حساب السعر الإجمالي');
    }

    // إنشاء رقم مرجعي للحجز
    $booking_reference = 'BUS' . date('Ymd') . sprintf('%04d', rand(1000, 9999));

    // التأكد من عدم تكرار الرقم المرجعي
    $stmt = $db->prepare("SELECT id FROM bus_bookings WHERE booking_reference = ?");
    $stmt->execute([$booking_reference]);
    while ($stmt->fetch()) {
        $booking_reference = 'BUS' . date('Ymd') . sprintf('%04d', rand(1000, 9999));
        $stmt->execute([$booking_reference]);
    }

    // إدراج الحجز
    $passenger_name = $first_name . ' ' . $last_name;
    $stmt = $db->prepare("INSERT INTO bus_bookings
                         (trip_id, passenger_name, passenger_phone, passenger_email,
                          travel_date, seats_count, total_amount, notes, booking_reference, booking_status)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed')");

    $stmt->execute([
        $trip_id, $passenger_name, $phone, $email,
        $travel_date, $seats_count, $total_amount, $notes, $booking_reference
    ]);

    $booking_id = $db->lastInsertId();

    // تأكيد المعاملة
    $db->commit();

    // إعداد بيانات الجلسة للتأكيد
    $_SESSION['booking_success'] = [
        'booking_id' => $booking_id,
        'booking_reference' => $booking_reference,
        'trip' => $trip,
        'travel_date' => $travel_date,
        'seats_count' => $seats_count,
        'total_amount' => $total_amount,
        'passenger_name' => $first_name . ' ' . $last_name,
        'phone' => $phone,
        'email' => $email
    ];

    // التوجه لصفحة التأكيد
    header('Location: booking-confirmation.php?ref=' . $booking_reference);
    exit;

} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $db->rollback();

    $_SESSION['error'] = $e->getMessage();

    // إعادة توجيه مع البيانات
    $redirect_data = [
        'data' => urlencode(json_encode([
            'trip_id' => $trip_id,
            'travel_date' => $travel_date,
            'passenger_count' => $seats_count,
            'total_price' => $total_amount
        ]))
    ];

    header('Location: bus-booking-form.php?' . http_build_query($redirect_data));
    exit;
}
?>
