<?php
ob_start();
$Title_page = 'المقالات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');

//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة مقالة جديدة</h4>
                    <a style="color:#fff" href="contents.php" class="btn btn-sm btn-gradient-danger waves-effect waves-light mrb_10 padrl20">رجوع</a>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
                <div class="row">
                <input value="0" class="form-control" type="hidden" id="p0">
					<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">عنوان المقالة</label>
	                      <input type="text" id="p1" value="" class="form-control">
	                    </div>
               	 	</div>

                    <div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">رابط المقالة</label>
	                      <input type="text" id="p2" value="" class="form-control ltr">
	                    </div>
               	 	</div>

                    <div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">رابط صورة المقالة (700*1024)</label>
	                      <input type="text" id="p3" value="" class="form-control ltr">
	                    </div>
               	 	</div>

               	 	<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">إختر الخدمه</label>
	                      <select id="p4" class="form-control">
	                      		<option value="0">إختر الخدمه</option>
	                      		<?php
	                      		$cats = getAllFrom('*' , 'services' , 'WHERE status = 1 ', 'ORDER BY orders ASC, id ASC');
				                if (count($cats) > 0 ){
				                	for ($i=0; $i <= count($cats)-1 ; $i++) {
				                		echo '<option value="'.$cats[$i]['id'].'">'.$cats[$i]['name'].'</option>';
				                    }
				                }
	                      		?>
	                      </select>
	                    </div>
               	 	</div>

                  <div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">وصف قصير للمقالة</label>
	                      <textarea id="p5" class="form-control"></textarea>
	                    </div>
               	 	</div>
               	 	<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">الكلمات الدلاليه (افصل بينهم بـ ,)</label>
	                      <textarea id="p6" class="form-control"></textarea>
	                    </div>
               	 	</div>
                </div>
				</div>
			</div>
		</div>
	</div>	


    <div class="row">
        <div class="col-md-12" id="non4"  >
            <label class="form-control-label">محتوي المقالة</label>
            <div id="toolbar-container"></div>
            <div id="editor" name="editor" class="editor">
            <?php //echo $ch[0]['descr'];?>
            </div>
        </div>
    </div>
    <script src="//cdn.ckeditor.com/4.14.1/full/ckeditor.js"></script>
    <script>
        var editorData;  
        CKEDITOR.editorConfig = function(config) {
            config.toolbarGroups = [
                { name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
                { name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
                { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
                { name: 'links', groups: [ 'links' ] },
                { name: 'insert', groups: [ 'insert' ] },
                '/',
                { name: 'styles', groups: [ 'styles' ] },
                { name: 'colors', groups: [ 'colors' ] },
                { name: 'tools', groups: [ 'tools' ] },
                { name: 'others', groups: [ 'others' ] },
                { name: 'about', groups: [ 'about' ] }
            ];
        };
        CKEDITOR.config.allowedContent = true;
        CKEDITOR.config.disallowedContent = 'span';
        CKEDITOR.config.removeButtons = 'Save,NewPage,Preview,Print,Templates,PasteText,PasteFromWord,Replace,Find,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,CopyFormatting,Outdent,Indent,Blockquote,CreateDiv,BidiLtr,BidiRtl,Language,Anchor,Flash,HorizontalRule,Smiley,SpecialChar,PageBreak,About,Maximize,ShowBlocks';

        CKEDITOR.config.contentsLangDirection = 'rtl';
        CKEDITOR.config.dialog_buttonsOrder = 'rtl';
        CKEDITOR.config.language = 'ar';
        CKEDITOR.config.height = 400;
        //CKEDITOR.config.allowedContent = 'u em strong ul li a h1 h2 h3 h4 h5 h6 p b table iframe img{width,height}';
        editorData = CKEDITOR.replace('editor'); 
    </script>
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <button onclick="Savecontents()" id="saveBtn" class="btn btn-gradient-primary btn-round  waves-effect waves-light"><i class="icoz mdi mdi-file-document-box"></i> إضافة المقالة </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>                            


<?php	
//---------------------------------------------------	
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'contents' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> التعديل على "<?php echo $ch[0]['title'];?>"</h4>
                    <a style="color:#fff" href="contents.php" class="btn btn-sm btn-gradient-danger waves-effect waves-light mrb_10 padrl20">رجوع</a>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
                <div class="row">
                <input value="<?php echo $ch[0]['id'];?>" class="form-control" type="hidden" id="p0">
					<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">إسم المقالة</label>
	                      <input type="text" id="p1" value="<?php echo $ch[0]['title'];?>" class="form-control">
	                    </div>
               	 	</div>

                    <div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">رابط المقالة</label>
	                      <input type="text" id="p2" value="<?php echo $ch[0]['link'];?>" class="form-control ltr">
	                    </div>
               	 	</div>

                    <div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">رابط صورة المقالة (700*1024)</label>
	                      <input type="text" id="p3" value="<?php echo $ch[0]['photo'];?>" class="form-control ltr">
	                    </div>
               	 	</div>

               	 	<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">إختر الخدمه</label>
	                      <select id="p4" class="form-control">
	                      		<option value="0">إختر الخدمه</option>
	                      		<?php
	                      		$cats = getAllFrom('*' , 'services' , 'WHERE status = 1 ', 'ORDER BY orders ASC, id ASC');
				                if (count($cats) > 0 ){
				                	for ($i=0; $i <= count($cats)-1 ; $i++) {
                                        if ($cats[$i]['id'] == $ch[0]['serviceid']){$sel = 'selected' ; }else{$sel = '';}
				                		echo '<option value="'.$cats[$i]['id'].'" '.$sel.'>'.$cats[$i]['name'].'</option>';
				                    }
				                }
	                      		?>
	                      </select>
	                    </div>
               	 	</div>

                  <div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">وصف قصير للمقالة</label>
	                      <textarea id="p5" class="form-control"><?php echo $ch[0]['descr'];?></textarea>
	                    </div>
               	 	</div>
               	 	<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">الكلمات الدلاليه (افصل بينهم بـ ,)</label>
	                      <textarea id="p6" class="form-control"><?php echo $ch[0]['tags'];?></textarea>
	                    </div>
               	 	</div>
                </div>
				</div>
			</div>
		</div>
	</div>	


    <div class="row">
        <div class="col-md-12" id="non4"  >
            <label class="form-control-label">محتوي المقالة</label>
            <div id="toolbar-container"></div>
            <div id="editor" name="editor" class="editor">
            <?php echo $ch[0]['content'];?>
            </div>
        </div>
    </div>
    <script src="//cdn.ckeditor.com/4.14.1/full/ckeditor.js"></script>
    <script>
        var editorData;  
        CKEDITOR.editorConfig = function(config) {
            config.toolbarGroups = [
                { name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
                { name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
                { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
                { name: 'links', groups: [ 'links' ] },
                { name: 'insert', groups: [ 'insert' ] },
                '/',
                { name: 'styles', groups: [ 'styles' ] },
                { name: 'colors', groups: [ 'colors' ] },
                { name: 'tools', groups: [ 'tools' ] },
                { name: 'others', groups: [ 'others' ] },
                { name: 'about', groups: [ 'about' ] }
            ];
        };
        
        CKEDITOR.config.allowedContent = true;
        CKEDITOR.config.disallowedContent = 'span';
        CKEDITOR.config.removeButtons = 'Save,NewPage,Preview,Print,Templates,PasteText,PasteFromWord,Replace,Find,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,CopyFormatting,Outdent,Indent,Blockquote,CreateDiv,BidiLtr,BidiRtl,Language,Anchor,Flash,HorizontalRule,Smiley,SpecialChar,PageBreak,About,Maximize,ShowBlocks';

        CKEDITOR.config.contentsLangDirection = 'rtl';
        CKEDITOR.config.dialog_buttonsOrder = 'rtl';
        CKEDITOR.config.language = 'ar';
        CKEDITOR.config.height = 400;
        //CKEDITOR.config.allowedContent = 'u em strong ul li a h1 h2 h3 h4 h5 h6 p b table iframe img{width,height}';
        editorData = CKEDITOR.replace('editor'); 
    </script>
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <button onclick="Savecontents()" id="saveBtn" class="btn btn-gradient-primary btn-round  waves-effect waves-light"><i class="icoz mdi mdi-file-document-box"></i> تعديل المقالة </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>                            
	
	<?php	
	}else{
		header('Location: contents.php'); exit();
	} 
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="contents.php?do=add_new" class="btn btn-md btn-gradient-primary waves-effect waves-light">إضافة مقالة جديدة</a>
			</div>
		</div>
		<hr>
	</div>			
</div>


<div class="row">
    <div class="col-12">
        <div class="card">
        <div class="card-body">
            <table id="datatables" style="display:none" class="table table-striped  table-hover " cellspacing="0" width="100%" style="width:100%">
              <thead>
              <tr role="row">
                <th class="all">#رقم</th>
                <th class="all">عنوان المقالة</th>
                <th class="mobile-p desktop">القسم </th>
                <th class="mobile-p desktop">تاريخ الإضافة </th>
                <th class="all">الإجراء </th>
            </tr>
              </thead>
              <tbody id="DataTableAll"></tbody>
            </table>
        </div>
        </div>
    </div>
    <!-- end col -->
</div>
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>
	function GetData(){
    $('#datatables').hide();
    $.post("ajax.php", { action : 'DataTableAll' , type : 'contents'} ,function(data){ 
        if ( $.fn.DataTable.isDataTable('#datatables') ) {
          $('#datatables').DataTable().destroy();
        }
        $('#DataTableAll').html(data);  
          table = $('#datatables').DataTable({
            "pagingType": "full_numbers",
            "lengthMenu": [
              [ 50, 100, 200, 500, 1000, -1],
              [ 50, 100, 200, 500, 1000, "كل  المقالات"]
            ],
            'destroy': true,
            responsive:true,
            //"order": [[ 2, "asc" ]],
            language: {
              search: "البحث",
              searchPlaceholder: "البحث عن مقاله",
            }
          });

        $('#datatables').show(200);  

    });
  }

  $(document).ready(function() {
    $('#datatables').hide();
    GetData();
  });


    function ShowHideContent(id , type){
        if (type == 1){
            var txt = "برجاء العلم انه سيتم عرض المقالة فى الموقع.";
        }else{
            var txt = "برجاء العلم انه سيتم إخفاء المقالة من الموقع.";
        }
        Swal({
        title: 'هل انت متأكد؟',
        text: txt,
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'ShowHideContent',id : id ,type : type} ,function(data){ 
            Swal({
              title: 'تم الإجراء بنجاح',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetData();
          });  
          
        }
      })
    }

    function Savecontents(){
        var err = false; var res = "";
        var content = editorData.getData();
        var var0 = $('#p0').val();
        var var1 = $('#p1').val();
        var var2 = $('#p2').val();
        var var3 = $('#p3').val();
        var var4 = $('#p4').val();
        var var5 = $('#p5').val();
        var var6 = $('#p6').val();
        if(var1 == ""){err = true; res = "يجب كتابة اسم المقالة";}
        else if(var2 == ""){err = true; res = "يجب كتابة رابط المقالة";}
        else if(var3 == ""){err = true; res = "يجب كتابة رابط صورة المقالة";}
        else if(var4 == "" || var4 == "0"){err = true; res = "يجب اختيار الخدمه";}

        if(err){
            Swal({
              title: 'خطأ',
              text: res,
              type: 'warning',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
        }else{
            $.post("ajax.php", { action : 'Savecontents' , content : content , var0 : var0 , var1 : var1 , var2 : var2 , var3 : var3 , var4 : var4 , var5 : var5 , var6 : var6 } ,function(data){
               if(data != ""){
                    window.location.href = data ;
               }else{
                    window.location.href = 'contents.php';
               }
            });
        }
    }

    function DelContentAction(id){
        Swal({
        title: 'هل أنت متأكد انك تريد حذف هذه المقالة؟',
        text: "برجاء العلم انه سيتم الحذف بشكل نهائي",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'DelContentAction',id : id} ,function(data){ 
            Swal({
              title: 'تم الحذف',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetData();
          });  
          
        }
      })
    }
</script>