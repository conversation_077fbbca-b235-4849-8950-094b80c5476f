<?php
include('webset.php');

echo "<h2>إدارة حجوزات الفنادق</h2>";

// جلب الحجوزات
$bookings = getAllFrom('hb.*, h.name as hotel_name, hc.city_name', 
                      'hotel_bookings hb 
                       JOIN hotels_booking h ON hb.hotel_id = h.id 
                       JOIN hotel_city hc ON h.hotel_city_id = hc.id', 
                      '', 'ORDER BY hb.booking_date DESC');

// إحصائيات سريعة
$total_bookings = count($bookings);
$pending_bookings = count(array_filter($bookings, function($b) { return $b['booking_status'] == 'pending'; }));
$confirmed_bookings = count(array_filter($bookings, function($b) { return $b['booking_status'] == 'confirmed'; }));
$total_revenue = array_sum(array_column($bookings, 'total_cost'));

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

$stats = [
    ['title' => 'إجمالي الحجوزات', 'value' => $total_bookings, 'color' => '#007bff', 'icon' => 'fas fa-calendar-check'],
    ['title' => 'حجوزات معلقة', 'value' => $pending_bookings, 'color' => '#ffc107', 'icon' => 'fas fa-clock'],
    ['title' => 'حجوزات مؤكدة', 'value' => $confirmed_bookings, 'color' => '#28a745', 'icon' => 'fas fa-check-circle'],
    ['title' => 'إجمالي الإيرادات', 'value' => number_format($total_revenue) . ' ريال', 'color' => '#17a2b8', 'icon' => 'fas fa-money-bill-wave'],
];

foreach($stats as $stat) {
    echo "<div style='background: ".$stat['color']."; color: white; padding: 20px; border-radius: 8px; text-align: center;'>";
    echo "<i class='".$stat['icon']."' style='font-size: 2rem; margin-bottom: 10px;'></i>";
    echo "<h3 style='margin: 0; font-size: 1.5rem;'>".$stat['value']."</h3>";
    echo "<p style='margin: 5px 0 0 0; opacity: 0.9;'>".$stat['title']."</p>";
    echo "</div>";
}

echo "</div>";

// البحث والفلترة
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>البحث والفلترة</h4>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
echo "<input type='text' id='search_booking' placeholder='البحث برقم الحجز أو اسم النزيل' style='padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
echo "<select id='filter_status' style='padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
echo "<option value=''>جميع الحالات</option>";
echo "<option value='pending'>معلق</option>";
echo "<option value='confirmed'>مؤكد</option>";
echo "<option value='cancelled'>ملغي</option>";
echo "<option value='completed'>مكتمل</option>";
echo "</select>";
echo "<select id='filter_hotel' style='padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
echo "<option value=''>جميع الفنادق</option>";
$hotels = array_unique(array_column($bookings, 'hotel_name'));
foreach($hotels as $hotel) {
    echo "<option value='".$hotel."'>".$hotel."</option>";
}
echo "</select>";
echo "</div>";
echo "</div>";

// جدول الحجوزات
if($total_bookings > 0) {
    echo "<div style='overflow-x: auto;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;' id='bookings_table'>";
    echo "<thead>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 12px;'>رقم الحجز</th>";
    echo "<th style='padding: 12px;'>النزيل</th>";
    echo "<th style='padding: 12px;'>الفندق</th>";
    echo "<th style='padding: 12px;'>نوع الغرفة</th>";
    echo "<th style='padding: 12px;'>التواريخ</th>";
    echo "<th style='padding: 12px;'>الليالي</th>";
    echo "<th style='padding: 12px;'>الغرف</th>";
    echo "<th style='padding: 12px;'>التكلفة</th>";
    echo "<th style='padding: 12px;'>الحالة</th>";
    echo "<th style='padding: 12px;'>تاريخ الحجز</th>";
    echo "<th style='padding: 12px;'>إجراءات</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach($bookings as $booking) {
        $status_colors = [
            'pending' => '#ffc107',
            'confirmed' => '#28a745',
            'cancelled' => '#dc3545',
            'completed' => '#6c757d'
        ];
        
        $status_names = [
            'pending' => 'معلق',
            'confirmed' => 'مؤكد',
            'cancelled' => 'ملغي',
            'completed' => 'مكتمل'
        ];
        
        $status_color = $status_colors[$booking['booking_status']] ?? '#6c757d';
        $status_name = $status_names[$booking['booking_status']] ?? $booking['booking_status'];
        
        echo "<tr class='booking-row' data-status='".$booking['booking_status']."' data-hotel='".$booking['hotel_name']."' data-guest='".$booking['guest_name']."' data-reference='".$booking['booking_reference']."'>";
        echo "<td style='padding: 8px; font-weight: bold;'>".$booking['booking_reference']."</td>";
        echo "<td style='padding: 8px;'>";
        echo "<strong>".$booking['guest_name']."</strong><br>";
        echo "<small>".$booking['guest_phone']."</small>";
        if($booking['guest_email']) {
            echo "<br><small>".$booking['guest_email']."</small>";
        }
        echo "</td>";
        echo "<td style='padding: 8px;'>";
        echo "<strong>".$booking['hotel_name']."</strong><br>";
        echo "<small>".$booking['city_name']."</small>";
        echo "</td>";
        echo "<td style='padding: 8px;'>".$booking['room_name']."</td>";
        echo "<td style='padding: 8px;'>";
        echo "<small>دخول: ".date('d/m/Y', strtotime($booking['check_in_date']))."</small><br>";
        echo "<small>خروج: ".date('d/m/Y', strtotime($booking['check_out_date']))."</small>";
        echo "</td>";
        echo "<td style='padding: 8px; text-align: center;'>".$booking['nights']."</td>";
        echo "<td style='padding: 8px; text-align: center;'>".$booking['room_count']."</td>";
        echo "<td style='padding: 8px; text-align: center; font-weight: bold;'>".number_format($booking['total_cost'])." ريال</td>";
        echo "<td style='padding: 8px; text-align: center;'>";
        echo "<span style='background: ".$status_color."; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;'>".$status_name."</span>";
        echo "</td>";
        echo "<td style='padding: 8px; text-align: center;'>".date('d/m/Y H:i', strtotime($booking['booking_date']))."</td>";
        echo "<td style='padding: 8px; text-align: center;'>";
        
        if($booking['booking_status'] == 'pending') {
            echo "<button onclick='updateBookingStatus(\"".$booking['id']."\", \"confirmed\")' style='background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 4px; margin: 2px; cursor: pointer;'>تأكيد</button>";
            echo "<button onclick='updateBookingStatus(\"".$booking['id']."\", \"cancelled\")' style='background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; margin: 2px; cursor: pointer;'>إلغاء</button>";
        } elseif($booking['booking_status'] == 'confirmed') {
            echo "<button onclick='updateBookingStatus(\"".$booking['id']."\", \"completed\")' style='background: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 4px; margin: 2px; cursor: pointer;'>مكتمل</button>";
        }
        
        echo "<button onclick='viewBookingDetails(\"".$booking['booking_reference']."\")' style='background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; margin: 2px; cursor: pointer;'>تفاصيل</button>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
} else {
    echo "<div style='text-center; padding: 40px; background: #f8f9fa; border-radius: 8px;'>";
    echo "<i class='fas fa-calendar-times fa-3x text-muted mb-3'></i>";
    echo "<h4 class='text-muted'>لا توجد حجوزات بعد</h4>";
    echo "<p class='text-muted'>ستظهر الحجوزات هنا عند إنشائها</p>";
    echo "</div>";
}

echo "<script>
// البحث والفلترة
document.getElementById('search_booking').addEventListener('input', filterBookings);
document.getElementById('filter_status').addEventListener('change', filterBookings);
document.getElementById('filter_hotel').addEventListener('change', filterBookings);

function filterBookings() {
    const searchTerm = document.getElementById('search_booking').value.toLowerCase();
    const statusFilter = document.getElementById('filter_status').value;
    const hotelFilter = document.getElementById('filter_hotel').value;
    
    const rows = document.querySelectorAll('.booking-row');
    
    rows.forEach(row => {
        const reference = row.getAttribute('data-reference').toLowerCase();
        const guest = row.getAttribute('data-guest').toLowerCase();
        const status = row.getAttribute('data-status');
        const hotel = row.getAttribute('data-hotel');
        
        let showRow = true;
        
        // فلترة البحث
        if (searchTerm && !reference.includes(searchTerm) && !guest.includes(searchTerm)) {
            showRow = false;
        }
        
        // فلترة الحالة
        if (statusFilter && status !== statusFilter) {
            showRow = false;
        }
        
        // فلترة الفندق
        if (hotelFilter && hotel !== hotelFilter) {
            showRow = false;
        }
        
        row.style.display = showRow ? '' : 'none';
    });
}

// تحديث حالة الحجز
function updateBookingStatus(bookingId, newStatus) {
    if (confirm('هل أنت متأكد من تغيير حالة الحجز؟')) {
        // يمكن إضافة AJAX هنا لتحديث الحالة
        alert('سيتم تطوير هذه الميزة قريباً');
    }
}

// عرض تفاصيل الحجز
function viewBookingDetails(bookingReference) {
    alert('عرض تفاصيل الحجز: ' + bookingReference + '\\nسيتم تطوير هذه الميزة قريباً');
}
</script>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 0 10px;'>العودة للرئيسية</a>";
echo "<a href='hotels-makkah' style='background: #ff9b00; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 0 10px;'>فنادق مكة</a>";
echo "<a href='hotels-madinah' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 0 10px;'>فنادق المدينة</a>";
echo "</div>";
?>
