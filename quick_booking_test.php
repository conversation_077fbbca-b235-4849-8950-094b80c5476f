<?php
// اختبار سريع للحجز
$booking_data = [
    'trip_id' => 1,
    'travel_date' => date('Y-m-d', strtotime('+1 day')),
    'passenger_count' => 1,
    'total_price' => 100
];

$encoded_data = urlencode(json_encode($booking_data));
$booking_form_url = "http://localhost/moo/bus-booking-form.php?data=" . $encoded_data;

echo "<h2>اختبار سريع للحجز</h2>";
echo "<p>سيتم فتح نموذج الحجز مع البيانات التالية:</p>";
echo "<ul>";
echo "<li>رقم الرحلة: " . $booking_data['trip_id'] . "</li>";
echo "<li>تاريخ السفر: " . $booking_data['travel_date'] . "</li>";
echo "<li>عدد المسافرين: " . $booking_data['passenger_count'] . "</li>";
echo "<li>السعر الإجمالي: " . $booking_data['total_price'] . " ريال</li>";
echo "</ul>";

echo "<a href='" . $booking_form_url . "' target='_blank' style='display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0;'>فتح نموذج الحجز</a>";

echo "<br><br>";
echo "<h3>أو اختبر الحجز المباشر:</h3>";

// نموذج اختبار مباشر
echo '
<form action="process-bus-booking.php" method="post" style="max-width: 500px; margin: 20px 0;">
    <input type="hidden" name="trip_id" value="1">
    <input type="hidden" name="travel_date" value="' . date('Y-m-d', strtotime('+1 day')) . '">
    <input type="hidden" name="seats_count" value="1">
    <input type="hidden" name="total_amount" value="100">
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم الأول:</label>
        <input type="text" name="first_name" value="أحمد" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم العائلة:</label>
        <input type="text" name="last_name" value="محمد" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الجوال:</label>
        <input type="tel" name="phone" value="0501234567" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">البريد الإلكتروني:</label>
        <input type="email" name="email" value="<EMAIL>" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهوية:</label>
        <input type="text" name="id_number" value="1234567890" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الجنسية:</label>
        <select name="nationality" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            <option value="سعودي">سعودي</option>
            <option value="مقيم">مقيم</option>
            <option value="زائر">زائر</option>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
        <textarea name="notes" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 60px;">اختبار الحجز</textarea>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: flex; align-items: center;">
            <input type="checkbox" name="terms" required style="margin-left: 8px;">
            أوافق على الشروط والأحكام
        </label>
    </div>
    
    <button type="submit" style="background: #28a745; color: white; border: none; padding: 12px 30px; border-radius: 5px; font-size: 16px; cursor: pointer;">
        تأكيد الحجز
    </button>
</form>
';

echo "<br><br>";
echo "<h3>روابط مفيدة للاختبار:</h3>";
echo "<ul>";
echo "<li><a href='bus-booking.php' target='_blank'>صفحة الحجز الرئيسية</a></li>";
echo "<li><a href='test_api.php' target='_blank'>اختبار API</a></li>";
echo "<li><a href='test_booking.php' target='_blank'>اختبار تفاعلي</a></li>";
echo "<li><a href='debug_status.php' target='_blank'>تشخيص قاعدة البيانات</a></li>";
echo "</ul>";
?>
