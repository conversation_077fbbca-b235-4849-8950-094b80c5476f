<?php
ob_start();
//---------------------------------------------------------------------------------------------
function GetSubCategory($category){
    global $AllCategory;
    $sub = [];
    for ($i=0; $i < count($AllCategory) ; $i++) { 
        if($AllCategory[$i]['parent'] == $category['id']){
            array_push($sub , $AllCategory[$i]);
        }
    }
    return $sub;
}
//---------------------------------------------------------------------------------------------
















function GetCityBox($city) {
    // استعلام لجلب عدد الرحلات المتاحة للمدينة
    global $db; // Assuming you're using a database connection object
    $cityName = $city['name'];
    $query = "SELECT COUNT(*) as totalOffers FROM offers WHERE from_to = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$cityName]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $totalOffers = $result['totalOffers'] ? $result['totalOffers'] : 0;

    // تعديل تنسيق HTML ليكون دائريًا ومع الكلاسات المطلوبة
    return '
    <div class="item">
    <a href="' . $city['link'] . '">
        <div class="feature-img rounded position-relative">
            <img class="feature-img" src="' . $city['photo'] . '" alt="' . $city['name'] . '">
            <div class="cityhome-title-overlay position-absolute">
                <h5 class="cityhome-title">' . $city['name'] . '</h5>
            </div>
        </div>
        <div class="feature-titl">
            <p class="fz15 fw400 dark-color mb-0">رحلات عمرة من ' . $city['name'] . '</p>
            <p style="font-size: 16px; margin-top: -5px; color: #000; font-weight: 300;">تم العثور <span style="color: #fb8506; font-size: 12px;">' . $totalOffers . '</span> من الرحلات</p>
        </div>
    </a>
    <div class="text-center mt-2">
        <a href="' . $city['link'] . '">شاهد جميع الرحلات</a>
    </div>
</div>
';
}










function compareOrder($a, $b) {
    return $a->order - $b->order;
}
//---------------------------------------------------------------------------------------------
function GetHotelByParent($parent){
    global $AllHotels;
    $hotel = [];
    for ($i=0; $i < count($AllHotels) ; $i++) { 
        if($AllHotels[$i]['parent'] == $parent){
            array_push($hotel , $AllHotels[$i]);
        }
    }
    return $hotel;
}
//---------------------------------------------------------------------------------------------
function GetHotelParent($parent){
    global $HotelCity;
    for ($i=0; $i < count($HotelCity) ; $i++) { 
        if($HotelCity[$i]['id'] == $parent){
            return $HotelCity[$i];
        }
    }
    return ['name' => ''];
}
//---------------------------------------------------------------------------------------------
function GetHotelBox($hotel , $cls = ''){
    global $Site_URL , $default_image , $HotelUrl;

    $hotel_link = $Site_URL.'/'.$HotelUrl.'/'.$hotel->link;
    $photo = $default_image;
    if($hotel->photo != ''){
        $photo = $hotel->photo;
    }else{
        $ap = json_decode($hotel->photos);
        if(count($ap) > 0 && $ap[0] != '' ){
            $photo = $ap[0];
        }
    }
    return '
     
    <div class="col-md-3 col-sm-6 '.$cls.'"> 
        <a href="'.$hotel_link.'" class="blog_compact_part-container">
            <div class="blog_compact_part"> 
                <img src="'.$Site_URL.'/'.$photo.'" alt="'.$hotel->name.'" />
                <div class="blog_compact_part_content">              
                    <h3>'.$hotel->name.'</h3>
                    <ul class="blog_post_tag_part">
                        <li>'.GetHotelParent($hotel->parent)['name'].'</li>
                    </ul>
                    <div class="utf_star_rating_section" data-rating="'.$hotel->rate.'"></div>
                </div>
            </div>
        </a> 
    </div>
    ';
}
//---------------------------------------------------------------------------------------------
function GetOfferBox($offer , $cls = ''){
    global $Site_URL , $default_image , $UmrahUrl;
    $link = $Site_URL.'/'.$UmrahUrl.$offer['offer_id'];
    $photo = $default_image;
  
    $ap = json_decode($offer['photos']);
    if(count($ap) > 0 && $ap[0] != '' ){
        $photo = $ap[0];
    } 

    $hotel = json_decode($offer['hotelid']); 
    $k = '' ; $d = '';
    if(isset($hotel->k) && $hotel->k != 0){
        $k = '<a class="col-4 me-0" href="javascript:void(0)"><span class="fa fa-check"></span>إقامة مكة</a>';
    }

    if(isset($hotel->d) && $hotel->d != 0){
        $d = '<a class="col-6 me-0" href="javascript:void(0)"><span class="fa fa-bus-alt"></span>زيارة المدينة</a>';
    }
 
    $vip = $offer['vip'] ? '<div class="listing-st2"><i class="fa fal fa-bus-alt"></i>باص vip</div>' : '<div class="list-tag fz12"><i class="fa fal fa-bus-alt" style=" margin-left: 3px;font-size: 14px;color: #ff6000;"></i>باص اساسي</div>';
    $name = 'حملة عمرة '.$offer['name'].' '.$offer['from_to'].' بتاريخ '.date("Y-m-d" , $offer['dateCreated']);

    $price = '';
    $data = $offer['offer_price'] != null && $offer['offer_price'] != '' ? json_decode($offer['offer_price']) : [];
    for ($i = 0; $i < count($data); $i++) {
        $price .= '
        <span class="short_price">
            <div style="font-size: 15px; color: #000; font-weight: bold;">'.$data[$i]->descr.'</div>
            <div style="font-size: 12px; ">'.$data[$i]->name.'</div>

        </span>';
    }


    $madinahValue = $offer['madinah']; // Assuming madinah is part of the offer array

    // تحديد النص بناءً على قيمة madinah
    $madinahText = '';
    switch ($madinahValue) {
        case 0:
            $madinahText = 'مكة فقط';
            break;
        case 1:
            $madinahText = 'مكة وزيارة مدينة';
            break;
        case 2:
            $madinahText = 'إقامة مدينة';
            break;
        default:
            $madinahText = 'غير متوفر';
            break;
    }





    $days = json_decode($offer['days'], true); // استبدل هذا بالاستعلام الخاص بك لاسترجاع البيانات من قاعدة البيانات

    // تعريف مصفوفة بالأيام
    $daysOfWeek = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    
    // المتغير الذي سيحتوي على الأيام المتاحة
    $availableDays = '';
    
    // التحقق من قيمة الأيام
    if (is_null($days) || count($days) <= 0) {
        // إذا كانت القيمة null أو فارغة، فإن الرحلة متاحة يوميًا
        $availableDays = 'متاحة يوميًا';
    } else {
        // التأكد من أن التحويل إلى مصفوفة كان ناجحًا
        if (is_array($days)) {
            // إنشاء سلسلة تحتوي على الأيام المتاحة
            $availableDaysList = [];
            foreach ($days as $day) {
                // التأكد من أن اليوم هو رقم صحيح قبل إضافته إلى القائمة
                if (is_numeric($day) && $day >= 0 && $day <= 6) {
                    $availableDaysList[] = $daysOfWeek[$day];
                }
            }
            // إذا كانت القائمة فارغة، فهذا يعني أنه لا توجد أيام متاحة
            if (count($availableDaysList) > 0) {
                $availableDays = implode(', ', $availableDaysList);
            } else {
                $availableDays = 'لا توجد أيام متاحة';
            }
        } else {
            // إذا لم يكن التحويل ناجحًا، يمكنك التعامل مع الحالة هنا
            $availableDays = 'خطأ في قراءة الأيام';
        }
    }
    
   
    
  
    

    $html = '
    <div class=" '.$cls.'" type="offer" offer="'.$offer['offer_id'].'">
        <div class="listing-style1">
            <div class="list-thumb">
                <a href="'.$link.'">
                    <img class="w-100" src="'.$Site_URL.'/'.$photo.'" alt="'.$name.'">
                </a>
                 <div class="list-price">'.$madinahText.'</div>

                '.$vip.'
            </div>
            <div class="list-content">
                            <h3 class="list-title-trip"><a href="'.$link.'">'.$offer['title'].'</a></h3>
                            <p class="list-text">  '.$offer['rate'].' كيلو للحرم <span style="color: #429941;">باصات للحرم مجانا</span></p>
                <div class="list-meta row">
                
                    <a class="col-5 me-0"><span class="fa fa-map-marker-alt"></span>من '.$offer['from_to'].'</a>
                    <a class="col-7 me-0"> ' . htmlspecialchars($availableDays) . '</a>
<!--<a class="col-4 me-0" href="javascript:void(0)"style="color: #292726;font-size: 13px;padding: 0px;text-align: center;">
        <span class="icon fal fa-bus-alt"></span>
        <span style="color:' . ($offer['vip'] ? '#62aa61' : '#df7c33') . ';">' . ($offer['vip'] ? 'باص VIP' : 'باص أساسي') . '</span>
      </a>-->
      
                                    </div>

                <hr class="mt-2 mb-2">

<div class="row align-items-center">
    <div class="col-4 text-center">
        <div class="list-meta2">
                    '.$price.' 
        </div>
    </div>
    
    <div class="col-8 text-center">
         <div class="gobookin">
                           <a href="'.$link.'" class="gobookin-bt">'.tr('HOME_OFFER_03').' </a>

    </div>
    </div>
</div>

                               
            </div>
        </div>
    </div>

    
    '; 
    return $html;
}



//---------------------------------------------------------------------------------------------
//---------------------------------------------------------------------------------------------
function GetDarkColor($darkerFactor = 0.7){
    $originalColor = GetTableSet('theme_color');
    list($r, $g, $b) = sscanf($originalColor, "#%02x%02x%02x");
    $r = max(0, round($r * $darkerFactor));
    $g = max(0, round($g * $darkerFactor));
    $b = max(0, round($b * $darkerFactor));
    $darkerColor = sprintf("#%02x%02x%02x", $r, $g, $b);
    return $darkerColor;
}


