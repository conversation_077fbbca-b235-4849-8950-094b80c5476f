
<?php
$Title_page = 'تسجيل الدخول';

if(isset($_SESSION['OUserData'])){
  header("Location:".$Site_URL); exit();
}

include('header.php');
include('navbar.php');
$msg = '';
if(isset($_POST['submit'])){ 
  $password = trim($_POST['password']);
  $phone = trim($_POST['phone']);

  $check = getAllFrom('*' , 'users' , 'WHERE status = 1 AND phone = "'.$phone.'" AND password = "'.$password.'"', '');

  if(count($check) > 0){
    $_SESSION['OUserData'] = $check[0];
    header("Location:".$Site_URL); exit();
  }else{
    $msg = Show_Alert('danger' , 'رقم الجوال او كلمة المرور غير صحيحه');
  }
}

echo '
<div class="wrapper ovh">
    <div class="body_content">
      <!-- Our Compare Area -->
      <section class="our-compare pt60 pb60">
        <img src="images/icon/login-page-icon.svg" alt="" class="login-bg-icon wow fadeInLeft" data-wow-delay="300ms" style="visibility: visible; animation-delay: 300ms; animation-name: fadeInLeft;">
        <div class="container">
          <div class="row wow fadeInRight" data-wow-delay="300ms" style="visibility: visible; animation-delay: 300ms; animation-name: fadeInRight;">
            <div class="col-lg-6 offset-lg-3">
              <div class="log-reg-form signup-modal form-style1 bgc-white p50 p30-sm default-box-shadow2 bdrs12">
              <form method="post">
                <div class="text-center mb40">
                  <img class="mb25" src="images/header-logo2.svg" alt="">
                  <h2>تسجيل دخول</h2>
                  <p class="text">سجل دخول لمتابعه حجوزاتك </p>
                </div>
                <div class="mb25">
                  <label class="form-label fw600 dark-color">رقم الجوال</label>
                  <input type="text" name="phone" class="form-control" placeholder="رقم الجوال">
                </div>
                 
                <div class="mb15">
                  <label class="form-label fw600 dark-color">كلمة المرور</label>
                  <input type="text" name="password" class="form-control" placeholder="كملة المرور">
                </div>
                <div class="checkbox-style1 d-block d-sm-flex align-items-center justify-content-between mb10">
                  <label class="custom_checkbox fz14 ff-heading">تذكرني
                    <input type="checkbox" checked="checked">
                    <span class="checkmark"></span>
                  </label>
                  <a class="fz14 ff-heading" href="#">هل نسيت كلمة المرور؟</a>
                </div>
                <div class="d-grid mb20">
                  <button class="ud-btn btn-thm" name="submit" type="submit">تسجيل دخول <i class="fal fa-arrow-right-long"></i></button>
                </div>
                '.$msg.'
              </form>
                
                <p class="dark-color text-center mb0 mt10">هل لست مسجل ؟ <a class="dark-color fw600" href="page-register.html">أنشاء حساب الان</a></p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <a class="scrollToHome" href="#"><i class="fas fa-angle-up"></i></a>
    </div>
</div>
';

include('footer.php');



