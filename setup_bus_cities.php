<?php
include('webset.php');

echo "<h2>إعداد جدول الصفحات المخصصة للرحلات</h2>";

try {
    // قراءة ملف SQL
    $sql_content = file_get_contents('update_bus_cities.sql');
    
    if (!$sql_content) {
        throw new Exception('لا يمكن قراءة ملف SQL');
    }
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query)) {
            $db->exec($query);
        }
    }
    
    echo "<div style='color: green; font-weight: bold; margin: 20px 0;'>✅ تم إنشاء جدول bus_cities بنجاح!</div>";
    
    // عرض البيانات المدرجة
    $stmt = $db->query("SELECT id, name, link, title_s, trip_type, from_city, to_city, status FROM bus_cities ORDER BY trip_type, id");
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>الصفحات المخصصة المنشأة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>ID</th>";
    echo "<th style='padding: 10px;'>اسم الصفحة</th>";
    echo "<th style='padding: 10px;'>الرابط</th>";
    echo "<th style='padding: 10px;'>العنوان المختصر</th>";
    echo "<th style='padding: 10px;'>نوع الرحلة</th>";
    echo "<th style='padding: 10px;'>من</th>";
    echo "<th style='padding: 10px;'>إلى</th>";
    echo "<th style='padding: 10px;'>الحالة</th>";
    echo "<th style='padding: 10px;'>معاينة</th>";
    echo "</tr>";
    
    foreach ($pages as $page) {
        $trip_type_text = $page['trip_type'] == 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة';
        $status_text = $page['status'] == 1 ? '✅ نشط' : '❌ غير نشط';
        
        echo "<tr>";
        echo "<td style='padding: 8px; text-align: center;'>" . $page['id'] . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($page['name']) . "</td>";
        echo "<td style='padding: 8px;'><code>" . htmlspecialchars($page['link']) . "</code></td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($page['title_s']) . "</td>";
        echo "<td style='padding: 8px;'>" . $trip_type_text . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($page['from_city']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($page['to_city']) . "</td>";
        echo "<td style='padding: 8px;'>" . $status_text . "</td>";
        echo "<td style='padding: 8px;'><a href='bus-route.php?route=" . $page['link'] . "' target='_blank' style='color: #007bff;'>معاينة</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>📋 الخطوات التالية:</h4>";
    echo "<ol>";
    echo "<li>تم إنشاء جدول <code>bus_cities</code> مع البيانات التجريبية</li>";
    echo "<li>يمكنك الآن إنشاء ملف <code>bus-route.php</code> لعرض الصفحات المخصصة</li>";
    echo "<li>كل صفحة لها رابط مخصص مثل: <code>bus-route.php?route=riyadh-to-mecca</code></li>";
    echo "<li>الصفحات تحتوي على إعدادات مسبقة للمدن والمحطات</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='bus-route.php?route=riyadh-to-mecca' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار صفحة الرياض → مكة</a>";
    echo "<a href='bus-route.php?route=mecca-to-riyadh' style='display: inline-block; padding: 10px 20px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار صفحة مكة → الرياض</a>";
    echo "<a href='bus-booking.php' style='display: inline-block; padding: 10px 20px; background: #ff9500; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'>";
    echo "<h4>تفاصيل الخطأ:</h4>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>
