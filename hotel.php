<?php
// تحديد العنوان والوصف بناءً على القسم
if (isset($HotelCityData) && count($HotelCityData) > 0) {
    // إذا كان هناك قسم فرعي محدد - استخدام البيانات من قاعدة البيانات
    $city_name = $HotelCityData[0]['city_name'];
    $city_slug = $HotelCityData[0]['slug'];

    // استخدام البيانات من قاعدة البيانات
    $Title_page = !empty($HotelCityData[0]['title-s']) ? $HotelCityData[0]['title-s'] : $HotelCityData[0]['title'];
    $Title_pa = $HotelCityData[0]['title'];
    $Page_Description = strip_tags($HotelCityData[0]['descr']);
    $Page_KeyWords = $HotelCityData[0]['tags'];
    $Page_images = !empty($HotelCityData[0]['photo']) ? $Site_URL.'/'.$HotelCityData[0]['photo'] : $default_image;

    // تحديد الأيقونة واللون بناءً على المدينة
    if ($city_slug == 'makah') {
        $header_icon = 'fas fa-kaaba';
        $header_color = 'linear-gradient(135deg, #ff9b00 0%, #ff7b00 100%)';
        $distance_text = 'من الحرم';
    } else {
        $header_icon = 'fas fa-mosque';
        $header_color = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        $distance_text = 'من المسجد النبوي';
    }

    // الفنادق تم جلبها بالفعل في index.php
} else {
    // الصفحة العامة للفنادق
    $Title_page = 'الفنادق';
    $Title_pa = 'جميع الفنادق';
    $Page_Description = 'جميع الفنادق المتاحة في مكة المكرمة والمدينة المنورة';
    $Page_KeyWords = 'فنادق, مكة, المدينة, حجز فنادق';
    $Page_images = $default_image;
    $header_icon = 'fas fa-hotel';
    $header_color = 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)';
    $distance_text = 'المسافة';

    // جلب جميع الفنادق مع المدينة المرتبطة من جدول الحجز المباشر
    $AllHotel = getAllFrom('*', 'hotels_booking h JOIN hotel_city hc ON h.hotel_city_id = hc.id', 'WHERE h.status = 1', 'ORDER BY h.star_rating DESC, h.id DESC');
}

include('header.php');
include('navbar.php');

echo '
<section class="pt0 pb90 bgc-f7">
    <div class="container">
        <!-- Header Section -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex align-items-center mb-4 p-3 custom-background-box2"
                     style="background: '.$header_color.'; border-radius: 12px; color: white;">
                    <div class="me-3 icon-container">
                        <i class="'.$header_icon.'" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <div class="flex-grow-1 text-container">
                        <h1 class="mb-1" style="color: white; font-size: 1.8rem;">'.$Title_pa.'</h1>
                        <p class="mb-0" style="color: rgba(255,255,255,0.9);">'.$Page_Description.'</p>
                    </div>
                    <div class="ms-2">
                        <span class="badge bg-light text-dark px-3 py-2" style="font-size: 0.9rem;">
                            ' . count($AllHotel) . ' فندق متاح
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="filter-section bg-white p-3 rounded shadow-sm">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label class="form-label">ترتيب حسب:</label>
                            <select class="form-select" id="sortHotels">
                                <option value="rating">التقييم (الأعلى أولاً)</option>
                                <option value="distance">المسافة</option>
                                <option value="name">الاسم (أ-ي)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تصنيف النجوم:</label>
                            <select class="form-select" id="filterStars">
                                <option value="">جميع التصنيفات</option>
                                <option value="5">5 نجوم</option>
                                <option value="4">4 نجوم</option>
                                <option value="3">3 نجوم</option>
                                <option value="2">2 نجوم</option>
                                <option value="1">1 نجمة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">البحث:</label>
                            <input type="text" class="form-control" id="searchHotels" placeholder="ابحث عن فندق...">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hotels Grid -->
        <div class="row" id="hotelsContainer">';

if (count($AllHotel) > 0) {
    foreach ($AllHotel as $hotel) {
        $hotel_name = $hotel['name'];
        $hotel_address = $hotel['address'];
        $hotel_star = $hotel['star_rating'];
        $hotel_distance = $hotel['distance_to_haram'] ? $hotel['distance_to_haram'] . ' كم' : 'غير محدد';
        $hotel_description = !empty($hotel['description']) ? substr(strip_tags($hotel['description']), 0, 150) . '...' : 'لا يوجد وصف متاح';
        $city_name = $hotel['city_name'];

        // معالجة الصور
        $hotel_image = $default_image; // استخدام الصورة الافتراضية من الإعدادات
        if (!empty($hotel['photos'])) {
            $photos = json_decode($hotel['photos'], true);
            if (is_array($photos) && count($photos) > 0 && !empty($photos[0])) {
                $hotel_image = $photos[0];
            }
        }

        // إنشاء رابط الفندق
        $hotel_link = $hotel['link'];
        if (strpos($hotel_link, '/hotel/') === false) {
            $hotel_link = "/hotel/" . $hotel_link;
        }

        // إنشاء نجوم التقييم
        $stars_html = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $hotel_star) {
                $stars_html .= '<i class="fas fa-star text-warning"></i>';
            } else {
                $stars_html .= '<i class="far fa-star text-muted"></i>';
            }
        }

        // تحديد لون البطاقة بناءً على المدينة
        $card_color = ($city_name == 'مكة') ? 'primary' : 'success';

        echo '
        <div class="col-lg-4 col-md-6 mb-4 hotel-card" data-stars="'.$hotel_star.'" data-name="'.$hotel_name.'" data-city="'.$city_name.'">
            <div class="card h-100 shadow-sm border-0 hotel-item">
                <div class="position-relative">
                    <img src="'.$Site_URL.'/'.$hotel_image.'" class="card-img-top" alt="'.$hotel_name.'" style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-'.$card_color.'">'.$hotel_star.' نجوم</span>
                    </div>
                    <div class="position-absolute top-0 start-0 m-2">
                        <span class="badge bg-info">'.$city_name.'</span>
                    </div>
                    <div class="position-absolute bottom-0 start-0 m-2">
                        <span class="badge bg-dark">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            '.$hotel_distance.' '.$distance_text.'
                        </span>
                    </div>
                </div>

                <div class="card-body d-flex flex-column">
                    <h5 class="card-title text-'.$card_color.' mb-2">'.$hotel_name.'</h5>

                    <div class="mb-2">
                        '.$stars_html.'
                    </div>

                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt text-'.$card_color.' me-1"></i>
                        '.$hotel_address.'
                    </p>

                    <p class="card-text text-muted small flex-grow-1">'.$hotel_description.'</p>

                    <div class="mt-auto">
                        <a href="'.$Site_URL.$hotel_link.'" class="btn btn-'.$card_color.' w-100">
                            <i class="fas fa-eye me-2"></i>
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        </div>';
    }
} else {
    echo '
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-hotel fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد فنادق متاحة حالياً</h4>
            <p class="text-muted">يرجى المحاولة مرة أخرى لاحقاً</p>
        </div>
    </div>';
}

echo '
        </div>

        <!-- Pagination -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-3">
                    <p class="text-muted">عرض ' . count($AllHotel) . ' فندق من إجمالي الفنادق المتاحة</p>
                </div>
            </div>
        </div>
    </div>
</section>';

// عرض الوصف والكلمات المفتاحية إذا كان هناك قسم فرعي محدد
if (isset($HotelCityData) && count($HotelCityData) > 0 && !empty($HotelCityData[0]['descr'])) {
    $tags = '';
    if(!empty($Page_KeyWords)){
        $t = explode(',' , $Page_KeyWords);
        for ($i=0; $i < count($t) ; $i++) {
            if(trim($t[$i]) != '' ){
                $tags .= '<a href="'.$actual_link.'">'.trim($t[$i]).'</a>';
            }
        }
    }

    echo '
    <section class="pt0 pb10 pt30 bgc-f7">
        <div class="container">
            <div class="city-end">';

            if(!empty($HotelCityData[0]['photo'])){
                echo '
                <div class="col-12">
                    <div class="row">
                        <div class="col-md-6 mb30">
                            <img class="w100" src="'.$Site_URL.'/'.$HotelCityData[0]['photo'].'" alt="'.$HotelCityData[0]['title'].'">
                        </div>
                        <div class="col-md-6 mb30">
                            <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative">
                                <div class="autohigh">';
            }

            echo $HotelCityData[0]['descr'];

            if($tags != ''){
                echo '
                <div class="bsp_tags">
                    <hr>
                    <p>الكلمات الدلالية :</p>
                    '.$tags.'
                </div>';
            }

            if(!empty($HotelCityData[0]['photo'])){
                echo '
                                </div>
                                <a class="btn-tap" onclick="ShowMore(this)"> قراءة المزيد </a>
                            </div>
                        </div>
                    </div>
                </div>';
            } else {
                echo '
                <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative">
                    <div class="autohigh">
                        '.$HotelCityData[0]['descr'].'
                        '.(($tags != '') ? '<div class="bsp_tags"><hr><p>الكلمات الدلالية :</p>'.$tags.'</div>' : '').'
                    </div>
                    <a class="btn-tap" onclick="ShowMore(this)"> قراءة المزيد </a>
                </div>';
            }

        echo '
            </div>
        </div>
    </section>';
}

echo '

<script>
document.addEventListener("DOMContentLoaded", function() {
    const sortSelect = document.getElementById("sortHotels");
    const filterSelect = document.getElementById("filterStars");
    const searchInput = document.getElementById("searchHotels");
    const hotelsContainer = document.getElementById("hotelsContainer");

    function filterAndSortHotels() {
        const sortValue = sortSelect.value;
        const filterValue = filterSelect.value;
        const searchValue = searchInput.value.toLowerCase();

        const hotelCards = Array.from(document.querySelectorAll(".hotel-card"));

        // تصفية الفنادق
        hotelCards.forEach(card => {
            const stars = card.getAttribute("data-stars");
            const name = card.getAttribute("data-name").toLowerCase();

            let showCard = true;

            // تصفية حسب النجوم
            if (filterValue && stars !== filterValue) {
                showCard = false;
            }

            // تصفية حسب البحث
            if (searchValue && !name.includes(searchValue)) {
                showCard = false;
            }

            card.style.display = showCard ? "block" : "none";
        });

        // ترتيب الفنادق المرئية
        const visibleCards = hotelCards.filter(card => card.style.display !== "none");

        visibleCards.sort((a, b) => {
            switch(sortValue) {
                case "rating":
                    return parseInt(b.getAttribute("data-stars")) - parseInt(a.getAttribute("data-stars"));
                case "name":
                    return a.getAttribute("data-name").localeCompare(b.getAttribute("data-name"), "ar");
                default:
                    return 0;
            }
        });

        // إعادة ترتيب العناصر في DOM
        visibleCards.forEach(card => {
            hotelsContainer.appendChild(card);
        });

        // تحديث عداد النتائج
        const visibleCount = visibleCards.length;
        const totalCount = hotelCards.length;
        document.querySelector(".text-center p").textContent = `عرض ${visibleCount} فندق من إجمالي ${totalCount} فندق متاح`;
    }

    sortSelect.addEventListener("change", filterAndSortHotels);
    filterSelect.addEventListener("change", filterAndSortHotels);
    searchInput.addEventListener("input", filterAndSortHotels);
});
</script>';

include('footer.php');
?>
