<?php
include('webset.php');

header('Content-Type: application/json; charset=utf-8');

try {
    echo "Testing database connection...<br>";
    
    // اختبار الاتصال
    $stmt = $db->query("SELECT 1");
    echo "Database connection: OK<br>";
    
    // اختبار وجود الجدول
    $stmt = $db->query("SHOW TABLES LIKE 'bus_trips'");
    if ($stmt->rowCount() > 0) {
        echo "Table bus_trips: EXISTS<br>";
    } else {
        echo "Table bus_trips: NOT FOUND<br>";
    }
    
    // اختبار وجود العمود
    $stmt = $db->query("SHOW COLUMNS FROM bus_trips LIKE 'status'");
    if ($stmt->rowCount() > 0) {
        echo "Column status: EXISTS<br>";
    } else {
        echo "Column status: NOT FOUND<br>";
    }
    
    // اختبار البيانات
    $stmt = $db->query("SELECT COUNT(*) as total FROM bus_trips");
    $total = $stmt->fetchColumn();
    echo "Total trips: " . $total . "<br>";
    
    // اختبار الاستعلام المشكوك فيه
    $stmt = $db->prepare("SELECT DISTINCT from_city FROM bus_trips WHERE trip_type = 'to_mecca' AND status = 1 ORDER BY from_city");
    $stmt->execute();
    $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Cities found: " . count($cities) . "<br>";
    foreach ($cities as $city) {
        echo "- " . $city['from_city'] . "<br>";
    }
    
} catch (PDOException $e) {
    echo "PDO Error: " . $e->getMessage() . "<br>";
    echo "Error Code: " . $e->getCode() . "<br>";
} catch (Exception $e) {
    echo "General Error: " . $e->getMessage() . "<br>";
}
?>
