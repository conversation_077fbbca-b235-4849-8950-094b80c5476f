<script src="<?php echo $Site_URL;?>/layout/js/jquery-3.6.4.min.js"></script>
<script src="<?php echo $Site_URL;?>/layout/js/jquery-migrate-3.0.0.min.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/popper.min.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/bootstrap.min.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/bootstrap-select.min.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/jquery.mmenu.all.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/ace-responsive-menu.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/jquery-scrolltofixed-min.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/wow.min.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/owl.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/isotop.js"></script> 
<script src="<?php echo $Site_URL;?>/layout/js/parallax.js"></script>  
<script src="<?php echo $Site_URL;?>/layout/js/script.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
 




<?php
	if(!isset($daysJson)){
		$daysJson = '[]';
	}
?>


<script>





	var site_url = "<?php echo $Site_URL;?>";
	

	var days = JSON.parse('<?php echo $daysJson;?>');
	days = days.map(Number);
	if(days.length <= 0){
		days = [1,2,3,4,5,6,0];
	}
	let currentDate = new Date();
	let year = currentDate.getFullYear().toString();
	let month = ('0' + (currentDate.getMonth() + 1)).slice(-2);
	let day = ('0' + currentDate.getDate()).slice(-2);
	let formattedDate = year + '-' + month + '-' + day;
	var sd = localStorage.getItem("SavedDate");
	if(sd == null || sd == undefined){
		localStorage.setItem("SavedDate", formattedDate);
		sd = formattedDate;
	}
	$(function() {
        $.datepicker.regional['ar'] = {
            closeText: 'إغلاق',
            prevText: 'السابق',
            nextText: 'التالي',
            currentText: 'اليوم',
            monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            monthNamesShort: ['1', '2', '3', '4', '5', '6',
			'7', '8', '9', '10', '11', '12'],
            dayNames: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            dayNamesShort: ['أحد', 'اثن', 'ثلاث', 'أربع', 'خميس', 'جمع', 'سبت'],
            dayNamesMin: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],
            weekHeader: 'أسبوع',
            dateFormat: 'yy-mm-dd',
            firstDay: 0,
            isRTL: true, // تفعيل الاتجاه من اليمين لليسار
            showMonthAfterYear: false,
            yearSuffix: ''
        };
        $.datepicker.setDefaults($.datepicker.regional['ar']);
		  
        // تطبيق datepicker على العنصر
        $("#date_timepicker_end").datepicker({
            //timeFormat: 'HH:mm',
            dateFormat: 'yy-mm-dd',
            minDate: 0, // للسماح فقط بالتواريخ الحالية والمستقبلية
            changeMonth: true,
            changeYear: true,
			beforeShowDay: function (date) {
				var day = date.getDay();
				// السماح بالأيام الموجودة في مصفوفة days فقط
				if (days.includes(day)) {
					return [true]; // مسموح
				} else {
					return [false]; // غير مسموح
				}
			},
        });
    }); 
	//$('#date_timepicker_end').val(sd);
	function SearchCity(){
		var c = $('#selectpicker').val();
		var d = $('#date_timepicker_end').val();
		localStorage.setItem("SavedDate", d);
		if(c != '0'){
			window.location.href = site_url+'/'+c;
		}
		
	}
	function BookTrip(id,offer){
		$('#bt_book option').removeAttr('selected')
		$('#bt_book option[value='+offer+']').attr('selected','selected');
		$('#book-trip').modal('show'); 
	}
	function ConfirmBookTrip(id,offer){
		$('#book-trip').modal('hide'); 
		var bt_name = $('#bt_name').val();
		var bt_phone = $('#bt_phone').val();
		var bt_people = $('#bt_people').val();
		var date_timepicker_end = $('#date_timepicker_end').val();
		var bt_book = $('#bt_book').val();
		var bt_mvist = $('#bt_mvist').is(":checked");
		var title = $('#bt_book').find(":selected").text();
		var hamla = $('#bt_hamla').val();
		var selectedPayment = $('input[name="payment"]:checked').closest('label').text().trim();
		var room_title = $('#room_title').text();
		var return_date_display = $('#return_date_display').text();
		var total_price = $('#total_price').text();
		var discount_display = $('#discount_display').text();

		$('#bt_name').val(''); $('#bt_phone').val('');

		$.post(site_url+"/ajax.php", { do : "ConfirmBookTrip" , bt_name , bt_phone , bt_people , date_timepicker_end , bt_book , bt_mvist , title , hamla , id , selectedPayment , room_title , return_date_display , total_price , discount_display } , function(data){ 
			if(data == 'done'){
				window.location.href = site_url+'/invoice';
			}else{
				$('#res').html(data);
				$('#res-modal').modal('show'); 
			}
			
		}); 
	}
	function ShowMore(elm) {
		$(elm).hide(200);
		$('.autohigh').addClass('show')
	}
	function calcPrice(p){
		var co = $('#bt_people').val();
		$('#total_price').text( (co * p) + ' ريال'  );
		$('#co_total').text(co);
	}




	
if(document.getElementById('toggleFilters')){
document.getElementById('toggleFilters').addEventListener('click', function() {
    const filters = document.querySelector('.search-filters');
    if (filters.classList.contains('d-none')) {
        filters.classList.remove('d-none');
    } else {
        filters.classList.add('d-none');
    }
});
}

if(document.getElementById('closeFilters')){
// إضافة زر الإغلاق
document.getElementById('closeFilters').addEventListener('click', function() {
    const filters = document.querySelector('.search-filters');
    filters.classList.add('d-none'); // إغلاق الفلتر
});
}


function toggleFilters() {
    var filters = document.getElementById('filters');
    filters.style.display = (filters.style.display === 'none' || filters.style.display === '') ? 'block' : 'none';
}




if(document.getElementById('selectpicker')){

	document.getElementById('selectpicker').addEventListener('change', function() {
		var selectedCity = this.value;
		
		// إجراء استعلام AJAX لاسترداد مدة الرحلة بناءً على المدينة المحددة
		// قد تحتاج إلى ضبط عنوان URL وفقًا لتطبيقك
		fetch('get_durations.php?city=' + selectedCity)
			.then(response => response.json())
			.then(data => {
				var durationSelect = document.getElementById('durationSelect');
				durationSelect.innerHTML = ''; // مسح الخيارات القديمة

				// إضافة خيار افتراضي
				var defaultOption = document.createElement('option');
				defaultOption.value = '0';
				defaultOption.text = 'مدة الرحلة';
				durationSelect.appendChild(defaultOption);

				// إضافة خيارات جديدة
				data.forEach(function(duration) {
					var option = document.createElement('option');
					option.value = duration.val;
					option.text = duration.val;
					durationSelect.appendChild(option);
				});

				// إعادة تهيئة القائمة المنسدلة
				$('.selectpicker').selectpicker('refresh');
			})
			.catch(error => console.error('Error fetching durations:', error));
	});
}

function filterTrips(selectedDuration) {
    // هنا يمكنك استخدام AJAX أو أي طريقة أخرى لتحديث قائمة العروض
    // بناءً على المدة المحددة
    console.log("Selected Duration: " + selectedDuration);
    // مثال: قم بتحديث الصفحة أو أعد تحميل العروض هنا
}





function showInfo() {
        var infoBox = document.getElementById("infoBox");
        if (infoBox.style.display === "none") {
            infoBox.style.display = "block";
        } else {
            infoBox.style.display = "none";
        }
    }


	/*
	var site_url = "<?php echo $Site_URL;?>";
	$(document).ready(function () {
		var like = localStorage.getItem("like");
		if(like == null || like == undefined){
			like = JSON.stringify([]);
			localStorage.setItem("like", like);
		}
		var like_json = JSON.parse(like);
		var offers = document.querySelectorAll('div[type="offer"]');
		offers.forEach((offer, index) => {
			var id = $(offer).attr('offer');
			if(like_json.includes(id)){
				$(offer).find('.like-icon').addClass('liked');
			}else{
				$(offer).find('.like-icon').removeClass('liked');
			}
		});
	});
	function SelectTrip(){
		var trip = $('#trip').val();
		if(trip != ''){
			window.location.href = site_url+'/'+trip;
		} 
	}
	function AddPhone(e){
		var phone = $('#phone_num').val();
		if(phone.trim() == ''){
			$('#form-phone-status').text('من فضلك أدخل رقم الجوال.');
		}else if(phone.trim().length < 8){
			$('#form-phone-status').text('من فضلك أدخل رقم جوال صحيح.');
		}else{
			$('#form-phone-status').text('');
			$(e).parent().addClass('dis');
			$.post(site_url+"/ajax.php", { do : "AddPhone" , phone: phone.trim() } , function(data){ 
                $("#form-phone-status").text(data);
                $(e).parent().removeClass('dis');
            });
		}
	}
	function ShowMoreOffer(limit){
		var offers = document.querySelectorAll('.hidebox');
		for (let i = 0; i <= Math.min.apply(null, [offers.length - 1, limit-1]); i++) {
			$(offers[i]).removeClass('hidebox');
		}
		if(document.querySelectorAll('.hidebox').length <= 0){
			$('.showmorebox').addClass('dis');
		}
	}
	*/





	


	
</script>

 
<script>




$(document).ready(function() {
    $('#searchButton').click(function() {
        var duration = $('#duration').val(); // قيمة مدة الرحلة
        var bus_type = $('#bus_type').val(); // قيمة نوع الباص
        var trip_route = $('#trip_route').val(); // قيمة مسار الرحلة

        $.ajax({
            url: '', // استخدم نفس الصفحة لإرسال الطلب
            type: 'POST',
            data: {
                duration: duration,
                bus_type: bus_type,
                trip_route: trip_route // إرسال قيمة مسار الرحلة
            },
            success: function(response) {
                // عرض النتائج في الحاوية
                $('#resultsContainer').html(response);
                
                // النزول إلى قسم النتائج
                document.getElementById('resultsContainer').scrollIntoView({ behavior: 'smooth' });
            },
            error: function() {
                alert('حدث خطأ أثناء البحث. حاول مرة أخرى.');
            }
        });
    });
});

function clearFilters() {
	window.location.reload();
    // إعادة تعيين قيمة Duration إلى القيمة الافتراضية
    document.getElementById('duration').selectedIndex = 0; // القيمة الافتراضية

    // إعادة تعيين قيمة Bus Type إلى القيمة الافتراضية
    document.getElementById('bus_type').selectedIndex = 0; // القيمة الافتراضية

    // إعادة تعيين قيمة Trip Route إلى القيمة الافتراضية
    document.getElementById('trip_route').selectedIndex = 0; // القيمة الافتراضية
}





</script>

