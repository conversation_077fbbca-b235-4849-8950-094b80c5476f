<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ob_start();
$Title_page = 'الإعدادات' ;
include('../webset.php');
include('../session.php');  
include('header.php'); 
include('navbar.php');

function EXPORT_DATABASE($host,$user,$pass,$name,$tables=false, $backup_name=false)
{ 
	set_time_limit(3000); $mysqli = new mysqli($host,$user,$pass,$name); $mysqli->select_db($name); $mysqli->query("SET NAMES 'utf8'");
	$queryTables = $mysqli->query('SHOW TABLES'); while($row = $queryTables->fetch_row()) { $target_tables[] = $row[0]; }	if($tables !== false) { $target_tables = array_intersect( $target_tables, $tables); } 
	$content = "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\r\nSET time_zone = \"+00:00\";\r\n\r\n\r\n/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;\r\n/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;\r\n/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;\r\n/*!40101 SET NAMES utf8 */;\r\n--\r\n-- Database: `".$name."`\r\n--\r\n\r\n\r\n";
	foreach($target_tables as $table){
		if (empty($table)){ continue; } 
		$result	= $mysqli->query('SELECT * FROM `'.$table.'`');  	$fields_amount=$result->field_count;  $rows_num=$mysqli->affected_rows; 	$res = $mysqli->query('SHOW CREATE TABLE '.$table);	$TableMLine=$res->fetch_row(); 
		$content .= "\n\n".$TableMLine[1].";\n\n";   $TableMLine[1]=str_ireplace('CREATE TABLE `','CREATE TABLE IF NOT EXISTS `',$TableMLine[1]);
		for ($i = 0, $st_counter = 0; $i < $fields_amount;   $i++, $st_counter=0) {
			while($row = $result->fetch_row())	{ //when started (and every after 100 command cycle):
				if ($st_counter%100 == 0 || $st_counter == 0 )	{$content .= "\nINSERT INTO ".$table." VALUES";}
					$content .= "\n(";    for($j=0; $j<$fields_amount; $j++){ $row[$j] = str_replace("\n","\\n", addslashes($row[$j]) ); if (isset($row[$j])){$content .= '"'.$row[$j].'"' ;}  else{$content .= '""';}	   if ($j<($fields_amount-1)){$content.= ',';}   }        $content .=")";
				//every after 100 command cycle [or at last line] ....p.s. but should be inserted 1 cycle eariler
				if ( (($st_counter+1)%100==0 && $st_counter!=0) || $st_counter+1==$rows_num) {$content .= ";";} else {$content .= ",";}	$st_counter=$st_counter+1;
			}
		} $content .="\n\n\n";
	}
	$content .= "\r\n\r\n/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;\r\n/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;\r\n/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;";
	$backup_name = $backup_name ? $backup_name : $name.'___('.date('H-i-s').'_'.date('d-m-Y').').sql';
	ob_get_clean(); header('Content-Type: application/octet-stream');  header("Content-Transfer-Encoding: Binary");  header('Content-Length: '. (function_exists('mb_strlen') ? mb_strlen($content, '8bit'): strlen($content)) );    header("Content-disposition: attachment; filename=\"".$backup_name."\""); 
	echo $content; exit;
}

if (isset($_GET['action']) && $_GET['action'] == 'BackUp'){
EXPORT_DATABASE($Database_Host,$Database_Username,$Database_Password,$Database_Name,false,'Backup_'.date("Y-m-d").'.sql');
header('Location: settings.php');	exit();
}else{


	 
	if (isset($_POST['edit1'])){
		 
		Update('Site_Name' , $_POST['var1']);
		Update('Site_Title' , $_POST['var2']);
		Update('Site_URL' , $_POST['var3']);
		Update('Description' , $_POST['var4']);
		Update('Keywords' , $_POST['var5']);
		
		redirect_home ('back' , 0); exit();
	}	
	if (isset($_POST['edit2'])){

		Update('LockContent' , $_POST['var1']);
		Update('google-site-verification' , $_POST['var2']);
		Update('default_image' , $_POST['var3']);
		redirect_home ('back' , 0); exit();
	}	

	if (isset($_POST['edit3'])){

		Update('FooterDesc' , $_POST['var1']);
		Update('about' , $_POST['var2']);
		Update('privacy-policy' , $_POST['var3']);
		Update('terms-and-conditions' , $_POST['var4']); 
		redirect_home ('back' , 0); exit();
	}

	if (isset($_POST['edit4'])){

		Update('Facebook' , $_POST['var1']);
		Update('Twitter' , $_POST['var2']);
		Update('Instagram' , $_POST['var3']);
		Update('Youtube' , $_POST['var4']); 
		Update('Pinterest' , $_POST['var5']); 
		redirect_home ('back' , 0); exit();
	}

	if (isset($_POST['edit5'])){

		Update('AdminUser' , $_POST['var1']);
		Update('AdminPass' , $_POST['var2']); 
		redirect_home ('back' , 0); exit();
	}
 
?>		


<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
                <a style="color:#fff" href="settings.php?action=BackUp" class="btn btn-md btn-gradient-primary waves-effect waves-light">نسخه إحتياطيه من قاعدة البيانات</a>
			</div>
		</div><hr>
	</div>			
</div>

<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">

			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> الإعدادات العامه</h4><hr>
				<div class="row">
					<div class="col-md-6">
				<form method="post">
	                    <div class="form-group">
	                      <label class="form-control-label">إسم الموقع</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('Site_Name');?>" class="form-control">
	                    </div>
						<div class="form-group">
	                      <label class="form-control-label">تايتل الموقع</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('Site_Title');?>" class="form-control">
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">رابط الموقع</label>
	                      <input type="text" name="var3" value="<?php echo GetTableSet ('Site_URL');?>" class="form-control ltr">
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">الوصف</label>
	                      <textarea class="form-control" name="var4"><?php echo GetTableSet ('Description');?></textarea>
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">الكلمات الدلاليه</label>
	                      <textarea class="form-control" name="var5"><?php echo GetTableSet ('Keywords');?></textarea>
						</div>
						 				
	                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit1" class="btn btn-primary">
		                </div>
               	</form> 	
               	 	</div>	
               	 	<div class="col-md-6">
               	 		  <label class="form-control-label" style="text-align: center; width: 100%;">اللوجو  (44*138)</label>
							  <center>
							    <img src="<?php echo $Site_URL.'/'.GetTableSet ('Logo') ;?>" style="width: 200px;height: 60px; margin-bottom: 40px;margin-top: 20px; ">
							    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
							         
							          <input type="file" name="photo" id="photo" required style="display: none;" />
							          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
							          <input type="hidden" name="Image_For" value="LOGO">
							        </form>
							        <label for="photo" class="btn btn-info btn-sm" ><i class="fa fa-camera"></i> إختر الصوره</label>
							        <label for="Uploads" class="btn btn-info btn-sm" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
							  </center>
		 					<hr>
               	 		  <label class="form-control-label" style="text-align: center; width: 100%;">اللوجو  (44*138)</label>
							  <center>
							    <img src="<?php echo $Site_URL.'/'.GetTableSet ('Logo_Light') ;?>" style="width: 200px;height: 60px;background: #000; margin-bottom: 40px;margin-top: 20px; ">
							    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
							         
							          <input type="file" name="photo" id="photox" required style="display: none;" />
							          <input type="submit" style="display: none;" id="Uploadsx" value="Uploads" class="submit" />
							          <input type="hidden" name="Image_For" value="Logo_Light">
							        </form>
							        <label for="photox" class="btn btn-info btn-sm" ><i class="fa fa-camera"></i> إختر الصوره</label>
							        <label for="Uploadsx" class="btn btn-info btn-sm" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
							  </center>
							
               	 	</div>
				</div>	
			</div>	
</div></div></div></div>




<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات عامة</h4><hr>
				<form method="post">
					<div class="row">
					<div class="col-md-4">
					<div class="form-group">
						<label class="form-control-label">إغلاق المحتوي</label>
						<select name="var1" class="form-control">
							<option value="0" <?php if( GetTableSet ('LockContent') == 0){echo 'selected';} ?>>لا</option>
							<option value="1" <?php if( GetTableSet ('LockContent') == 1){echo 'selected';} ?>>نعم</option>
						</select>
						</div>
					</div>

				 
					<div class="col-md-4">
						<div class="form-group">
	                      <label class="form-control-label">كود تأكيد ملكية الموقع لجوجل</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('google-site-verification');?>" class="form-control ltr">
	                    </div>
					</div>	
					<div class="col-md-4">
						<div class="form-group">
	                      <label class="form-control-label">الصورة الافتراضية</label>
	                      <input type="text" name="var3" value="<?php echo GetTableSet ('default_image');?>" class="form-control ltr">
	                    </div>
					</div>
				  
					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit2" class="btn btn-primary">
		             </div>
		            </div>   
					</div>
				</form>
			</div>
</div></div></div></div>



<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات HTML</h4><hr>
				<form method="post">
					<div class="row">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">معلومات عنا فى الفوتر</label>
	                      <textarea class="form-control" name="var1"><?php echo GetTableSet ('FooterDesc');?></textarea>
	                    </div>
					</div>	
					 
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة معلومات عنا</label>
	                      <textarea class="form-control ltr" name="var2"><?php echo GetTableSet ('about');?></textarea>
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة الخصوصية</label>
	                      <textarea class="form-control ltr" name="var3"><?php echo GetTableSet ('privacy-policy');?></textarea>
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة الشروط والأحكام</label>
	                      <textarea class="form-control ltr" name="var4"><?php echo GetTableSet ('terms-and-conditions');?></textarea>
	                    </div>
					</div>	
					 
					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit3" class="btn btn-primary">
		             </div>
		            </div>   
					</div>
				</form>
			</div>
</div></div></div></div>


<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات التواصل الاجتماعي</h4><hr>
				<form method="post">
					<div class="row">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط الفيس بوك</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('Facebook');?>" class="form-control ltr">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط تويتر</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('Twitter');?>" class="form-control ltr">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط انستجرام</label>
	                      <input type="text" name="var3" value="<?php echo GetTableSet ('Instagram');?>" class="form-control ltr">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط اليوتيوب</label>
	                      <input type="text" name="var4" value="<?php echo GetTableSet ('Youtube');?>" class="form-control ltr">
	                    </div>
					</div> 
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رابط بينتريست</label>
	                      <input type="text" name="var5" value="<?php echo GetTableSet ('Pinterest');?>" class="form-control ltr">
	                    </div>
					</div>


					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit4" class="btn btn-primary">
		             </div>
		            </div>   
					</div>
				</form>
			</div>
</div></div></div></div>


<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات الإدارة</h4><hr>
				<form method="post">
					<div class="row">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رقم الجوال</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('AdminUser');?>" class="form-control ltr">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">كلمة المرور</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('AdminPass');?>" class="form-control ltr">
	                    </div>
					</div>
					 


					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit5" class="btn btn-primary">
		             </div>
		            </div>   
					</div>
				</form>
			</div>
</div></div></div></div>
 

<?php
}
include('footer.php'); 
ob_end_flush();
?>