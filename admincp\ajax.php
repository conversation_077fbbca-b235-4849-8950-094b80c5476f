<?php
ob_start();
include('../webset.php');
include('../session.php'); 

$AllCities = getAllFrom('*' , 'cities' , '', 'ORDER BY orders ASC , id ASC');
//-----------------------------------------------------------
if (isset($_POST['action']) && $_POST['action'] == 'GetNoti' ){
//----------------------------------------------------------- 
}elseif (isset($_POST['action']) && $_POST['action'] == 'Getcities'  ){
  
  for ($i=0; $i <= count($AllCities)-1 ; $i++) { 
 
      if ($AllCities[$i]['status'] == 1){
        $sh = '<a style="color:#fff" onclick="ShowHidePageAction('.$AllCities[$i]['id'].' , 0 )" class="btn btn-sm btn-gradient-success waves-effect waves-light mrb_10">المحافظة معروضة</a> ';
      }else{
        $sh = '<a style="color:#fff" onclick="ShowHidePageAction('.$AllCities[$i]['id'].' , 1 )" class="btn btn-sm btn-gradient-pink waves-effect waves-light mrb_10">المحافظة مخفيه</a> ';
      }
      echo '
      <div class="row sorts" id="page_'.$AllCities[$i]['id'].'">
        <div class="col-md-12 col-sm-12">
          <div class="card">
             
            <div class="card-body">
              <h4 class="cattitlel"> <i class="fa fa-sort"></i> '.$AllCities[$i]['name'].'</h4>
              <br>
              <a style="color:#fff" href="cities.php?action=edit&id='.$AllCities[$i]['id'].'" class="btn btn-sm btn-gradient-secondary waves-effect waves-light mrb_10">تعديل المحافظة</a> 
              '
              .$sh.
              '
              <a style="color:#fff" onclick="DelPageAction('.$AllCities[$i]['id'].')" class="btn btn-sm btn-gradient-danger waves-effect waves-light mrb_10">حذف المحافظة</a>
              <br>
            </div>
          </div>
        </div>
      </div>
      '; 
  }  
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'Sortcities' && isset($_POST['ids']) ){
  $ids  = trim($_POST['ids']) ;
  $d = explode(',' ,$ids);
  for ($i=0; $i <= count($d)-1 ; $i++) { 
      if(trim($d[$i]) != "" ){
        $stmt = $db->prepare("UPDATE cities SET orders = :var1 WHERE  id = :var0 ");  
        $stmt->execute(array( 'var1' => $i ,  'var0' => str_replace('page_' , '' , trim($d[$i])) )); 	
      }
  }
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'ShowHidePageAction' && isset($_POST['pageid']) ){
  $pageid  = trim($_POST['pageid']   ) ;
  $type  = trim($_POST['type']   ) ;
  $stmt = $db->prepare("UPDATE cities SET status = :var1 WHERE  id = :var0 ");  
  $stmt->execute(array( 'var1' => $type,  'var0' => $pageid )); 

//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'DelPageAction' && isset($_POST['pageid']) ){
  $pageid  = trim($_POST['pageid']   ) ;
  $stmt = $db -> prepare("DELETE FROM cities WHERE  id = :var0 ");
  $stmt->execute(array('var0' => $pageid ));   
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'AddNewCat' ){

  $stmt = $db->prepare("INSERT INTO cities ( name , link , title , descr , photo , tags , dateCreated , dateModified ) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6 ,:user_7 ,:user_8 )");  
  $stmt->execute(array(
      'user_1' => 'محافظة جديدة' ,
      'user_2' => 'محافظة-جديده-'.time() ,
      'user_3' => 'محافظة جديدة' ,
      'user_4' => 'محافظة جديدة' ,
      'user_5' => $default_image ,
      'user_6' => 'محافظة جديدة' ,
      'user_7' => time(),
      'user_8' => time()
  )) ;
  $pid = $db->lastInsertId();

  echo $pid;
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'SaveEdit' && isset($_POST['var0']) ){

  $content = $_POST['content'];
  $content = preg_replace('/(<[^>]+) style=".*?"/i', '$1', $content);
  $content = preg_replace('/(<[^>]+) dir=".*?"/i', '$1', $content);
  $content = preg_replace('/(<[^>]+) font=".*?"/i', '$1', $content);

  $stmt = $db->prepare("UPDATE cities SET name  = :var1 , title  = :var2 , link  = :var3 , descr  = :var4 , photo = :var5 , tags = :var6 , dateModified = :var7 WHERE  id = :var0 ");  
  $stmt->execute(array(
    'var1'  => $_POST['var1'] ,
    'var2'  => $_POST['var2'] ,
    'var3'  => str_replace(' ' , '-' , $_POST['var3']) ,
    'var4'  => $content ,
    'var5'  => $_POST['var4'] ,
    'var6'  => $_POST['var5'] ,
    'var7'  => time() ,
    'var0' => $_POST['var0'] 
  ));
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'hotels' ){
  $hotels = getAllFrom('*' , 'hotels' , '', 'ORDER BY id DESC'); 

	for ($x=0; $x <= count($hotels)-1 ; $x++) { 
	                    
        
        echo '<tr>
                <td class="padding30">'.($x+1).'</td>
                <td class="padding30">'.$hotels[$x]['name'].'</td>
                <td class="padding30">'.$hotels[$x]['rate'].' نجوم</td>
                  
                <td class="padding30">'.date("Y-m-d H:i" ,$hotels[$x]['dateCreated']).'</td>
                
                <td class="padding30"> <a style="color:#fff" href="hotels.php?action=edit&id='.$hotels[$x]['id'].'" class="btn btn-sm btn-gradient-secondary waves-effect waves-light"><i class="mdi mdi-tooltip-edit" aria-hidden="true"></i></a>   <a style="color:#fff" onclick="DelHotelAction('.$hotels[$x]['id'].')" class="btn btn-sm btn-danger smb"><i class="mdi mdi-delete-variant" aria-hidden="true"></i></a></td>
          	</tr>';
  }
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'AddNewHotel' ){

  $stmt = $db->prepare("INSERT INTO hotels ( name , info , photos , dateCreated , dateModified ) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 )");  
  $stmt->execute(array(
      'user_1' => 'فندق جديد' ,
      'user_2' => '[]' ,
      'user_3' => '["'.$default_image.'"]' ,
      'user_4' => time(),
      'user_5' => time()
  )) ;
  $pid = $db->lastInsertId();

  echo $pid;
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'DelHotelAction' && isset($_POST['id']) ){
  $pageid  = trim($_POST['id']   ) ;
  $stmt = $db -> prepare("DELETE FROM hotels WHERE  id = :var0 ");
  $stmt->execute(array('var0' => $pageid ));   
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'SaveEditHotel' && isset($_POST['var0']) ){

  $stmt = $db->prepare("UPDATE hotels SET name  = :var1 , rate  = :var2 , info  = :var3 , photos  = :var4 , dateModified = :var5 WHERE  id = :var0 ");  
  $stmt->execute(array(
    'var1'  => $_POST['var1'] ,
    'var2'  => $_POST['var2'] ,
    'var3'  => json_encode($_POST['var3'] , JSON_UNESCAPED_UNICODE) ,
    'var4'  => json_encode($_POST['var4'] , JSON_UNESCAPED_UNICODE) ,
    'var5'  => time() ,
    'var0' => $_POST['var0'] 
  ));

//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] ==  'Getoffers' ){
  $offers = getAllFrom('*' , 'offers' , '', 'ORDER BY offers.status DESC , offers.orders ASC , offers.id DESC'); 

	for ($i=0; $i <= count($offers)-1 ; $i++) { 
        $vip = $offers[$i]['vip'] == 1 ? 'VIP' : 'إقتصادية' ;
        if ($offers[$i]['status'] == 1){
          $sh = '<a style="color:#fff" onclick="ShowHideOfferAction('.$offers[$i]['id'].' , 0 )" class="btn btn-sm btn-gradient-success waves-effect waves-light mrb_10">الحملة معروضة</a> ';
        }else{
          $sh = '<a style="color:#fff" onclick="ShowHideOfferAction('.$offers[$i]['id'].' , 1 )" class="btn btn-sm btn-gradient-pink waves-effect waves-light mrb_10">الحملة مخفيه</a> ';
        }
        echo '
        <div class="row sorts" id="page_'.$offers[$i]['id'].'">
          <div class="col-md-12 col-sm-12">
            <div class="card">
              
              <div class="card-body">
                <h4 class="cattitlel"> <i class="fa fa-sort"></i> '.$offers[$i]['title'].'</h4>
                <p>من '.$offers[$i]['from_to'].' رحلة '.$vip.'</p>
                <br>
                <a style="color:#fff" href="offers.php?action=edit&id='.$offers[$i]['id'].'" class="btn btn-sm btn-gradient-secondary waves-effect waves-light mrb_10">تعديل الحملة</a> 
                '.$sh.'
                <a style="color:#fff" onclick="DelOfferAction('.$offers[$i]['id'].')" class="btn btn-sm btn-gradient-danger waves-effect waves-light mrb_10">حذف الحملة</a>
                <br>
              </div>
            </div>
          </div>
        </div>
        '; 
  }
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'AddNewOffer' ){

  $stmt = $db->prepare("INSERT INTO offers ( title , hotelid , duration , info , price , aprice , dateCreated , dateModified ) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6 ,:user_7 ,:user_8 )");  
  $stmt->execute(array(
      'user_1' => 'حملة جديدة' ,
      'user_2' => '{"k":0, "d":0}' ,
      'user_3' => '0' ,
      'user_4' => '[]' ,
      'user_5' => '[]' ,
      'user_6' => '[]' ,
      'user_7' => time(),
      'user_8' => time()
  )) ;
  $pid = $db->lastInsertId();

  echo $pid;
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'ShowHideOfferAction' && isset($_POST['pageid']) ){
  $pageid  = trim($_POST['pageid']   ) ;
  $type  = trim($_POST['type']   ) ;
  $stmt = $db->prepare("UPDATE offers SET status = :var1 WHERE  id = :var0 ");  
  $stmt->execute(array( 'var1' => $type,  'var0' => $pageid )); 	
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'DelOfferAction' && isset($_POST['pageid']) ){
  $pageid  = trim($_POST['pageid']   ) ;
  $stmt = $db -> prepare("DELETE FROM offers WHERE  id = :var0 ");
  $stmt->execute(array('var0' => $pageid ));   
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'Sortoffers' && isset($_POST['ids']) ){
  $ids  = trim($_POST['ids']) ;
  $d = explode(',' ,$ids);
  for ($i=0; $i <= count($d)-1 ; $i++) { 
      if(trim($d[$i]) != "" ){ 
        $stmt = $db->prepare("UPDATE offers SET orders = :var1 WHERE  id = :var0 ");  
        $stmt->execute(array( 'var1' => $i ,  'var0' => str_replace('page_' , '' , trim($d[$i])) )); 	
      }
  }
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'SaveOfferEdit' && isset($_POST['var0']) ){
  
  $formto = '';

  $cty = getAllFrom('*' , 'cities' , 'WHERE id = "'.$_POST['catid'].'"', ''); 
  if(count($cty) > 0){
    $formto = $cty[0]['name'];
  }


  $stmt = $db->prepare("UPDATE offers SET catid  = :var1 , title  = :var2 , hotelid  = :var3 , duration  = :var4 , info = :var5 , price = :var6 , aprice = :var7 , vip = :var8 , from_to = :var9 , dateModified = :var10 , days = :var11 WHERE  id = :var0 ");  
  $stmt->execute(array(
    'var1'  => $_POST['catid'] ,
    'var2'  => $_POST['title'] ,
    'var3'  => json_encode($_POST['hotelid'] , JSON_UNESCAPED_UNICODE) ,
    'var4'  => $_POST['duration'] ,
    'var5'  => json_encode($_POST['info'] , JSON_UNESCAPED_UNICODE) ,
    'var6'  => json_encode($_POST['price'] , JSON_UNESCAPED_UNICODE) ,
    'var7'  => json_encode($_POST['aprice'] , JSON_UNESCAPED_UNICODE) ,
    'var8'  => $_POST['vip'] ,
    'var9'  => $formto ,
    'var10'  => time() ,
    'var11'  => json_encode(isset($_POST['days']) && $_POST['days'] !=null ? $_POST['days'] : [] , JSON_UNESCAPED_UNICODE) ,
    'var0' => $_POST['var0'] 
  ));
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'orders' ){
  $orders = getAllFrom('*' , 'orders' , '', 'ORDER BY id DESC'); 

	for ($x=0; $x <= count($orders)-1 ; $x++) { 

      $madina = $orders[$x]['madina'] == 'true' ? 'نعم' : 'لا';
        
        echo '<tr>
                <td class="padding30">'.($x+1).'</td>
                <td class="padding30">'.$orders[$x]['name'].'</td>
                <td class="padding30">'.$orders[$x]['phone'].'</td>
                <td class="padding30">'.$orders[$x]['hamla_title'].'</td>
                <td class="padding30">'.$orders[$x]['room_title'].'</td>
                <td class="padding30">'.$orders[$x]['datee'].'</td>
                <td class="padding30">'.$orders[$x]['total_price'].'</td>
                  
                
                
                <td class="padding30"> <a style="color:#fff" onclick="DelOrderAction('.$orders[$x]['id'].')" class="btn btn-sm btn-danger smb"><i class="mdi mdi-delete-variant" aria-hidden="true"></i></a></td>
          	</tr>';
  }
//-----------------------------------------------------------   
}elseif (isset($_POST['action']) && $_POST['action'] == 'DelOrderAction' && isset($_POST['id']) ){
  $id  = trim($_POST['id']   ) ;
  $stmt = $db -> prepare("DELETE FROM orders WHERE  id = :var0 ");
  $stmt->execute(array('var0' => $id ));   
//-----------------------------------------------------------   
}