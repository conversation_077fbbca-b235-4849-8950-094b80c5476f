<?php
include('webset.php');

echo "<h2>فحص العناوين التفصيلية في قاعدة البيانات</h2>";

try {
    // فحص بنية الجدول
    echo "<h3>1. فحص بنية الجدول:</h3>";
    $stmt = $db->query("DESCRIBE bus_trips");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص البيانات
    echo "<h3>2. فحص البيانات مع العناوين:</h3>";
    $stmt = $db->query("SELECT id, from_city, from_station, departure_address, to_city, to_station, arrival_address, departure_location FROM bus_trips LIMIT 5");
    $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($trips) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>من المدينة</th>";
        echo "<th>من المحطة</th>";
        echo "<th>عنوان الانطلاق</th>";
        echo "<th>إلى المدينة</th>";
        echo "<th>إلى المحطة</th>";
        echo "<th>عنوان الوصول</th>";
        echo "<th>موقع الانطلاق</th>";
        echo "</tr>";
        
        foreach ($trips as $trip) {
            echo "<tr>";
            echo "<td>" . $trip['id'] . "</td>";
            echo "<td>" . $trip['from_city'] . "</td>";
            echo "<td>" . $trip['from_station'] . "</td>";
            echo "<td style='max-width: 200px; word-wrap: break-word;'>" . ($trip['departure_address'] ?: 'غير محدد') . "</td>";
            echo "<td>" . $trip['to_city'] . "</td>";
            echo "<td>" . $trip['to_station'] . "</td>";
            echo "<td style='max-width: 200px; word-wrap: break-word;'>" . ($trip['arrival_address'] ?: 'غير محدد') . "</td>";
            echo "<td>" . ($trip['departure_location'] ? 'متوفر' : 'غير متوفر') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد بيانات في الجدول</p>";
    }
    
    // إحصائيات
    echo "<h3>3. إحصائيات:</h3>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM bus_trips");
    $total = $stmt->fetchColumn();
    echo "إجمالي الرحلات: " . $total . "<br>";
    
    $stmt = $db->query("SELECT COUNT(*) as with_departure_address FROM bus_trips WHERE departure_address IS NOT NULL AND departure_address != ''");
    $with_departure = $stmt->fetchColumn();
    echo "الرحلات مع عنوان الانطلاق: " . $with_departure . "<br>";
    
    $stmt = $db->query("SELECT COUNT(*) as with_arrival_address FROM bus_trips WHERE arrival_address IS NOT NULL AND arrival_address != ''");
    $with_arrival = $stmt->fetchColumn();
    echo "الرحلات مع عنوان الوصول: " . $with_arrival . "<br>";
    
    $stmt = $db->query("SELECT COUNT(*) as with_location FROM bus_trips WHERE departure_location IS NOT NULL AND departure_location != ''");
    $with_location = $stmt->fetchColumn();
    echo "الرحلات مع موقع الانطلاق: " . $with_location . "<br>";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>

<br><br>
<a href="bus-booking.php" style="display: inline-block; padding: 10px 20px; background: #ff9500; color: white; text-decoration: none; border-radius: 5px;">اختبار صفحة الحجز</a>
<a href="test_booking.php" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;">اختبار تفاعلي</a>
