-- إنشاء جدول رحلات الباصات
CREATE TABLE IF NOT EXISTS bus_trips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    from_city VARCHAR(100) NOT NULL,
    from_station VARCHAR(200) NOT NULL,
    to_city VARCHAR(100) NOT NULL,
    to_station VARCHAR(200) NOT NULL,
    departure_time TIME NOT NULL,
    arrival_time TIME NOT NULL,
    seat_price DECIMAL(10,2) NOT NULL,
    total_seats INT NOT NULL DEFAULT 50,
    trip_type ENUM('to_mecca', 'from_mecca') NOT NULL,
    available_days TEXT DEFAULT '0',
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إ<PERSON><PERSON><PERSON><PERSON> جدول حجوزات الباصات
CREATE TABLE IF NOT EXISTS bus_bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trip_id INT NOT NULL,
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    id_number VARCHAR(20) NOT NULL,
    nationality VARCHAR(50) NOT NULL,
    travel_date DATE NOT NULL,
    seats_count INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    notes TEXT,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    booking_reference VARCHAR(20) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_id) REFERENCES bus_trips(id)
);

-- إدراج بيانات تجريبية للرحلات
INSERT INTO bus_trips (from_city, from_station, to_city, to_station, departure_time, arrival_time, seat_price, total_seats, trip_type, available_days, status) VALUES 
('الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '13:30:00', '17:30:00', 100.00, 50, 'to_mecca', '0', 1),
('الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '20:00:00', '00:00:00', 100.00, 50, 'to_mecca', '0', 1),
('الرياض', 'محطة الملك فهد', 'مكة المكرمة', 'شارع ابراهيم الخليل', '15:00:00', '19:00:00', 120.00, 45, 'to_mecca', '0', 1),
('جدة', 'محطة جدة المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '14:00:00', '15:30:00', 50.00, 45, 'to_mecca', '0', 1),
('جدة', 'محطة الحرمين', 'مكة المكرمة', 'شارع ابراهيم الخليل', '16:00:00', '17:30:00', 50.00, 45, 'to_mecca', '0', 1),
('الدمام', 'محطة الدمام المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '10:00:00', '16:00:00', 150.00, 40, 'to_mecca', '["1","3","5"]', 1),
('الطائف', 'محطة الطائف', 'مكة المكرمة', 'شارع ابراهيم الخليل', '12:00:00', '13:30:00', 40.00, 35, 'to_mecca', '0', 1),
('المدينة المنورة', 'محطة المدينة', 'مكة المكرمة', 'شارع ابراهيم الخليل', '11:00:00', '15:00:00', 80.00, 50, 'to_mecca', '0', 1),

-- رحلات العودة من مكة
('مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'الرياض', 'محطة البطحاء', '13:30:00', '17:30:00', 100.00, 50, 'from_mecca', '0', 1),
('مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'الرياض', 'محطة البطحاء', '21:00:00', '01:00:00', 100.00, 50, 'from_mecca', '0', 1),
('مكة المكرمة', 'شارع ابراهيم الخليل', 'الرياض', 'محطة الملك فهد', '14:00:00', '18:00:00', 120.00, 45, 'from_mecca', '0', 1),
('مكة المكرمة', 'شارع ابراهيم الخليل', 'جدة', 'محطة جدة المركزية', '15:00:00', '16:30:00', 50.00, 45, 'from_mecca', '0', 1),
('مكة المكرمة', 'شارع ابراهيم الخليل', 'جدة', 'محطة الحرمين', '17:00:00', '18:30:00', 50.00, 45, 'from_mecca', '0', 1),
('مكة المكرمة', 'شارع ابراهيم الخليل', 'الدمام', 'محطة الدمام المركزية', '16:00:00', '22:00:00', 150.00, 40, 'from_mecca', '["1","3","5"]', 1),
('مكة المكرمة', 'شارع ابراهيم الخليل', 'الطائف', 'محطة الطائف', '18:00:00', '19:30:00', 40.00, 35, 'from_mecca', '0', 1),
('مكة المكرمة', 'شارع ابراهيم الخليل', 'المدينة المنورة', 'محطة المدينة', '16:00:00', '20:00:00', 80.00, 50, 'from_mecca', '0', 1);
