<?php
include('webset.php');

echo "<h2>تحديث حقل ساعة المغادرة ليصبح قابل للكتابة يدوياً</h2>";

try {
    // قراءة ملف SQL
    $sql_content = file_get_contents('update_departure_time_field.sql');
    
    if (!$sql_content) {
        throw new Exception('لا يمكن قراءة ملف SQL');
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>سيتم تنفيذ التحديثات التالية:</h4>";
    echo "<ul>";
    echo "<li>إضافة حقل جديد departure_time_text لحفظ الوقت كنص</li>";
    echo "<li>نسخ البيانات الحالية وتحويلها إلى نص عربي (مثل: 1:30 ظهراً)</li>";
    echo "<li>حذف الحقل القديم departure_time</li>";
    echo "<li>إعادة تسمية الحقل الجديد ليصبح departure_time</li>";
    echo "<li>إضافة حقل مساعد للترتيب departure_sort_time</li>";
    echo "</ul>";
    echo "</div>";
    
    // عرض البيانات الحالية قبل التحديث
    echo "<h3>البيانات الحالية قبل التحديث:</h3>";
    $stmt = $db->query("SELECT id, from_city, to_city, departure_time, seat_price FROM bus_trips LIMIT 10");
    $current_trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($current_trips) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>من المدينة</th>";
        echo "<th style='padding: 8px;'>إلى المدينة</th>";
        echo "<th style='padding: 8px;'>وقت المغادرة الحالي</th>";
        echo "<th style='padding: 8px;'>السعر</th>";
        echo "</tr>";
        
        foreach ($current_trips as $trip) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $trip['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . $trip['from_city'] . "</td>";
            echo "<td style='padding: 8px;'>" . $trip['to_city'] . "</td>";
            echo "<td style='padding: 8px; font-weight: bold; color: #ff9500;'>" . $trip['departure_time'] . "</td>";
            echo "<td style='padding: 8px;'>" . $trip['seat_price'] . " ريال</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // تأكيد التنفيذ
    if (isset($_GET['confirm']) && $_GET['confirm'] == 'yes') {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>جاري تنفيذ التحديث...</h4>";
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql_content);
        $executed_queries = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                try {
                    $db->exec($query);
                    $executed_queries++;
                    echo "<p style='color: green;'>✅ تم تنفيذ الاستعلام رقم $executed_queries</p>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ خطأ في الاستعلام رقم $executed_queries: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "</div>";
        
        echo "<div style='color: green; font-weight: bold; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "✅ تم تحديث حقل ساعة المغادرة بنجاح!";
        echo "</div>";
        
        // عرض البيانات بعد التحديث
        echo "<h3>البيانات بعد التحديث:</h3>";
        $stmt = $db->query("SELECT id, from_city, to_city, departure_time, departure_sort_time, seat_price FROM bus_trips LIMIT 10");
        $updated_trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($updated_trips) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>من المدينة</th>";
            echo "<th style='padding: 8px;'>إلى المدينة</th>";
            echo "<th style='padding: 8px;'>وقت المغادرة الجديد</th>";
            echo "<th style='padding: 8px;'>وقت الترتيب</th>";
            echo "<th style='padding: 8px;'>السعر</th>";
            echo "</tr>";
            
            foreach ($updated_trips as $trip) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $trip['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['from_city'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['to_city'] . "</td>";
                echo "<td style='padding: 8px; font-weight: bold; color: #28a745;'>" . $trip['departure_time'] . "</td>";
                echo "<td style='padding: 8px; color: #666;'>" . $trip['departure_sort_time'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['seat_price'] . " ريال</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>الآن يمكنك:</h4>";
        echo "<ul>";
        echo "<li>تحرير أوقات المغادرة مباشرة في قاعدة البيانات كنص عربي</li>";
        echo "<li>مثال: يمكنك كتابة '1.5 ظهراً' أو '2:30 عصراً' أو أي نص تريده</li>";
        echo "<li>سيتم عرض النص كما هو في كارت الرحلة</li>";
        echo "<li>حقل departure_sort_time يساعد في ترتيب الرحلات حسب الوقت</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        // عرض زر التأكيد
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ تحذير مهم:</h4>";
        echo "<p>هذا التحديث سيغير هيكل قاعدة البيانات. تأكد من عمل نسخة احتياطية قبل المتابعة.</p>";
        echo "<a href='?confirm=yes' style='background: #ff9500; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>تأكيد التحديث</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>
