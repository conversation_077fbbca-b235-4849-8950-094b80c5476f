<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>
    Header set Access-Control-Allow-Origin *
    RewriteEngine On 
    
    
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP:X-Forwarded-Proto} !https
    RewriteCond %{HTTP:CF-Visitor} !https
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    RewriteCond %{HTTP_HOST} ^www\.([^.]+\.xn----3mcoehg2joag\.com)$ [NC]
    RewriteRule ^ http://%1%{REQUEST_URI} [R=301,L]
    # Redirect Trailing Slashes If Not A Folder...
    #RewriteCond %{REQUEST_URI} !(/$|\.) 
    #RewriteRule (.*) %{REQUEST_URI}/ [R=301,L] 
  
  
  	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule ^(.*)/$ /$1 [L,R] # <- for test, for prod use [L,R=301]

    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    #RewriteRule ^ index.php [L]
    RewriteRule .* index.php?path=$0
    
    



    AddType 'text/css; charset=UTF-8' css
</IfModule>
