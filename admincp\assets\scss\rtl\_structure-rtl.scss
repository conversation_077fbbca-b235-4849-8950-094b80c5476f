/*
Template Name: Dealzon - Admin & Dashboard Template
Author: <PERSON><PERSON><PERSON><PERSON>
Email: <EMAIL>
File: RTL Stucture
*/


.left-sidenav-menu {
    
    li {
        > a {
           
            i {
                margin-right: 0;
                margin-left: 5px;
                &.ti-control-record{
                    margin-right: auto;
                    margin-left: 0;
                }
            }
        }

        ul {
            padding: 0 25px 0 0;

            li {
                > a {
                    border-right: none;
                   
                }
            }
        }
        
    }
    
    .menu-arrow {
        float: left;
        i:before{
            content: "\F141";
            font: "Material Design Icons";
        }
    }
    li.mm-active .menu-arrow.left-has-menu {
        i:before{
            content: "\F141";
            font: "Material Design Icons";
        }
    }
    .vertical-menu-icon{
        margin-right: auto;
        margin-left: 6px;
    }
}



.page-content-tab{
    margin-left: 0;
    margin-right: auto;
}

//  Rightbar Modal

.modal{
    &.modal-rightbar{
      right: auto;
      left: 0;      
    }
    &.modal-rightbar{
      .modal-dialog {
        width: 320px;
        right: auto;
        left: -320px;
        margin: 0 auto 0 0;
      }
      &.show .modal-dialog {
        right: auto;
        left: 0;  
        transition: left 0.3s ease, right 0.3s ease;  
      }
    }
  }
  

  .left-sidenav {
    .leftbar-profile {
        .leftbar-user:after {
            left: 0;
            right: 40px;
        }
    }
  }

//
// Topbar
//


.topbar {
    .topbar-left {
        float: right;
        .logo {
            line-height: $topbar-height;
            .logo-lg {
                margin-left: 0;
                margin-right: 2px;
            }  
        }
    }
}
  
.navbar-custom {
    margin-left:auto;
    margin-right: $leftbar-width;
    box-shadow: -6px 0px 6px rgba(31, 30, 47, 0.06);
    .topbar-nav {
        li {
            float: right;         
        }
    }
}
  


  
  /* Notification */
  
.notification-list {
    margin-left: 0;   
    .noti-icon-badge {  
        right: auto;
        left: 8px;
    }
}
.topbar-nav{
    li:nth-last-of-type(2){
        &.dropdown{
            .dropdown-menu{
                a.dropdown-item{
                    i{
                        float: right;
                    }
                }                
            }
        }
    }
}
  // Search
  
.app-search .form-control, 
.app-search .form-control:focus {
    padding-left: 40px;
    padding-right: 20px;
}
  
.app-search {
    margin-left: auto;
    margin-right: 20px;
}
  
.app-search a {
    right: auto;
    left: 0
}

// Horizontal

[data-layout="horizontal"] {
    .navbar-custom{
        margin-left: 0;
        margin-right: 160px;
        .topbar-nav{
            li:nth-last-of-type(3){
                &.dropdown{
                    .dropdown-menu{
                        a.dropdown-item{
                            i{
                                float: right;
                            }
                        }                
                    }
                }
            }
        }
    }
    .topbar {
        .topbar-left{
            width: 160px;
        }
    }
}

[data-layout="horizontal-tab"] {

    .dual-btn-icon i {
        margin: 0 14px 0 -4px;
    }

   

    &.enlarge-menu.enlarge-menu-all .topbar .navbar-custom {
        margin-left: auto;
        margin-right: 40px;
    }

    .navigation-menu>li:first-child a {
        padding-right: 0;
        padding-left: 20px;
    }
    
    .navbar-custom-tab-menu {
        .main-nav-item{
            &.active {
                .submenu-tab{
                    &.show {
                        .has-submenu{
                            &:hover {
                                .submenu {
                                    left: auto;
                                    right: auto;
                                }
                            }
                        }
                    }
                    .submenu {
                        text-align: right;
                    }
                }
            }
        }
    }

    @media (min-width: 992px){
        .navbar-custom-tab-menu {
            .navigation-menu{
                >li {
                    .submenu-tab{
                        >li {
                            .submenu {
                                li {
                                    a:before {
                                        left: auto;
                                        right: 10px;
                                    }
                                }
                            }
                            &.has-submenu{
                                >a:after {
                                    content: "\e64a";
                                    font-family: 'themify';
                                    margin: -2px 0 0 12px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 991px){
        .navbar-custom-tab-menu {
            .navigation-menu {
                text-align: right;
                >li {
                    .submenu-tab {
                        li{
                            &.has-submenu{
                                >a:after {
                                    left: 30px;
                                    right: auto;
                                }
                            }
                        }
                    }
                }
            }
        }

        .navigation-menu>li:first-child a{
            padding-right: 20px;
            padding-left: 0;
        }
    }



    .navigation-menu>li {
        .submenu-tab{
            >li{
                &.has-submenu{
                    >a:after {
                        left: -8px;
                        right: auto;
                    }
                }
            }
        }
    }
    
    @media (min-width: 768px){
        .navbar-custom-menu .navigation-menu>li.has-submenu:hover>.submenu{
            visibility: hidden;
            opacity: 0;
        } 
    }
}

.navbar-custom-menu {     
    .has-submenu.active {          
        .submenu {
            li.active>a {
                padding-left: 0;
                padding-right: 35px;                  
                &::before{
                    left: auto;
                    right: 14px;
                    transform: rotate(180deg);
                }
            }
        }
    }
}
  
.navigation-menu {
    >li {      
      a {
            i {
                margin-right: 0;
                margin-left: 8px;
            }                  
        }
    }
}

.tooltip.tooltip-custom.bs-tooltip-left .arrow::before, 
  .tooltip.tooltip-sky.bs-tooltip-left .arrow::before {
    border-left-color: $tooltip-custom;
  }

// leftbar-tab

.leftbar-tab-menu {
    .main-icon-menu{
        .nav-link.active:before {
            content: '';
            position: absolute;
            left: auto;
            right: 45px;           
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 10px solid $card-bg;
            border-right: 0px solid transparent;            
        }
    }
}
  
  .main-menu-inner {
    left: auto;
    right: 70px;
    .topbar-left {
        padding-left: 0;    
        padding-right: 20px;    
    }
    .menu-body{
        .nav-item {
            ul {
                li {
                    
                    a:before{
                        content: "";
                        position: absolute;
                        left: auto;
                        right: 24px;
                    } 
                }
                
            }           
        }
        .nav-link {
            i {
                margin-right: auto;   
                margin-left: 10px;        
            }
           
            .menu-arrow {
                i {
                    float: left;
                }
            }            
            
        } 
        
    }
  }
  
  
  // Enlarge menu
  .enlarge-menu {
    
    .topbar {
        .navbar-custom{
            margin-left: auto;
            margin-right: $main-icon-menu-width;
        }        
        .topbar-left{
            margin-left: auto;
            margin-right: 0;
        }
    }
    &.enlarge-menu-all {
       
        .topbar {
            .topbar-left{
                margin-left: auto;
                margin-right: 0;
            }
            .navbar-custom{
                margin-left: auto;
                margin-right: 0;
            } 
        }
    }
  }
  
  
  
  @media (max-width: 1024px) {
    
    .navbar-custom {
      margin-left: $leftbar-width-collapsed;
      .responsive-logo{
        padding-left: 0;
        padding-right: 10px;
      }
    }
    
    .enlarge-menu {
        .topbar {
            .topbar-left{
                margin-left: auto;
                margin-right: 0;
            }
        }
    }
    .enlarge-menu-all{
        .topbar {            
            .navbar-custom{
               margin-left: auto;
                margin-right: 0;
            } 
        }
    }
  }
  
@media (min-width: 992px) {
    
    .main-menu-inner{
        .menu-body{
            .nav-link.with-sub::after {
                margin-left: auto;
                margin-right: 4px;
            }           
        }
    }
    
}
  



@media (max-width: 1024px){
    .topbar .navbar-custom {
        margin-right: 70px;
        margin-left: 0;
    }
}

@media (min-width: 992px) {
    
    .navbar-custom-menu {
        .navigation-menu{
            >li{                
                &.last-elements {
                    .submenu {
                        right: auto;
                        left: 0;
                        >li.has-submenu {
                            .submenu {
                                right: auto; //right: auto; if you want submenu right
                                left: 100%; // left: 100%; if you want submenu right
                                margin-right: 0;
                                margin-left: 10px;
                                
                            }
                        }
                    } 
                }
                .submenu {
                    left: auto;
                    right: 0;
                    text-align: right;                    
                    >li{
                        &.has-submenu>a:after {
                            right: auto;
                            left: 20px;
                            transform: rotate(180deg);
                        }
                        .submenu {
                            right: 100%;
                            left: auto;                           
                        }
                    }
                    
                    li {
                        ul {
                            padding-right: 0;
                        }                        
                    }
                }                
            }
        }
    }     
}


@media (max-width: 991px) {
   
    .topbar {
        .navigation-menu {
            text-align: right;
            >li {
                >a {
                    &:after {
                        position: absolute;
                        right: auto;
                        left: 15px;
                    }
                }
                .submenu {
                    padding-right: 20px;
                    padding-left: 0;
                    li {                        
                        &.has-submenu>a:after {
                            right: auto;
                            left: 30px;
                        }
                    }
                    
                    &.megamenu>li>ul {
                        padding-right: 0;                        
                    }
                }
            }
        }
        .navbar-header {
            float: right;
        }
    }    
}


@media (min-width: 768px) {
   
    .topbar {
        .navigation-menu{
            >li{
                &.has-submenu:hover{
                    >.submenu {
                        >li{
                            &.has-submenu:hover{
                                >.submenu {
                                    margin-left: 0;
                                    margin-right: auto;
                                }   
                            }
                        }
                    }
                }                
            }
        }
    } 
}

@media (max-width: 768px){
    [data-layout="horizontal"] {        
        .topbar {
            .topbar-left{
                width: 70px;
            }
        }
    }
    
    .topbar{
        .topbar-left{
            margin-left: 0;
            margin-right: auto;           
        }       
    }
}



