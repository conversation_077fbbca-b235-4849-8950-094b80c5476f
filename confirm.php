<?php

$Title_page = 'تأكيد الحجز';
include('header.php');
include('navbar.php');

$infoindex = $dxxxx->info;


// افترض أن لديك متغير $offer الذي يحتوي على بيانات العرض
$busType = $UmrahData[0]['vip'] ? 'باص 3 صفوف vip' : 'باص اساسي 4 صفوف'; // تحديد نوع الباص بناءً على القيمة


$price = $UmrahData[0]['aprice'] != '' ? json_decode($UmrahData[0]['aprice']) : [];
$days = $UmrahData[0]['days'] != '' ? json_decode($UmrahData[0]['days']) : [];
$daysJson = json_encode($days ,JSON_UNESCAPED_UNICODE );
$selectedInfo = isset($price[$infoindex]) ? $price[$infoindex] : null;

$selectedPrice = 0;
if ($selectedInfo != null) {
    for ($x = 0; $x < count($selectedInfo->info); $x++) {
        if (strpos($selectedInfo->info[$x]->name, 'ريال') !== false) {
            $selectedPrice = $selectedInfo->info[$x]->name;
        }
    }
}

$hotel = json_decode($UmrahData[0]['hotelid']);
$k = []; $d = []; $k_photos = []; $d_photos = [];
if (isset($hotel->k)) {
    $k = getAllFrom('*', 'hotels', 'WHERE id= "' . $hotel->k . '" ', '');
    if (count($k) <= 0) {
        http_response_code(404);
        header("Location:" . $Site_URL . "/error404");
        exit();
    }
    $k_photos = json_decode($k[0]['photos']);
    for ($i = count($k_photos); $i <= 5; $i++) {
        array_push($k_photos, $default_image);
    }
}

if (isset($hotel->d) && $hotel->d != 0) {
    $d = getAllFrom('*', 'hotels', 'WHERE id= "' . $hotel->d . '" ', '');
    if (count($d) <= 0) {
        http_response_code(404);
        header("Location:" . $Site_URL . "/error404");
        exit();
    }

    $d_photos = json_decode($d[0]['photos']);
    for ($i = count($d_photos); $i <= 5; $i++) {
        array_push($d_photos, $default_image);
    }
}






// حساب تاريخ العودة بناءً على تاريخ الرحلة
$departureDate = isset($_POST['date_timepicker_end']) ? $_POST['date_timepicker_end'] : date("Y-m-d");
$duration = 0;
$info = isset($UmrahData[0]['info']) ? json_decode($UmrahData[0]['info']) : [];
for ($i = 0; $i < count($info); $i++) {
    if (isset($info[$i]->key) && $info[$i]->key === 'مدة الرحلة') {
        $duration = intval($info[$i]->val);
        break;
    }
}
$returnDate = date('Y-m-d', strtotime($departureDate . ' + ' . ($duration - 1) . ' days')); // طرح 1 ليكون تاريخ العودة بعد مدة الرحلة















echo '





<section id="main-container" class="main-content home-page-default container inner">
    <div class="row responsive-medium">
        <div id="main-content" class="col-12 col-lg-12 col-sm-12 col-12">
            <div id="main" class="site-main layout-blog" role="main">
                <div class="row">
                    <div class="col-md-4">
                        <div class="layout-posts-list">
                            <article class="modal-body">
                                <div class="list-inner align-items-center">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="boxhtel">
                                            <div class="list-categories"><a href="#" class="categories-name">' . $UmrahData[0]['title'] . '</a></div>
                                            <h6 class="y-title m0">
                                                <a href="#">' . $k[0]['name'] . '</a>
                                            </h6>
                                            <div class="description">
                                                <div class="list-meta">
                                                    <!-- مربع Accordion القابل للفتح والإغلاق -->
    <div class="accordion-style3">
        <div class="accordion" id="accordionExampleInfo">
            <div class="accordion-item border-none bgc-transparent">
                <h2 class="accordion-header" id="headingInfo">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInfo" aria-expanded="true" aria-controls="collapseInfo">
                        تفاصيل الرحلة
                    </button>
                </h2>
                <div id="collapseInfo" class="accordion-collapse collapse" aria-labelledby="headingInfo" data-bs-parent="#accordionExampleInfo">
                    <div class="accordion-body">
                        <div class="description">
                            <div class="list-meta">
                                <ul>

                                                ';

                                                $info = isset($UmrahData[0]['info']) ? json_decode($UmrahData[0]['info']) : [];
                                                for ($i = 0; $i < count($info); $i++) {
                                                    echo '
                                                    <li><a class="col-4 me-0"><span class="' . $info[$i]->ico . '"></span><strong>' . $info[$i]->key . ': </strong> ' . $info[$i]->val . '</a></li>';
                                                }
                                                echo '
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </article>
                        </div>
                        <div class="modal-bg">
                        <div class="modal-body">
                            <input id="bt_hamla" type="hidden" class="form-control" value="' . $UmrahData[0]['title'] . '">
                            <div class="row">
                                <h6>ادخل تاريخ السفر</h6>
                                <div class="col-12 d-flex flex-wrap">
                                      <div class="col-12">
                                        <div class="ui-content">
                                            <div class="form-style1">
                                                <input type="text" id="date_timepicker_end" value="' . date('Y-m-d', strtotime(' +4 days')) . '" class="form-control" placeholder="تاريخ السفر" onchange="updateTravelDates()">
                                                <i class="fas fa-calendar-alt" style="position: absolute;color: #ff8f00; left: 10px; top: 35%; transform: translateY(-50%); pointer-events: none;"></i>
                                                   <hr>
                                            </div>
                                        </div>
                                    </div>
                                </div> 



                                <div class="col-12 d-flex flex-wrap">
                                    <div class="col-4 mb-3 p5">
                                        <div class="ui-content">
                                            
                                            <div class="form-style2">
                                                <label class="info-form-titel">تاريخ السفر</label>
                                                <li class="date-box" id="departure_date_display">' . $departureDate . '  </li>
                                                <p id="departure_day"></p>  
                                                                                 

                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 mb-3 p5">
                                      <div class="bus-line">
                                     <span class="line"></span><i class="fas fa-bus" style=" font-size: 16px;  color: #ff8f00;"></i>
                                       <span class="line"></span>
                                         </div>
                                                                                  </div>


                                    <div class="col-4 mb-3 p5">
                                        <div class="ui-content">
                                            <div class="form-style2">
                                                <label class="info-form-titel">تاريخ العودة</label>
                                                <li class="date-box" id="return_date_display">' . $returnDate . ' </li>
                                                <p id="return_day"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>




                        <div class="modal-body">
                            <input id="bt_hamla" type="hidden" class="form-control" value="' . $UmrahData[0]['title'] . '">

                            <div class="row">






                            
                                <input type="hidden" value="0" id="bt_book">                   
                                
                                <div class="form-style1 mt15">
                                           <label class="form-label dark-color">لقد اخترت</label>
                                    
                                        <li><a class="text-room-ofer dark-color fw600 mb-1 title" id="room_title"><span class="fa fa-light fa-door-closed"></span>' . $selectedInfo->title . '</a></li>';
                
                                        for ($i = 0; $i < count($selectedInfo->info); $i++) {
                                            if ($selectedPrice == $selectedInfo->info[$i]->name) {
                                                continue;
                                            }
                                            echo '<a class="listing_room-ofer col-6 me-0" href="javascript:void(0)">
                                                    <span class="fa ' . $selectedInfo->info[$i]->icon . '"></span> ' . $selectedInfo->info[$i]->name . '
                                                  </a>';
                                        }
                                        
                
                                        echo '
                                        <div class="priceoffer" style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="flex: 7; text-align: right;">
                                            <div class="col-lg-12">
                                    <div class="ui-content mb20">
                                      <div class="form-group">
                                            <label class="form-slabel dark-color">العدد </label>
                                            <div class="input-group">                                            
                                            <input id="bt_people" onkeyup="calcPrice(' . preg_replace('/[^0-9]/', '', $selectedPrice) . ')" type="number" class="form-control" value="1" min="1" max="20">
                                               <button type="button" class="btn btn-secondary" id="increase">+</button>
                                               <button type="button" class="btn btn-secondary" id="decrease">-</button>
                                            </div>

                                        </div>

                                        <script>
                                            document.getElementById("increase").addEventListener("click", function() {
                                                let input = document.getElementById("bt_people");
                                                let value = parseInt(input.value);
                                                if (value < 20) {
                                                    input.value = value + 1;
                                                    calcPrice(' . preg_replace('/[^0-9]/', '', $selectedPrice) . ');
                                                }
                                            });

                                            document.getElementById("decrease").addEventListener("click", function() {
                                                let input = document.getElementById("bt_people");
                                                let value = parseInt(input.value);
                                                if (value > 1) {
                                                    input.value = value - 1;
                                                    calcPrice(' . preg_replace('/[^0-9]/', '', $selectedPrice) . ');
                                                }
                                            });
                                        </script>
                                    </div>
                                </div>
                                                
                                            </div>
                                            
                                            
                                            <div style="flex: 8; text-align: left;">
                                                <h6 style="margin: 0;font-size: 22px;"><strong>' . $selectedPrice . '</strong></h6>
                                                <p style="margin: 0; font-size: 14px; color: gray;">' . $selectedInfo->title . '</p>
                                                <p style="margin: 0; font-size: 14px; color: #008234;">
                                                    <i class="fa fal fa-bus-alt"></i> ' . $busType . '
                                                </p>
                                                
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="priceoffer" style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="flex: 4; text-align: right;">
                                                <h6 style="margin: 0;"> السعر الإجمالي:</h6>

                                                   </div>
                                            
                                            <div style="flex: 8; text-align: left;">
                                  <h6 style="margin: 0;font-size: 22px;"><strong id="total_price">' . $selectedPrice . '</strong></h6>
                                                           <p id="original_price_display" class="text-danger" style="display:none;">السعر قبل الخصم: <span id="original_price" style="text-decoration: line-through;">100.00</span> ر.س</p> <!-- لعرض السعر الأصلي مشطوب -->

                          <p id="discount_display" class="text-success"></p> <!-- لعرض الخصم -->


<li>
    <a class="text-roo dark-color fw600 mb-1 title">
        عدد <strong id="co_total">1</strong> <span class="multiplier">×</span> ' . $selectedInfo->title . '
    </a>
</li>
                                            </div>
                                         
                                        </div>
                        
<p>لديك كود خصم ؟</p>
<div class="form-style1 mb20 d-flex align-items-center">
    <input id="discount_code" type="text" class="form-control me-2" placeholder="أدخل كود الخصم">
    <button type="button" id="apply_discount" class="btn btn-secondary">تطبيق</button>
</div>






<div id="discount_message" class="text-danger"></div>

<!-- عنصر السعر الإجمالي، يمكنك تعديله ليتناسب مع تطبيقك -->
<div>
    <p id="original_price_display" class="text-danger" style="display:none;">السعر الأصلي: <span id="original_price" style="text-decoration: line-through;">100.00</span> ر.س</p> <!-- لعرض السعر الأصلي مشطوب -->
    <p id="discount_display" class="text-success"></p> <!-- لعرض الخصم -->
</div>


<script>
    let discountApplied = false; // متغير لتتبع حالة الخصم

    document.getElementById("apply_discount").addEventListener("click", function() {
        var discountCode = document.getElementById("discount_code").value.toUpperCase(); // تحويل الكود المدخل إلى حروف كبيرة
        var totalPriceElement = document.getElementById("total_price");
        var discountDisplayElement = document.getElementById("discount_display");
        var originalPriceElement = document.getElementById("original_price");
        var originalPriceDisplayElement = document.getElementById("original_price_display");
        var originalPrice = parseFloat(totalPriceElement.innerText.replace(/[^0-9.-]+/g,"")); // الحصول على السعر الأصلي
        var discountAmount = 0;

        // تحقق مما إذا تم تطبيق الخصم بالفعل
        if (discountApplied) {
            document.getElementById("discount_message").innerText = "تم تطبيق الخصم بالفعل.";
            return;
        }

        // تحقق من الكود وتحديد قيمة الخصم
        if (discountCode === "MO30") {
            discountAmount = 30; // خصم 30 ريال
        } else {
            document.getElementById("discount_message").innerText = "كود الخصم غير صحيح.";
            discountDisplayElement.innerText = ""; // مسح خصم سابق إذا كان الكود غير صحيح
            return;
        }

        // حساب السعر بعد الخصم
        var newTotalPrice = originalPrice - discountAmount;
        totalPriceElement.innerText = newTotalPrice.toFixed(2); // تحديث السعر الإجمالي
        
        // عرض السعر الأصلي المشطوب
        originalPriceElement.innerText = originalPrice.toFixed(2); // تحديث السعر الأصلي المشطوب
        originalPriceDisplayElement.style.display = "block"; // عرض السعر الأصلي
        
        document.getElementById("discount_message").innerText = "تم تطبيق الخصم بنجاح!";
        
        // عرض الخصم
        discountDisplayElement.innerText = "خصم: " + discountAmount.toFixed(2) + " ر.س"; // عرض القيمة المخصومة
        
        discountApplied = true; // تعيين حالة الخصم على "تم التطبيق"
    });
</script>

</div>
    <div id="discount_message" class="text-danger"></div>
                                    </div>
                     </div>
                     </div>




                     
                    <div class="col-md-8">
<div class="col-lg-12 mt15">
    <div class="d-flex align-items-center mb-3 p-2 custom-background-box2" 
         style="border: 1px solid #ddd; border-radius: 5px; cursor: pointer;">
        <div class="me-2 icon-container">
            <img src="../img/search.png" alt="رمز البحث" class="search-icon"> 
        </div>
        <div class="flex-grow-1 text-container">
            <span class="highlight-text"> لديك كوبون خصم 30 ريال </span>
        </div>
        <div class="ms-2">
            <button class="search-button">
              MO30
            </button>
        </div>
    </div>
</div>


        <div class="modal-body">
    <h5>ادخل بيانات الحجز</h5>
    <div class="row">
        <div class="col-lg-12">
            <div class="form-style2 mb20">
                <label for="bt_name" class="form-label dark-color">اسم صاحب الحجز</label>
                <input id="bt_name" type="text" class="form-control" placeholder="الإسم">
            </div>
        </div>
        <div class="col-lg-12">
            <div class="form-style2 mb20">
                <label for="bt_phone" class="form-label dark-color">رقم واتساب </label>
                <input id="bt_phone" type="text" class="form-control" placeholder="رقم الجوال">
            </div>
        </div>
    </div>
</div>

<div class="modal-body mt20">
    <h5>اختر طريقة الدفع</h5>
    <div class="row">
        <div class="col-lg-12">
            <div class="payment-method">
                <label class="custom_radio">
                    <input type="radio" name="payment" checked>
                    <span class="radio-btn"></span>
            <img src="../img/pay-bus.png" alt="Icon" style="margin-left: 10px; max-width: 30px;"> 
                    <span class="payment-title">دفع عند الوصول</span>
                </label>
            </div>
        </div>
        <div class="col-lg-12">
            <span class="upcoming-text">قريبًا</span> 
            <div class="payment-method">
                <label class="custom_radio">
                    <input type="0" name="payment">
                    <span class="radio-btn"></span>
            <img src="../img/mada.png" alt="Icon" style="margin-left: 10px; max-width: 30px;"> 
                    <span class="payment-title">مدى - mada</span>
                </label>
            </div>
        </div>

 <div class="col-lg-12">
            <span class="upcoming-text">قريبًا</span> 
            <div class="payment-method">
                <label class="custom_radio">
                    <input type="0" name="payment">
                    <span class="radio-btn"></span>
            <img src="../img/visa.png" alt="Icon" style="margin-left: 10px; max-width: 30px;"> 
                    <span class="payment-title">Visa / Mastercard</span>
                </label>
            </div>
        </div>

    </div>
</div>

   


<div class="infobooking">
<h6 style="color: #000;margin-bottom: 3px;">نود إعلامكم</h6>    <p>  أنه في بعض الأحيان القليلة قد نضطر إلى تغيير الفندق المختار لأسباب خارجة عن إرادتنا. ونلتزم بتوفير فندق بنفس المستوى والخدمات لضمان تجربة عمرة مميزة معنا.</p>
</div>





                <!-- زر تأكيد الحجز -->
               <div class="go-order mt20">
    <a onclick="ConfirmBookTrip(' . $UmrahData[0]['id'] . ', ' . $i . ')" href="javascript:void(0)" class="ud-btn udx-btn btn-thm-border">
        <i class="far fa-check-circle"></i> تأكيد الحجز
    </a>
</div>

                <div class="col-md-12" id="res"></div>
                   
                   
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
';


echo '
<script>

function updateTravelDates() {
    var departureDate = document.getElementById("date_timepicker_end").value;
    var duration = ' . $duration . '; // نستخدم المتغير $duration من PHP

    // حساب تاريخ العودة
    var departure = new Date(departureDate);
    departure.setDate(departure.getDate() + (duration - 1)); // طرح 1 ليكون تاريخ العودة بعد مدة الرحلة
    var returnDate = departure.toISOString().split("T")[0]; // للحصول على التاريخ بصيغة YYYY-MM-DD

    // تحديث عرض ملخص الطلب وتاريخ الرحلة
    document.getElementById("departure_date_display").innerText = " " + departureDate;
    document.getElementById("return_date_display").innerText = " " + returnDate;

    // تحديث معلومات مدة الرحلة وأيام السفر والعودة
    var daysOfWeek = ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"];
    var departureDay = daysOfWeek[new Date(departureDate).getDay()];
    var returnDay = daysOfWeek[departure.getDay()];

    const departureElement = document.getElementById("departure_day");
departureElement.innerText = "سفر : " + departureDay;
departureElement.classList.add("date-box3"); // إضافة كلاس للعنصر الأول

const returnElement = document.getElementById("return_day");
returnElement.innerText = "عودة يوم: " + returnDay;
returnElement.classList.add("date-box3"); // إضافة كلاس للعنصر الثاني


    document.getElementById("return_day").innerText = "عودة : " + returnDay;

    
}
    





</script>





';

include('footer.php');
?>
