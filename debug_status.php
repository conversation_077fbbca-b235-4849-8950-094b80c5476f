<?php
include('webset.php');

echo "<h2>تشخيص مشكلة عمود status</h2>";

try {
    // اختبار 1: فحص بنية الجدول
    echo "<h3>1. فحص بنية الجدول:</h3>";
    $stmt = $db->query("DESCRIBE bus_trips");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // اختبار 2: فحص وجود عمود status
    echo "<h3>2. فحص وجود عمود status:</h3>";
    $stmt = $db->query("SHOW COLUMNS FROM bus_trips LIKE 'status'");
    $status_column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($status_column) {
        echo "✅ عمود status موجود<br>";
        echo "النوع: " . $status_column['Type'] . "<br>";
        echo "القيمة الافتراضية: " . $status_column['Default'] . "<br>";
    } else {
        echo "❌ عمود status غير موجود<br>";
    }
    
    // اختبار 3: اختبار الاستعلام المباشر
    echo "<h3>3. اختبار الاستعلام المباشر:</h3>";
    try {
        $stmt = $db->query("SELECT id, from_city, status FROM bus_trips LIMIT 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            echo "✅ الاستعلام نجح<br>";
            echo "ID: " . $result['id'] . "<br>";
            echo "المدينة: " . $result['from_city'] . "<br>";
            echo "الحالة: " . $result['status'] . "<br>";
        } else {
            echo "⚠️ لا توجد بيانات في الجدول<br>";
        }
    } catch (Exception $e) {
        echo "❌ فشل الاستعلام: " . $e->getMessage() . "<br>";
    }
    
    // اختبار 4: اختبار الاستعلام مع WHERE
    echo "<h3>4. اختبار الاستعلام مع WHERE status:</h3>";
    try {
        $stmt = $db->query("SELECT COUNT(*) as total FROM bus_trips WHERE status = 1");
        $total = $stmt->fetchColumn();
        echo "✅ الاستعلام مع WHERE نجح<br>";
        echo "عدد الرحلات النشطة: " . $total . "<br>";
    } catch (Exception $e) {
        echo "❌ فشل الاستعلام مع WHERE: " . $e->getMessage() . "<br>";
    }
    
    // اختبار 5: اختبار الاستعلام المستخدم في API
    echo "<h3>5. اختبار الاستعلام المستخدم في API:</h3>";
    try {
        $stmt = $db->prepare("SELECT DISTINCT from_city FROM bus_trips WHERE trip_type = 'to_mecca' AND status = 1 ORDER BY from_city");
        $stmt->execute();
        $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "✅ استعلام API نجح<br>";
        echo "عدد المدن: " . count($cities) . "<br>";
        foreach ($cities as $city) {
            echo "- " . $city['from_city'] . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ فشل استعلام API: " . $e->getMessage() . "<br>";
    }
    
    // اختبار 6: معلومات قاعدة البيانات
    echo "<h3>6. معلومات قاعدة البيانات:</h3>";
    $stmt = $db->query("SELECT DATABASE() as db_name");
    $db_info = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "اسم قاعدة البيانات: " . $db_info['db_name'] . "<br>";
    
    $stmt = $db->query("SELECT VERSION() as version");
    $version_info = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "إصدار MySQL: " . $version_info['version'] . "<br>";
    
} catch (Exception $e) {
    echo "خطأ عام: " . $e->getMessage() . "<br>";
}
?>

<style>
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h2, h3 { color: #333; }
</style>
