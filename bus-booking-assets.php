<?php
// ملف مشترك للـ CSS والـ JavaScript الخاص بحجز الباصات
echo '
<style>
.bus-booking-hero {
    position: relative;
}

.bus-booking-hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.tab-item:hover {
    background: #e9ecef !important;
    color: #333 !important;
}

.tab-item.active {
    background: #28a745 !important;
    color: white !important;
}

.btn-counter:hover {
    background: #e9ecef !important;
    transform: scale(1.1);
}

.btn-search:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
}

.trip-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s;
    border: 2px solid transparent;
}

.trip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-color: #ff9500;
}

.trip-route {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.route-point {
    text-align: center;
    flex: 1;
}

.route-arrow {
    margin: 0 15px;
    color: #ff9500;
    font-size: 24px;
}

.trip-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    text-align: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.detail-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.detail-value {
    font-weight: bold;
    color: #333;
}

.price-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.price-display {
    font-size: 24px;
    font-weight: bold;
    color: #28a745;
}

.book-btn {
    background: linear-gradient(135deg, #ff9500 0%, #ff7b00 100%);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: bold;
    transition: all 0.3s;
}

.book-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 149, 0, 0.3);
    color: white;
}

.city-badge {
    display: inline-block;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
    text-align: center;
    min-width: 120px;
}

.departure-badge {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.arrival-badge {
    background: linear-gradient(135deg, #ff9500, #ff7b00);
    color: white;
}

.city-name {
    display: block;
    font-size: 16px;
    font-weight: bold;
}

.station-name {
    display: block;
    font-size: 12px;
    opacity: 0.9;
    margin-top: 2px;
}

.location-section {
    margin: 15px 0;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.location-toggle:hover {
    background: #f8f9fa !important;
    border-color: #ff9500 !important;
    color: #ff9500 !important;
}

.station-addresses {
    margin: 15px 0;
}

.address-item {
    margin: 10px 0;
    padding: 12px;
    border-radius: 8px;
    border-right: 3px solid;
    transition: all 0.3s ease;
}

.departure-address {
    background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
    border-right-color: #28a745;
}

.arrival-address {
    background: linear-gradient(135deg, #fff8f0, #ffeee0);
    border-right-color: #ff9500;
}

.address-label {
    font-weight: bold;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.address-label i {
    margin-left: 8px;
    font-size: 16px;
}

.address-value {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    padding-right: 24px;
}

.departure-address .address-label {
    color: #28a745;
}

.arrival-address .address-label {
    color: #ff9500;
}

.passenger-selector {
    margin: 15px 0;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.passenger-selector:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.passenger-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.passenger-label {
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
}

.passenger-counter-trip {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 8px;
    border: 1px solid #ddd;
    overflow: hidden;
}

.btn-counter-trip {
    background: none;
    border: none;
    width: 35px;
    height: 35px;
    font-size: 16px;
    font-weight: bold;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-counter-trip:hover {
    background: #f8f9fa;
    color: #ff9500;
}

.btn-counter-trip:active {
    transform: scale(0.95);
}

.passenger-input-trip {
    border: none;
    width: 50px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    background: transparent;
    color: #333;
}

.passenger-note {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 8px;
}

.price-display {
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .booking-form-card {
        margin: 5px;
        padding: 20px !important;
    }

    .trip-details {
        grid-template-columns: 1fr 1fr;
    }

    .col-6 .form-label {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .col-6 .form-select,
    .col-6 .form-control {
        padding: 8px;
        font-size: 14px;
    }

    .passenger-counter {
        padding: 4px !important;
    }

    .btn-counter {
        width: 30px !important;
        height: 30px !important;
        font-size: 14px !important;
    }

    .passenger-counter input {
        font-size: 14px !important;
    }

    .city-badge {
        min-width: 100px;
        padding: 6px 10px;
        font-size: 14px;
    }

    .city-name {
        font-size: 14px;
    }

    .station-name {
        font-size: 11px;
    }

    .trip-date-header {
        padding: 8px 10px !important;
        font-size: 14px;
    }

    .passenger-selector {
        margin-bottom: 10px !important;
    }

    .seat-price-box {
        margin-bottom: 10px !important;
    }

    div[style*="display: flex; gap: 15px"] {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .seat-price-value {
        font-size: 18px !important;
    }
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const decreaseBtn = document.getElementById("decreaseBtn");
    const increaseBtn = document.getElementById("increaseBtn");
    const passengerCount = document.getElementById("passengerCount");
    const busSearchForm = document.getElementById("busSearchForm");
    const fromStationSelect = document.getElementById("fromStation");

    // للصفحات المخصصة - تحميل المحطات تلقائياً
    const presetFromCity = document.getElementById("presetFromCity");
    const tripType = document.getElementById("tripType").value;

    if (presetFromCity && presetFromCity.value) {
        loadStationsForCity(presetFromCity.value, tripType);
    }

    // تحميل المحطات لمدينة محددة
    function loadStationsForCity(city, tripType) {
        fetch("bus-api.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                action: "get_stations",
                city: city,
                trip_type: tripType
            })
        })
        .then(response => response.json())
        .then(data => {
            fromStationSelect.innerHTML = "<option value=\\"\\">اختر المحطة</option>";

            if (data.success && data.stations) {
                data.stations.forEach(station => {
                    const option = document.createElement("option");
                    option.value = station;
                    option.textContent = station;
                    fromStationSelect.appendChild(option);
                });
                fromStationSelect.disabled = false;

                // تحديد المحطة المسبقة إن وجدت
                const presetStation = document.getElementById("presetFromStation");
                if (presetStation && presetStation.value) {
                    fromStationSelect.value = presetStation.value;
                }
            }
        })
        .catch(error => {
            console.error("Error loading stations:", error);
        });
    }

    // عداد المسافرين
    if (decreaseBtn) {
        decreaseBtn.addEventListener("click", function() {
            let count = parseInt(passengerCount.value);
            if (count > 1) {
                passengerCount.value = count - 1;
            }
        });
    }

    if (increaseBtn) {
        increaseBtn.addEventListener("click", function() {
            let count = parseInt(passengerCount.value);
            if (count < 10) {
                passengerCount.value = count + 1;
            }
        });
    }

    // معالج البحث
    if (busSearchForm) {
        busSearchForm.addEventListener("submit", function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const searchData = {
                action: "search_trips",
                trip_type: formData.get("trip_type"),
                from_city: formData.get("from_city"),
                from_station: formData.get("from_station"),
                travel_date: formData.get("travel_date"),
                passenger_count: formData.get("passenger_count")
            };

            // التحقق من البيانات المطلوبة
            if (!searchData.from_city) {
                alert("يرجى اختيار المدينة");
                return;
            }

            if (!searchData.from_station) {
                alert("يرجى اختيار المحطة");
                return;
            }

            if (!searchData.travel_date) {
                alert("يرجى اختيار تاريخ السفر");
                return;
            }

            // عرض مؤشر التحميل
            const searchBtn = this.querySelector(".btn-search");
            const originalText = searchBtn.innerHTML;
            searchBtn.innerHTML = "<span class=\\"spinner-border spinner-border-sm me-2\\"></span>جاري البحث...";
            searchBtn.disabled = true;

            fetch("bus-api.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(searchData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayTrips(data.trips, searchData);
                    document.getElementById("searchResults").style.display = "block";
                    document.getElementById("searchResults").scrollIntoView({ behavior: "smooth" });
                } else {
                    alert(data.message || "لم يتم العثور على رحلات متاحة");
                }
            })
            .catch(error => {
                console.error("Error searching trips:", error);
                alert("حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.");
            })
            .finally(() => {
                searchBtn.innerHTML = originalText;
                searchBtn.disabled = false;
            });
        });
    }

    // عرض الرحلات
    function displayTrips(trips, searchData) {
        const container = document.getElementById("tripsContainer");

        if (!trips || trips.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="mb-3" style="font-size: 4rem;">🚌</div>
                    <h4>لا توجد رحلات متاحة</h4>
                    <p class="text-muted">لم يتم العثور على رحلات متاحة للتاريخ والمسار المحدد</p>
                </div>
            `;
            return;
        }

        let html = "";
        trips.forEach((trip, index) => {
            const totalPrice = (parseFloat(trip.seat_price) * parseInt(searchData.passenger_count)).toFixed(2);

            html += `
                <div class="trip-card">
                    <!-- تاريخ الرحلة -->
                    <div class="trip-date-header" style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 10px 15px; border-radius: 10px 10px 0 0; border-bottom: 2px solid #ff9500; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; justify-content: center;">
                            <i class="fa fa-calendar" style="color: #ff9500; margin-left: 8px;"></i>
                            <span style="font-weight: bold; color: #333;">تاريخ الرحلة: ${formatDate(searchData.travel_date)}</span>
                        </div>
                    </div>

                    <div class="trip-route">
                        <div class="route-point">
                            <div class="city-badge departure-badge">
                                <span class="city-name">${trip.from_city}</span>
                                <span class="station-name">${trip.from_station}</span>
                            </div>
                        </div>
                        <div class="route-arrow">
                            <i class="fa fa-arrow-left" style="color: #ff9500; font-size: 24px;"></i>
                        </div>
                        <div class="route-point">
                            <div class="city-badge arrival-badge">
                                <span class="city-name">${trip.to_city}</span>
                                <span class="station-name">${trip.to_station}</span>
                            </div>
                        </div>
                    </div>

                    <div class="trip-details">
                        <div class="detail-item">
                            <div class="detail-label">وقت المغادرة</div>
                            <div class="detail-value">${formatTime(trip.departure_time)}</div>
                        </div>
                    </div>

                    <!-- العناوين التفصيلية للمحطات -->
                    <div class="station-addresses">
            `;

            // إضافة عنوان محطة الانطلاق
            if (trip.departure_address) {
                html += `
                    <div class="address-item departure-address" style="margin: 10px 0; padding: 10px; background: #f0f8f0; border-radius: 8px; border-right: 3px solid #28a745;">
                        <div class="address-label" style="font-weight: bold; color: #28a745; margin-bottom: 5px;">
                            <i class="fa fa-map-marker" style="margin-left: 5px;"></i>
                            عنوان محطة الانطلاق:
                        </div>
                        <div class="address-value" style="color: #666; font-size: 14px; line-height: 1.4;">${trip.departure_address}</div>
                    </div>
                `;
            }

            // إضافة عنوان محطة الوصول
            if (trip.arrival_address) {
                html += `
                    <div class="address-item arrival-address" style="margin: 10px 0; padding: 10px; background: #fff8f0; border-radius: 8px; border-right: 3px solid #ff9500;">
                        <div class="address-label" style="font-weight: bold; color: #ff9500; margin-bottom: 5px;">
                            <i class="fa fa-flag" style="margin-left: 5px;"></i>
                            عنوان محطة الوصول:
                        </div>
                        <div class="address-value" style="color: #666; font-size: 14px; line-height: 1.4;">${trip.arrival_address}</div>
                    </div>
                `;
            }

            html += `
                </div>
            `;

            // إضافة مربع الموقع إذا كان متوفراً
            if (trip.departure_location) {
                html += `
                <div class="location-section" style="margin: 15px 0; padding-top: 15px; border-top: 1px solid #eee;">
                    <button class="location-toggle" onclick="toggleLocation(` + index + `)" style="background: none; border: 1px solid #ddd; border-radius: 5px; padding: 8px 15px; color: #666; cursor: pointer; font-size: 14px; width: 100%;">
                        <i class="fa fa-map-marker"></i> عرض موقع الانطلاق
                    </button>
                    <div id="location-` + index + `" class="location-details" style="display: none; margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <p style="margin: 0 0 10px 0; color: #666; font-size: 14px;">موقع محطة الانطلاق:</p>
                        <a href="` + trip.departure_location + `" target="_blank" style="color: #ff9500; text-decoration: none; font-weight: bold; display: inline-block; padding: 8px 15px; background: white; border-radius: 5px; border: 1px solid #ff9500;">
                            <i class="fa fa-external-link"></i> فتح في خرائط جوجل
                        </a>
                    </div>
                </div>
                `;
            }

            html += `
                <!-- مربعين منفصلين: عدد المسافرين وسعر المقعد -->
                <div style="display: flex; gap: 15px; margin: 15px 0; align-items: stretch;">
                    <!-- مربع عدد المسافرين -->
                    <div class="passenger-selector" style="flex: 1; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;">
                        <div class="passenger-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                            <div class="passenger-label" style="font-weight: bold; color: #333;">
                                <i class="fa fa-users" style="color: #007bff; margin-left: 8px;"></i>
                                عدد المسافرين
                            </div>
                            <div class="passenger-counter-trip" style="display: flex; align-items: center; background: white; border-radius: 8px; border: 1px solid #ddd;">
                                <button type="button" class="btn-counter-trip" onclick="updatePassengerCount(${index}, -1)" style="background: none; border: none; width: 35px; height: 35px; border-radius: 6px; font-size: 16px; font-weight: bold; color: #666; cursor: pointer;">-</button>
                                <input type="number" id="passenger-count-${index}" class="passenger-input-trip" value="${searchData.passenger_count}" min="1" max="10" style="border: none; width: 50px; text-align: center; font-size: 16px; font-weight: bold; background: transparent;" readonly>
                                <button type="button" class="btn-counter-trip" onclick="updatePassengerCount(${index}, 1)" style="background: none; border: none; width: 35px; height: 35px; border-radius: 6px; font-size: 16px; font-weight: bold; color: #666; cursor: pointer;">+</button>
                            </div>
                        </div>
                        <div class="passenger-note" style="font-size: 12px; color: #666; text-align: center;">
                            <i class="fa fa-info-circle" style="margin-left: 5px;"></i>
                            يُنصح بحجز مقعد للأطفال مع ولي الأمر
                        </div>
                    </div>

                    <!-- مربع سعر المقعد -->
                    <div class="seat-price-box" style="flex: 1; padding: 15px; background: linear-gradient(135deg, #e8f5e8, #f0f8f0); border-radius: 10px; border: 1px solid #28a745; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <div class="seat-price-label" style="font-weight: bold; color: #28a745; margin-bottom: 8px;">
                            <i class="fa fa-money" style="color: #28a745; margin-left: 8px;"></i>
                            سعر المقعد
                        </div>
                        <div class="seat-price-value" style="font-size: 20px; font-weight: bold; color: #28a745;">
                            ${trip.seat_price} ريال
                        </div>
                    </div>
                </div>

                <div class="price-section">
                    <div>
                        <div class="price-display" id="total-price-${index}">${totalPrice} ريال</div>
                        <div style="font-size: 14px; color: #666;" id="passenger-text-${index}">لـ ${searchData.passenger_count} مسافر</div>
                    </div>
                    <button class="book-btn" id="book-btn-${index}" onclick="bookTrip(${trip.id}, \'${searchData.travel_date}\', ${searchData.passenger_count}, ${totalPrice})">
                        احجز الآن
                    </button>
                </div>
            </div>
        `;
        });

        container.innerHTML = html;

        // حفظ بيانات الرحلات للاستخدام في تحديث الأسعار
        window.currentTripsData = trips;
    }

    // تنسيق الوقت
    function formatTime(timeString) {
        const time = new Date("2000-01-01 " + timeString);
        return time.toLocaleTimeString("ar-SA", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true
        });
    }

    // تنسيق التاريخ
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString("ar-SA", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric"
        });
    }
});

// دالة حجز الرحلة
function bookTrip(tripId, travelDate, passengerCount, totalPrice) {
    // إنشاء نموذج الحجز
    const bookingData = {
        trip_id: tripId,
        travel_date: travelDate,
        passenger_count: passengerCount,
        total_price: totalPrice
    };

    // تحويل إلى صفحة الحجز
    const encodedData = encodeURIComponent(JSON.stringify(bookingData));
    window.location.href = `bus-booking-form.php?data=${encodedData}`;
}

// دالة إظهار/إخفاء الموقع
function toggleLocation(index) {
    const locationDiv = document.getElementById(`location-${index}`);
    const button = event.target;

    if (locationDiv.style.display === "none") {
        locationDiv.style.display = "block";
        button.innerHTML = "<i class=\\"fas fa-map-marker-alt\\"></i> إخفاء موقع الانطلاق";
    } else {
        locationDiv.style.display = "none";
        button.innerHTML = "<i class=\\"fas fa-map-marker-alt\\"></i> عرض موقع الانطلاق";
    }
}

// دالة تحديث عدد المسافرين
function updatePassengerCount(tripIndex, change) {
    const passengerInput = document.getElementById(`passenger-count-${tripIndex}`);
    const totalPriceElement = document.getElementById(`total-price-${tripIndex}`);
    const passengerTextElement = document.getElementById(`passenger-text-${tripIndex}`);
    const bookButton = document.getElementById(`book-btn-${tripIndex}`);

    let currentCount = parseInt(passengerInput.value);
    let newCount = currentCount + change;

    // التحقق من الحدود
    if (newCount < 1) newCount = 1;
    if (newCount > 10) newCount = 10;

    // تحديث العدد
    passengerInput.value = newCount;

    // الحصول على سعر المقعد الواحد من البيانات المخزنة
    const tripData = window.currentTripsData[tripIndex];
    const seatPrice = parseFloat(tripData.seat_price);
    const newTotalPrice = (seatPrice * newCount).toFixed(2);

    // تحديث السعر الإجمالي
    totalPriceElement.textContent = newTotalPrice + " ريال";

    // تحديث نص المسافرين
    passengerTextElement.textContent = `لـ ${newCount} مسافر`;

    // تحديث زر الحجز
    const travelDate = document.getElementById("travelDate").value;
    bookButton.setAttribute("onclick", "bookTrip(" + tripData.id + ", \'" + travelDate + "\', " + newCount + ", " + newTotalPrice + ")");

    // تأثير بصري للتغيير
    totalPriceElement.style.transform = "scale(1.1)";
    totalPriceElement.style.color = "#ff9500";
    setTimeout(function() {
        totalPriceElement.style.transform = "scale(1)";
        totalPriceElement.style.color = "#ff9500";
    }, 200);
}
</script>';
?>
