<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/bootstrap.rtl.min.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/jquery-ui.min.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/ace-responsive-menu.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/menu.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/fontawesome.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/flaticon.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/bootstrap-select.min.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/animate.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/slider.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/jquery-ui.min.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/magnific-popup.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/template.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/style.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/ud-custom-spacing.css">
        <link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/responsive.css">
        
        <style>
            *{-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;-o-user-select:none;user-select:none;-webkit-touch-callout:default;-webkit-touch-callout:none}::-webkit-scrollbar{width:.01em}::-webkit-scrollbar-track{box-shadow:inset 0 0 6px transparent;-webkit-box-shadow:inset 0 0 6px transparent}::-webkit-scrollbar-thumb{background-color:#000;outline:0 solid transparent}::selection{background:#ddd}::-moz-selection{background:#ddd}
            :root{
                --MasterColor : <?php echo $MasterColorForAll;?>;
            }
            .ltr{
                direction: ltr;
            } 
            .home-banner-style1 {
                background-image: url("<?php echo $Site_URL;?>/img/home-1.jpg");
            }
            .autohigh{
                height: 200px;
                overflow: hidden;
            }
            .autohigh.show{
                height: auto !important;
            }
            input[type="number"] {
                -webkit-appearance: textfield !important;
            }
            .dis{
                pointer-events: none;
                opacity: 0.6;
            }
            /* إزالة أي تأثيرات قد تخفي الأزرار */
            input[type="number"]::-webkit-outer-spin-button,
            input[type="number"]::-webkit-inner-spin-button {
                -webkit-appearance: spinbutton !important;
            }

            /* تأكيد عرض الأزرار في Firefox */
            input[type="number"] {
                -moz-appearance: number-input !important;
            }
        </style>