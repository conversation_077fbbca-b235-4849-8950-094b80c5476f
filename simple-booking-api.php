<?php
include('webset.php');

// تعيين header للاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// تسجيل جميع الأخطاء
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', 'booking_debug.log');

try {
    // فحص طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب يجب أن تكون POST');
    }
    
    // قراءة البيانات
    $raw_input = file_get_contents('php://input');
    if (empty($raw_input)) {
        throw new Exception('لا توجد بيانات مرسلة');
    }
    
    // تحليل JSON
    $input = json_decode($raw_input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('خطأ في تحليل JSON: ' . json_last_error_msg());
    }
    
    // فحص العملية
    $action = $input['action'] ?? '';
    if ($action !== 'create_booking') {
        throw new Exception('عملية غير مدعومة: ' . $action);
    }
    
    // فحص البيانات المطلوبة
    $required_fields = [
        'hotel_id', 'room_type_index', 'room_name', 'room_price',
        'guest_name', 'guest_phone', 'check_in_date', 'check_out_date',
        'nights', 'room_count', 'total_cost'
    ];
    
    $missing_fields = [];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || $input[$field] === '' || $input[$field] === null) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        throw new Exception('الحقول التالية مطلوبة: ' . implode(', ', $missing_fields));
    }
    
    // التحقق من الفندق
    $hotel_sql = "SELECT * FROM hotels_booking WHERE id = ? AND status = 1 AND booking_enabled = 1";
    $hotel_stmt = $db->prepare($hotel_sql);
    $hotel_stmt->execute([$input['hotel_id']]);
    $hotel = $hotel_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$hotel) {
        throw new Exception('الفندق غير متاح للحجز');
    }
    
    // التحقق من التواريخ
    $check_in = new DateTime($input['check_in_date']);
    $check_out = new DateTime($input['check_out_date']);
    $today = new DateTime();
    
    if ($check_in < $today) {
        throw new Exception('تاريخ تسجيل الدخول لا يمكن أن يكون في الماضي');
    }
    
    if ($check_out <= $check_in) {
        throw new Exception('تاريخ تسجيل الخروج يجب أن يكون بعد تاريخ الدخول');
    }
    
    // إنشاء رقم مرجعي فريد
    do {
        $booking_reference = 'HTL' . date('Ymd') . rand(1000, 9999);
        $ref_sql = "SELECT id FROM hotel_bookings WHERE booking_reference = ?";
        $ref_stmt = $db->prepare($ref_sql);
        $ref_stmt->execute([$booking_reference]);
    } while ($ref_stmt->rowCount() > 0);
    
    // حساب التكلفة
    $calculated_cost = floatval($input['room_price']) * intval($input['nights']) * intval($input['room_count']);
    $submitted_cost = floatval(preg_replace('/[^0-9.]/', '', $input['total_cost']));
    
    // إدراج الحجز
    $sql = "INSERT INTO hotel_bookings (
        booking_reference, hotel_id, room_type_index, room_name, room_price,
        guest_name, guest_phone, guest_email, guest_count,
        check_in_date, check_out_date, nights, room_count, total_cost,
        booking_status, payment_status, created_by_ip
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending', ?)";
    
    $stmt = $db->prepare($sql);
    $result = $stmt->execute([
        $booking_reference,
        $input['hotel_id'],
        $input['room_type_index'],
        $input['room_name'],
        $input['room_price'],
        $input['guest_name'],
        $input['guest_phone'],
        $input['guest_email'] ?? null,
        $input['guest_count'] ?? 1,
        $input['check_in_date'],
        $input['check_out_date'],
        $input['nights'],
        $input['room_count'],
        $calculated_cost,
        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    if (!$result) {
        throw new Exception('فشل في حفظ الحجز في قاعدة البيانات');
    }
    
    $booking_id = $db->lastInsertId();
    
    // إرسال استجابة النجاح
    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء الحجز بنجاح',
        'booking_id' => $booking_id,
        'booking_reference' => $booking_reference,
        'data' => [
            'hotel_name' => $hotel['name'],
            'room_name' => $input['room_name'],
            'check_in_date' => $input['check_in_date'],
            'check_out_date' => $input['check_out_date'],
            'nights' => $input['nights'],
            'room_count' => $input['room_count'],
            'guest_count' => $input['guest_count'],
            'total_cost' => $calculated_cost,
            'guest_name' => $input['guest_name'],
            'guest_phone' => $input['guest_phone']
        ]
    ]);
    
} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("Booking API Error: " . $e->getMessage() . " | Input: " . ($raw_input ?? 'none'));
    
    // إرسال استجابة الخطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'BOOKING_ERROR',
        'debug_info' => [
            'error_line' => $e->getLine(),
            'error_file' => basename($e->getFile()),
            'input_received' => !empty($raw_input),
            'json_valid' => isset($input)
        ]
    ]);
    
} catch (PDOException $e) {
    // خطأ قاعدة البيانات
    error_log("Database Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Throwable $e) {
    // أي خطأ آخر
    error_log("Unexpected Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ غير متوقع',
        'error_code' => 'UNEXPECTED_ERROR'
    ]);
}
?>
