<?php

$Title_page = $HotelData[0]['name'];

include('header.php');
include('navbar.php');

echo '
<section class="pt20 pb90 bgc-f7">
    <div class="container">
        <div class="row wow fadeInUp" data-wow-delay="100ms">
            <div class="col-lg-12">
                <div class="single-property-content mb0-md">
                    <h2 class="sp-lg-title">'.$HotelData[0]['name'].'</h2>
                    <div class="pd-meta mb15 d-md-flex align-items-center">
                        <a class="text fz15 mb-0 bdrr1 pl10 bdrrn-sm" >
                            <span class="icon fal fa-map-marker-alt" style="color: #ff9b00; padding: 3px;"></span> 
                           '.$HotelData[0]['address'].'
                        </a>
                        <a class="text fz15 mb-0 bdrr1 pl10 bdrrn-sm">
                            <span class="icon fal fa-star" style="color: #ff9b00; padding: 3px;"></span>
                            تصنيف: '.$HotelData[0]['star_rating'].' نجوم
                        </a>
                    </div>
                </div>
            </div> 
        </div>

        <div class="row mb30 mt5 wow fadeInUp" data-wow-delay="300ms">
            <div class="col-sm-12">
            </div>';

            if (isset($HotelData[0]['photos']) && !empty($HotelData[0]['photos'])) {
                $photos = json_decode($HotelData[0]['photos'], true); // فك تشفير المصفوفة من JSON

             
                if (is_array($photos) && count($photos) > 0) {
                  
                    echo '<div class="col-sm-6">
                            <div class="sp-img-content mb15-md">
                                <a class="popup-img preview-img-1 sp-img" href="'.$Site_URL.'/'.$photos[0].'">
                                    <img class="w-100" src="'.$Site_URL.'/'.$photos[0].'" alt="'.$HotelData[0]['name'].'_1">
                                </a>
                            </div>
                          </div>';

                    // عرض باقي الصور
                    echo '<div class="col-sm-6">
                            <div class="row">';
                    for ($i = 1; $i < count($photos); $i++) {
                        echo '
                            <div class="col-6 ps-sm-0">
                                <div class="sp-img-content">
                                    <a class="popup-img sp-img mb10" href="'.$Site_URL.'/'.$photos[$i].'">
                                        <img class="w-100" src="'.$Site_URL.'/'.$photos[$i].'" alt="'.$HotelData[0]['name'].'_'.($i + 1).'">
                                    </a>
                                </div>
                            </div>';
                    }
                    echo '</div>
                          </div>';
                } else {
                 
                    echo '<p>لا توجد صور لعرضها.</p>';
                }
            } else {
                echo '<p>لا توجد صور لعرضها.</p>';
            }

        echo '
        </div>';

       
        $hotel_info = !empty($HotelData[0]['info']) ? json_decode($HotelData[0]['info'], true) : [];
        if (count($hotel_info) > 0) {
            echo '
            <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 mt30 overflow-hidden position-relative">
                <h4 class="title fz17 mb30">معلومات  '.$HotelData[0]['name'].'</h4>
                <div class="row">';
                foreach ($hotel_info as $info) {
                    echo '
                    <div class="col-sm-6 col-lg-4">
                        <div class="overview-element mb25 d-flex align-items-center">
                            <span class="icon '.$info['ico'].'"></span>
                            <div class="mr15">
                                <h6 class="mb-0">'.$info['key'].'</h6>
                                <p class="text mb-0 fz15">'.$info['val'].'</p>
                            </div>
                        </div>
                    </div>';
                }
            echo '
                </div>
            </div>';
        }


$hotel_description = !empty($HotelData[0]['description']) ? [
    ['key' => 'نبذة عن '.$HotelData[0]['name'].'', 'val' => $HotelData[0]['description'], 'ico' => 'fal fa-info-circle']
] : [];

if (count($hotel_description) > 0) {
    echo '
    <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 mt30 overflow-hidden position-relative">
        <div class="row">';
        
    foreach ($hotel_description as $info) {
        echo '
        <div class="col-sm-12 col-lg-12">
            <div class="overview-element mb25 d-flex align-items-center">
                <span class="icon '.$info['ico'].'"></span>
                <div class="mr15">
                    <h6 class="mb-0">'.$info['key'].'</h6>
                    <p class="text mb-0 fz15">'.nl2br($info['val']).'</p>
                </div>
            </div>
        </div>';
    }

    echo '
        </div>
    </div>';
}




        echo '
        <div class="col-lg-12">
            <div class="custom-box-quality">
                <div class="d-flex align-items-center mb-2">
                    
                    <span class="box-title">المرافق والخدمات</span>
                </div>
                <div class="pd-list mt-2">
    <p class="text mb-2"><i class="fas fa-smoking-ban fz6 align-middle pe-2"></i> غرف لغير المدخنين</p>
    <p class="text mb-2"><i class="fas fa-concierge-bell fz6 align-middle pe-2"></i> خدمة الغرف</p>
    <p class="text mb-2"><i class="fas fa-wheelchair fz6 align-middle pe-2"></i> مرافق لذوي الاحتياجات الخاصة</p>
    <p class="text mb-2"><i class="fas fa-wifi fz6 align-middle pe-2"></i> واي فاي مجاني</p>
    <p class="text mb-2"><i class="fas fa-users fz6 align-middle pe-2"></i> غرف عائلية</p>
    <p class="text mb-2"><i class="fas fa-clock fz6 align-middle pe-2"></i> مكتب استقبال على مدار 24 ساعة</p>
    <p class="text mb-2"><i class="fas fa-elevator fz6 align-middle pe-2"></i> مصعد</p>
    <p class="text mb-0"><i class="fas fa-snowflake fz6 align-middle pe-2"></i> تكييف</p>
</div>
            </div>
        </div>

        <!-- نظام حجز الغرف مع Sidebar -->
        <div class="row mb30 mt30 wow fadeInUp" data-wow-delay="500ms">
            <!-- قسم الغرف -->
            <div class="col-lg-8">
                <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative">
                    <h4 class="title fz17 mb30">أنواع الغرف المتاحة</h4>';

                    // عرض أنواع الغرف إذا كانت متوفرة
                    if(!empty($HotelData[0]['room_types'])) {
                        $room_types = json_decode($HotelData[0]['room_types'], true);
                        if(is_array($room_types) && count($room_types) > 0) {

                            for ($i=0; $i < count($room_types) ; $i++) {
                                $borb = $i < count($room_types)-1 ? 'bdrb1' : '';

                                $n = count($room_types[$i]['info']) > 0 ? preg_replace('/[^0-9]/', '', $room_types[$i]['info'][0]['name']) : 0;
                                $io = '';
                                for ($z=1; $z <= $n ; $z++) {
                                    $io .= '<span class="icon '.($z == $n ? "ml15" : '').' mb10-sm '.$room_types[$i]['icon'].'"></span>';
                                }

                                echo '
                                <div class="booking walkscore d-sm-flex align-items-center mb20 '.$borb.'" id="room-'.$i.'">
                                    <div class="details-booking">
                                        <p class="text-room dark-color fw600 mb-1 title">'.$io.' '.$room_types[$i]['title'].' </p>
                                        <p class="text mb-1">'.$room_types[$i]['desc'].'</p>

                                        <div class="modal-body">
                                            <div class="form-label dark-color">السعر يشمل</div>';

                                            // البحث عن السعر أولاً
                                            $room_price = 0;
                                            $price_text = '';
                                            foreach($room_types[$i]['info'] as $info) {
                                                if(strpos($info['name'], 'ريال') !== false) {
                                                    $room_price = preg_replace('/[^0-9]/', '', $info['name']);
                                                    $price_text = $info['name'];
                                                    break;
                                                }
                                            }

                                            for ($x = 0; $x < count($room_types[$i]['info']); $x++) {
                                                $pad = $x == 0 ? '' : 'pr10';
                                                $por = $x < count($room_types[$i]['info']) - 1 ? 'bdrr1' : '';

                                                // تحقق مما إذا كانت المعلومات تحتوي على "ريال"
                                                if (strpos($room_types[$i]['info'][$x]['name'], 'ريال') !== false) {

                                                    echo ' <a class="text fz15 mb-0 '.$por.' '.$pad.' pl10 bdrrn-sm price-highlight" href="javascript:void(0)">
                                                            </a><hr>

                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <a class="text fz15 mb-0 pl10 price-highlight" href="javascript:void(0)">
                                                                        <strong style="color: #4a8f46; font-weight: bold; font-size: 18px;">'.$price_text.'</strong>
                                                                    </a>
                                                                    <p style="margin: 0; font-size: 12px; color: #008234;">  <i class="fa fal fa-bed"></i> '.$room_types[$i]['title'].' </p>
                                                                </div>
                                                                <div class="col-6">
                                                                    <div class="gonow">
                                                                        <a href="javascript:void(0)" onclick="selectRoom('.$i.', \''.$room_types[$i]['title'].'\', '.$room_price.')" class="'.($HotelData[0]['booking_enabled'] == 1 ? '' : 'dis').'">
                                                                            <i class="far fa-check-circle"></i> اختر هذه الغرفة
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>';

                                                } else {
                                                    echo ' <a class="listing_info-ofer '.$por.' '.$pad.' pl10 bdrrn-sm" href="javascript:void(0)">
                                                                <span class="fa '.$room_types[$i]['info'][$x]['icon'].'"></span>'.$room_types[$i]['info'][$x]['name'].'
                                                            </a>';
                                                }
                                            }

                                            echo '
                                        </div>
                                    </div>
                                </div>';
                            }
                        } else {
                            echo '<p class="text-center text-muted">لا توجد غرف متاحة حالياً</p>';
                        }
                    } else {
                        echo '<p class="text-center text-muted">لا توجد معلومات عن الغرف</p>';
                    }

                    echo '
                </div>
            </div>

            <!-- Sidebar الحجز -->
            <div class="col-lg-4">
                <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative" style="position: sticky; top: 20px;">
                    <h4 class="title fz17 mb20">احجز إقامتك</h4>

                    <!-- اختيار التواريخ -->
                    <div class="booking-dates mb20">
                        <div class="row">
                            <div class="col-12 mb15">
                                <label class="form-label dark-color">تاريخ تسجيل الدخول</label>
                                <input type="date" id="check_in_date" class="form-control" value="'.date('Y-m-d', strtotime('+1 day')).'" onchange="updateBookingCalculation()">
                            </div>
                            <div class="col-12 mb15">
                                <label class="form-label dark-color">تاريخ تسجيل الخروج</label>
                                <input type="date" id="check_out_date" class="form-control" value="'.date('Y-m-d', strtotime('+2 days')).'" onchange="updateBookingCalculation()">
                            </div>
                        </div>

                        <!-- عرض عدد الليالي -->
                        <div class="nights-display text-center p15 bgc-f7 bdrs8 mb15">
                            <span class="fz14 text-muted">عدد الليالي</span>
                            <h5 class="mb0 text-primary" id="nights_count">1</h5>
                        </div>
                    </div>

                    <!-- اختيار عدد الغرف والضيوف -->
                    <div class="guests-rooms mb20">
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label dark-color">عدد الغرف</label>
                                <div class="input-group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="changeRoomCount(-1)">-</button>
                                    <input type="number" id="room_count" class="form-control text-center" value="1" min="1" max="10" readonly>
                                    <button type="button" class="btn btn-outline-secondary" onclick="changeRoomCount(1)">+</button>
                                </div>
                            </div>
                            <div class="col-6">
                                <label class="form-label dark-color">عدد الضيوف</label>
                                <div class="input-group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="changeGuestCount(-1)">-</button>
                                    <input type="number" id="guest_count" class="form-control text-center" value="1" min="1" max="20" readonly>
                                    <button type="button" class="btn btn-outline-secondary" onclick="changeGuestCount(1)">+</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الغرفة المختارة -->
                    <div class="selected-room mb20" id="selected_room_info" style="display: none;">
                        <div class="p15 bgc-f7 bdrs8">
                            <h6 class="mb10">الغرفة المختارة:</h6>
                            <p class="mb5" id="selected_room_name">-</p>
                            <p class="mb0 text-success" id="selected_room_price">-</p>
                        </div>
                    </div>

                    <!-- ملخص التكلفة -->
                    <div class="cost-summary mb20">
                        <div class="p15 border bdrs8">
                            <h6 class="mb15">ملخص التكلفة</h6>
                            <div class="d-flex justify-content-between mb10">
                                <span>السعر لكل ليلة:</span>
                                <span id="price_per_night">اختر الغرفة أولاً</span>
                            </div>
                            <div class="d-flex justify-content-between mb10">
                                <span>عدد الليالي:</span>
                                <span id="total_nights">1</span>
                            </div>
                            <div class="d-flex justify-content-between mb10">
                                <span>عدد الغرف:</span>
                                <span id="total_rooms">1</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>المجموع الكلي:</strong>
                                <strong class="text-primary" id="total_cost">اختر الغرفة أولاً</strong>
                            </div>
                        </div>
                    </div>

                    <!-- زر الحجز -->
                    <div class="booking-action">
                        <button class="btn btn-primary w-100 btn-lg" id="book_now_btn" onclick="proceedToBooking()" disabled>
                            <i class="fas fa-calendar-check me-2"></i>
                            احجز الآن
                        </button>
                        <p class="text-center text-muted mt10 fz12" id="booking_note">اختر نوع الغرفة أولاً</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// متغيرات النظام
let selectedRoomIndex = null;
let selectedRoomName = "";
let selectedRoomPrice = 0;
const hotelId = parseInt("'.$HotelData[0]['id'].'");
const hotelName = "'.$HotelData[0]['name'].'";

console.log("Hotel ID:", hotelId);
console.log("Hotel Name:", hotelName);

// تهيئة النظام
document.addEventListener("DOMContentLoaded", function() {
    console.log("Hotel booking system initialized"); // للتشخيص

    // تعيين الحد الأدنى للتاريخ (اليوم)
    const today = new Date().toISOString().split("T")[0];
    const checkInInput = document.getElementById("check_in_date");
    const checkOutInput = document.getElementById("check_out_date");

    if (checkInInput) {
        checkInInput.setAttribute("min", today);
        // تعيين قيمة افتراضية إذا لم تكن موجودة
        if (!checkInInput.value) {
            checkInInput.value = today;
        }
    }

    if (checkOutInput) {
        checkOutInput.setAttribute("min", today);
        // تعيين قيمة افتراضية إذا لم تكن موجودة
        if (!checkOutInput.value) {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            checkOutInput.value = tomorrow.toISOString().split("T")[0];
        }
    }

    // تحديث الحسابات الأولية
    updateBookingCalculation();
});

// اختيار الغرفة
function selectRoom(roomIndex, roomName, roomPrice) {
    console.log("Selecting room:", roomIndex, roomName, roomPrice); // للتشخيص

    selectedRoomIndex = roomIndex;
    selectedRoomName = roomName;
    selectedRoomPrice = parseInt(roomPrice) || 0;

    // تحديث عرض الغرفة المختارة
    document.getElementById("selected_room_info").style.display = "block";
    document.getElementById("selected_room_name").textContent = roomName;
    document.getElementById("selected_room_price").textContent = selectedRoomPrice + " ريال لكل ليلة";

    // تفعيل زر الحجز
    const bookBtn = document.getElementById("book_now_btn");
    const bookNote = document.getElementById("booking_note");

    if (bookBtn) {
        bookBtn.disabled = false;
        bookBtn.style.backgroundColor = "#007bff";
        bookBtn.style.cursor = "pointer";
    }

    if (bookNote) {
        bookNote.textContent = "جاهز للحجز";
        bookNote.style.color = "#28a745";
    }

    // تحديث الحسابات
    updateBookingCalculation();

    // تمييز الغرفة المختارة
    document.querySelectorAll(".booking").forEach(function(room, index) {
        if(index === roomIndex) {
            room.style.border = "2px solid #007bff";
            room.style.backgroundColor = "#f8f9ff";
        } else {
            room.style.border = "1px solid #e3e3e3";
            room.style.backgroundColor = "#fff";
        }
    });
}

// تحديث حسابات الحجز
function updateBookingCalculation() {
    const checkInInput = document.getElementById("check_in_date");
    const checkOutInput = document.getElementById("check_out_date");

    if (!checkInInput.value || !checkOutInput.value) {
        return; // لا تحديث إذا لم تكن التواريخ محددة
    }

    const checkInDate = new Date(checkInInput.value);
    const checkOutDate = new Date(checkOutInput.value);

    // التأكد من صحة التواريخ
    if (checkOutDate <= checkInDate) {
        const newCheckOut = new Date(checkInDate);
        newCheckOut.setDate(newCheckOut.getDate() + 1);
        checkOutInput.value = newCheckOut.toISOString().split("T")[0];
        updateBookingCalculation();
        return;
    }

    // حساب عدد الليالي
    const timeDiff = checkOutDate.getTime() - checkInDate.getTime();
    const nights = Math.ceil(timeDiff / (1000 * 3600 * 24));

    // تحديث عرض عدد الليالي
    document.getElementById("nights_count").textContent = nights;
    document.getElementById("total_nights").textContent = nights;

    // حساب التكلفة الإجمالية إذا كانت الغرفة مختارة
    const roomCount = parseInt(document.getElementById("room_count").value) || 1;

    if (selectedRoomPrice > 0) {
        const totalCost = selectedRoomPrice * nights * roomCount;

        // تحديث العرض
        document.getElementById("price_per_night").textContent = selectedRoomPrice + " ريال";
        document.getElementById("total_rooms").textContent = roomCount;
        document.getElementById("total_cost").textContent = totalCost + " ريال";
    } else {
        // إذا لم تكن الغرفة مختارة بعد
        document.getElementById("total_rooms").textContent = roomCount;
    }
}

// تغيير عدد الغرف
function changeRoomCount(change) {
    const roomCountInput = document.getElementById("room_count");
    let currentCount = parseInt(roomCountInput.value);
    let newCount = currentCount + change;

    if (newCount >= 1 && newCount <= 10) {
        roomCountInput.value = newCount;
        updateBookingCalculation();
    }
}

// تغيير عدد الضيوف
function changeGuestCount(change) {
    const guestCountInput = document.getElementById("guest_count");
    let currentCount = parseInt(guestCountInput.value);
    let newCount = currentCount + change;

    if (newCount >= 1 && newCount <= 20) {
        guestCountInput.value = newCount;
    }
}

// المتابعة للحجز
function proceedToBooking() {
    console.log("proceedToBooking called"); // للتشخيص
    console.log("selectedRoomIndex:", selectedRoomIndex);

    if (selectedRoomIndex === null || selectedRoomIndex === undefined) {
        alert("يرجى اختيار نوع الغرفة أولاً");
        return;
    }

    const checkInDate = document.getElementById("check_in_date").value;
    const checkOutDate = document.getElementById("check_out_date").value;
    const roomCount = document.getElementById("room_count").value;
    const guestCount = document.getElementById("guest_count").value;
    const totalCostText = document.getElementById("total_cost").textContent;
    const nights = document.getElementById("nights_count").textContent;

    console.log("Form data:", {checkInDate, checkOutDate, roomCount, guestCount, totalCostText, nights});

    // التأكد من صحة البيانات
    if (!checkInDate || !checkOutDate) {
        alert("يرجى اختيار تواريخ الإقامة");
        return;
    }

    if (selectedRoomPrice <= 0) {
        alert("خطأ في سعر الغرفة، يرجى اختيار الغرفة مرة أخرى");
        return;
    }

    // التأكد من صحة البيانات الأساسية
    if (!hotelId || hotelId <= 0) {
        alert("خطأ في معرف الفندق");
        return;
    }

    if (!selectedRoomName || selectedRoomPrice <= 0) {
        alert("خطأ في بيانات الغرفة المختارة");
        return;
    }

    // إنشاء بيانات الحجز
    const bookingData = {
        hotel_id: hotelId,
        room_type_index: selectedRoomIndex,
        room_type: selectedRoomIndex, // للتوافق مع النسخة القديمة
        room_name: selectedRoomName,
        room_price: selectedRoomPrice,
        check_in_date: checkInDate,
        check_out_date: checkOutDate,
        nights: parseInt(nights),
        room_count: parseInt(roomCount),
        guest_count: parseInt(guestCount),
        total_cost: totalCostText,
        hotel_name: hotelName
    };

    console.log("Final booking data:", bookingData);

    try {
        // تحويل إلى صفحة الحجز باستخدام encodeURIComponent بدلاً من btoa
        const jsonString = JSON.stringify(bookingData);
        const encodedData = encodeURIComponent(jsonString);
        console.log("Encoded data:", encodedData);

        const bookingUrl = "'.$Site_URL.'/hotel-booking/" + encodedData;
        console.log("Redirecting to:", bookingUrl);

        window.location.href = bookingUrl;
    } catch (error) {
        console.error("Error in booking process:", error);
        alert("حدث خطأ في عملية الحجز، يرجى المحاولة مرة أخرى");
    }
}

// تحديث تاريخ الخروج عند تغيير تاريخ الدخول
document.getElementById("check_in_date").addEventListener("change", function() {
    const checkInDate = new Date(this.value);
    const checkOutDate = new Date(document.getElementById("check_out_date").value);

    if (checkOutDate <= checkInDate) {
        const newCheckOut = new Date(checkInDate);
        newCheckOut.setDate(newCheckOut.getDate() + 1);
        document.getElementById("check_out_date").value = newCheckOut.toISOString().split("T")[0];
    }

    // تحديث الحد الأدنى لتاريخ الخروج
    const minCheckOut = new Date(checkInDate);
    minCheckOut.setDate(minCheckOut.getDate() + 1);
    document.getElementById("check_out_date").setAttribute("min", minCheckOut.toISOString().split("T")[0]);

    updateBookingCalculation();
});
</script>';

include('footer.php');
?>
