-- تحديث حقل departure_time ليصبح نص قابل للكتابة يدوياً
-- بدلاً من حقل TIME المحدد

-- إضافة حقل جديد للوقت النصي
ALTER TABLE bus_trips ADD COLUMN departure_time_text VARCHAR(50) AFTER departure_time;

-- نسخ البيانات الحالية من departure_time إلى departure_time_text مع تنسيق عربي
UPDATE bus_trips SET departure_time_text = 
    CASE 
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '13:30' THEN '1:30 ظهراً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '20:00' THEN '8:00 مساءً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '15:00' THEN '3:00 عصراً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '14:00' THEN '2:00 ظهراً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '16:00' THEN '4:00 عصراً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '18:00' THEN '6:00 مساءً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '12:00' THEN '12:00 ظهراً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '10:00' THEN '10:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '11:00' THEN '11:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '09:00' THEN '9:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '17:00' THEN '5:00 عصراً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '19:00' THEN '7:00 مساءً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '21:00' THEN '9:00 مساءً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '22:00' THEN '10:00 مساءً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '23:00' THEN '11:00 مساءً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '00:00' THEN '12:00 منتصف الليل'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '01:00' THEN '1:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '02:00' THEN '2:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '03:00' THEN '3:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '04:00' THEN '4:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '05:00' THEN '5:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '06:00' THEN '6:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '07:00' THEN '7:00 صباحاً'
        WHEN TIME_FORMAT(departure_time, '%H:%i') = '08:00' THEN '8:00 صباحاً'
        ELSE CONCAT(
            CASE 
                WHEN HOUR(departure_time) = 0 THEN '12'
                WHEN HOUR(departure_time) <= 12 THEN HOUR(departure_time)
                ELSE HOUR(departure_time) - 12
            END,
            CASE 
                WHEN MINUTE(departure_time) = 0 THEN ':00'
                ELSE CONCAT(':', LPAD(MINUTE(departure_time), 2, '0'))
            END,
            ' ',
            CASE 
                WHEN HOUR(departure_time) < 12 THEN 'صباحاً'
                WHEN HOUR(departure_time) = 12 THEN 'ظهراً'
                ELSE 'مساءً'
            END
        )
    END;

-- حذف الحقل القديم
ALTER TABLE bus_trips DROP COLUMN departure_time;

-- إعادة تسمية الحقل الجديد
ALTER TABLE bus_trips CHANGE departure_time_text departure_time VARCHAR(50) NOT NULL;

-- إضافة حقل مساعد للترتيب (اختياري)
ALTER TABLE bus_trips ADD COLUMN departure_sort_time TIME AFTER departure_time;

-- تحديث حقل الترتيب بناءً على النص (للاستعلامات المرتبة)
UPDATE bus_trips SET departure_sort_time = 
    CASE 
        WHEN departure_time LIKE '%صباحاً%' THEN 
            CASE 
                WHEN departure_time LIKE '12:%' THEN '00:00:00'
                WHEN departure_time LIKE '1:%' THEN '01:00:00'
                WHEN departure_time LIKE '2:%' THEN '02:00:00'
                WHEN departure_time LIKE '3:%' THEN '03:00:00'
                WHEN departure_time LIKE '4:%' THEN '04:00:00'
                WHEN departure_time LIKE '5:%' THEN '05:00:00'
                WHEN departure_time LIKE '6:%' THEN '06:00:00'
                WHEN departure_time LIKE '7:%' THEN '07:00:00'
                WHEN departure_time LIKE '8:%' THEN '08:00:00'
                WHEN departure_time LIKE '9:%' THEN '09:00:00'
                WHEN departure_time LIKE '10:%' THEN '10:00:00'
                WHEN departure_time LIKE '11:%' THEN '11:00:00'
                ELSE '06:00:00'
            END
        WHEN departure_time LIKE '%ظهراً%' THEN 
            CASE 
                WHEN departure_time LIKE '12:%' THEN '12:00:00'
                WHEN departure_time LIKE '1:%' THEN '13:00:00'
                WHEN departure_time LIKE '2:%' THEN '14:00:00'
                WHEN departure_time LIKE '3:%' THEN '15:00:00'
                ELSE '12:00:00'
            END
        WHEN departure_time LIKE '%عصراً%' THEN 
            CASE 
                WHEN departure_time LIKE '3:%' THEN '15:00:00'
                WHEN departure_time LIKE '4:%' THEN '16:00:00'
                WHEN departure_time LIKE '5:%' THEN '17:00:00'
                ELSE '15:00:00'
            END
        WHEN departure_time LIKE '%مساءً%' THEN 
            CASE 
                WHEN departure_time LIKE '6:%' THEN '18:00:00'
                WHEN departure_time LIKE '7:%' THEN '19:00:00'
                WHEN departure_time LIKE '8:%' THEN '20:00:00'
                WHEN departure_time LIKE '9:%' THEN '21:00:00'
                WHEN departure_time LIKE '10:%' THEN '22:00:00'
                WHEN departure_time LIKE '11:%' THEN '23:00:00'
                ELSE '18:00:00'
            END
        WHEN departure_time LIKE '%منتصف الليل%' THEN '00:00:00'
        ELSE '12:00:00'
    END;
