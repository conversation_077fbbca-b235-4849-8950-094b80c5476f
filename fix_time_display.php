<?php
include('webset.php');

echo "<h2>إصلاح مشكلة عرض الوقت</h2>";

try {
    // التحقق من وجود البيانات
    $stmt = $db->query("SELECT COUNT(*) as count FROM bus_trips");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>حالة قاعدة البيانات:</h4>";
    echo "<p>عدد الرحلات الموجودة: <strong>$count</strong></p>";
    
    // التحقق من وجود الحقول الجديدة
    $check_columns = $db->query("SHOW COLUMNS FROM bus_trips");
    $columns = $check_columns->fetchAll(PDO::FETCH_ASSOC);
    
    $has_departure_sort_time = false;
    $departure_time_type = '';
    
    foreach ($columns as $column) {
        if ($column['Field'] == 'departure_sort_time') {
            $has_departure_sort_time = true;
        }
        if ($column['Field'] == 'departure_time') {
            $departure_time_type = $column['Type'];
        }
    }
    
    echo "<p>نوع حقل departure_time: <strong>$departure_time_type</strong></p>";
    echo "<p>وجود حقل departure_sort_time: <strong>" . ($has_departure_sort_time ? 'نعم' : 'لا') . "</strong></p>";
    echo "</div>";
    
    // عرض عينة من البيانات الحالية
    echo "<h3>عينة من البيانات الحالية:</h3>";
    $stmt = $db->query("SELECT id, from_city, to_city, departure_time" . 
                      ($has_departure_sort_time ? ", departure_sort_time" : "") . 
                      " FROM bus_trips LIMIT 10");
    $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($trips) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>من المدينة</th>";
        echo "<th style='padding: 8px;'>إلى المدينة</th>";
        echo "<th style='padding: 8px;'>departure_time</th>";
        if ($has_departure_sort_time) {
            echo "<th style='padding: 8px;'>departure_sort_time</th>";
        }
        echo "</tr>";
        
        foreach ($trips as $trip) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $trip['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . $trip['from_city'] . "</td>";
            echo "<td style='padding: 8px;'>" . $trip['to_city'] . "</td>";
            echo "<td style='padding: 8px; font-weight: bold; color: #17a2b8;'>" . $trip['departure_time'] . "</td>";
            if ($has_departure_sort_time) {
                echo "<td style='padding: 8px; color: #666;'>" . ($trip['departure_sort_time'] ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // إصلاح البيانات إذا لزم الأمر
    if (isset($_GET['fix']) && $_GET['fix'] == 'yes') {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>جاري إصلاح البيانات...</h4>";
        
        if (!$has_departure_sort_time) {
            // إضافة حقل departure_sort_time إذا لم يكن موجوداً
            $db->exec("ALTER TABLE bus_trips ADD COLUMN departure_sort_time TIME AFTER departure_time");
            echo "<p style='color: green;'>✅ تم إضافة حقل departure_sort_time</p>";
        }
        
        // تحديث البيانات الفارغة أو NULL
        $update_count = 0;
        
        // إذا كان departure_time من نوع TIME، نحوله إلى نص
        if (strpos($departure_time_type, 'time') !== false) {
            $db->exec("ALTER TABLE bus_trips MODIFY departure_time VARCHAR(50) NOT NULL");
            echo "<p style='color: green;'>✅ تم تحويل نوع حقل departure_time إلى VARCHAR</p>";
            
            // تحويل الأوقات الموجودة إلى نص عربي
            $conversion_sql = "
                UPDATE bus_trips SET 
                departure_time = CASE 
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '13:30' THEN '1:30 ظهراً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '20:00' THEN '8:00 مساءً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '15:00' THEN '3:00 عصراً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '14:00' THEN '2:00 ظهراً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '16:00' THEN '4:00 عصراً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '18:00' THEN '6:00 مساءً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '12:00' THEN '12:00 ظهراً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '10:00' THEN '10:00 صباحاً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '11:00' THEN '11:00 صباحاً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '09:00' THEN '9:00 صباحاً'
                    WHEN TIME_FORMAT(departure_time, '%H:%i') = '00:00' THEN 'منتصف الليل'
                    ELSE CONCAT(
                        CASE 
                            WHEN HOUR(departure_time) = 0 THEN '12'
                            WHEN HOUR(departure_time) <= 12 THEN HOUR(departure_time)
                            ELSE HOUR(departure_time) - 12
                        END,
                        CASE 
                            WHEN MINUTE(departure_time) = 0 THEN ':00'
                            ELSE CONCAT(':', LPAD(MINUTE(departure_time), 2, '0'))
                        END,
                        ' ',
                        CASE 
                            WHEN HOUR(departure_time) < 12 THEN 'صباحاً'
                            WHEN HOUR(departure_time) = 12 THEN 'ظهراً'
                            ELSE 'مساءً'
                        END
                    )
                END,
                departure_sort_time = departure_time
            ";
            
            $db->exec($conversion_sql);
            echo "<p style='color: green;'>✅ تم تحويل جميع الأوقات إلى نص عربي</p>";
        }
        
        // التأكد من وجود قيم في departure_sort_time
        $stmt = $db->query("SELECT COUNT(*) as count FROM bus_trips WHERE departure_sort_time IS NULL");
        $null_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($null_count > 0) {
            // محاولة استخراج الوقت من النص العربي
            $db->exec("
                UPDATE bus_trips 
                SET departure_sort_time = CASE 
                    WHEN departure_time LIKE '%منتصف الليل%' THEN '00:00:00'
                    WHEN departure_time LIKE '%الفجر%' THEN '05:00:00'
                    WHEN departure_time LIKE '%صباحاً%' THEN 
                        CONCAT(
                            CASE 
                                WHEN departure_time LIKE '12:%' THEN '00'
                                WHEN departure_time LIKE '1:%' OR departure_time LIKE '1 %' THEN '01'
                                WHEN departure_time LIKE '2:%' OR departure_time LIKE '2 %' THEN '02'
                                WHEN departure_time LIKE '3:%' OR departure_time LIKE '3 %' THEN '03'
                                WHEN departure_time LIKE '4:%' OR departure_time LIKE '4 %' THEN '04'
                                WHEN departure_time LIKE '5:%' OR departure_time LIKE '5 %' THEN '05'
                                WHEN departure_time LIKE '6:%' OR departure_time LIKE '6 %' THEN '06'
                                WHEN departure_time LIKE '7:%' OR departure_time LIKE '7 %' THEN '07'
                                WHEN departure_time LIKE '8:%' OR departure_time LIKE '8 %' THEN '08'
                                WHEN departure_time LIKE '9:%' OR departure_time LIKE '9 %' THEN '09'
                                WHEN departure_time LIKE '10:%' OR departure_time LIKE '10 %' THEN '10'
                                WHEN departure_time LIKE '11:%' OR departure_time LIKE '11 %' THEN '11'
                                ELSE '08'
                            END,
                            ':00:00'
                        )
                    WHEN departure_time LIKE '%ظهراً%' THEN 
                        CONCAT(
                            CASE 
                                WHEN departure_time LIKE '12:%' OR departure_time LIKE '12 %' THEN '12'
                                WHEN departure_time LIKE '1:%' OR departure_time LIKE '1 %' THEN '13'
                                WHEN departure_time LIKE '2:%' OR departure_time LIKE '2 %' THEN '14'
                                ELSE '12'
                            END,
                            ':00:00'
                        )
                    WHEN departure_time LIKE '%عصراً%' THEN 
                        CONCAT(
                            CASE 
                                WHEN departure_time LIKE '1:%' OR departure_time LIKE '1 %' THEN '13'
                                WHEN departure_time LIKE '2:%' OR departure_time LIKE '2 %' THEN '14'
                                WHEN departure_time LIKE '3:%' OR departure_time LIKE '3 %' THEN '15'
                                WHEN departure_time LIKE '4:%' OR departure_time LIKE '4 %' THEN '16'
                                WHEN departure_time LIKE '5:%' OR departure_time LIKE '5 %' THEN '17'
                                ELSE '15'
                            END,
                            ':00:00'
                        )
                    WHEN departure_time LIKE '%مساءً%' THEN 
                        CONCAT(
                            CASE 
                                WHEN departure_time LIKE '6:%' OR departure_time LIKE '6 %' THEN '18'
                                WHEN departure_time LIKE '7:%' OR departure_time LIKE '7 %' THEN '19'
                                WHEN departure_time LIKE '8:%' OR departure_time LIKE '8 %' THEN '20'
                                WHEN departure_time LIKE '9:%' OR departure_time LIKE '9 %' THEN '21'
                                WHEN departure_time LIKE '10:%' OR departure_time LIKE '10 %' THEN '22'
                                WHEN departure_time LIKE '11:%' OR departure_time LIKE '11 %' THEN '23'
                                ELSE '18'
                            END,
                            ':00:00'
                        )
                    ELSE '12:00:00'
                END
                WHERE departure_sort_time IS NULL
            ");
            echo "<p style='color: green;'>✅ تم إصلاح $null_count قيمة فارغة في departure_sort_time</p>";
        }
        
        echo "</div>";
        
        echo "<div style='color: green; font-weight: bold; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "✅ تم إصلاح جميع المشاكل بنجاح!";
        echo "</div>";
        
        // عرض البيانات بعد الإصلاح
        echo "<h3>البيانات بعد الإصلاح:</h3>";
        $stmt = $db->query("SELECT id, from_city, to_city, departure_time, departure_sort_time FROM bus_trips LIMIT 10");
        $fixed_trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($fixed_trips) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>من المدينة</th>";
            echo "<th style='padding: 8px;'>إلى المدينة</th>";
            echo "<th style='padding: 8px;'>departure_time</th>";
            echo "<th style='padding: 8px;'>departure_sort_time</th>";
            echo "</tr>";
            
            foreach ($fixed_trips as $trip) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $trip['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['from_city'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['to_city'] . "</td>";
                echo "<td style='padding: 8px; font-weight: bold; color: #28a745;'>" . $trip['departure_time'] . "</td>";
                echo "<td style='padding: 8px; color: #666;'>" . $trip['departure_sort_time'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        // عرض زر الإصلاح
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>هل تريد إصلاح المشاكل؟</h4>";
        echo "<p>سيتم:</p>";
        echo "<ul>";
        echo "<li>إضافة حقل departure_sort_time إذا لم يكن موجوداً</li>";
        echo "<li>تحويل نوع departure_time إلى VARCHAR إذا لزم الأمر</li>";
        echo "<li>تحويل الأوقات الموجودة إلى نص عربي</li>";
        echo "<li>إصلاح القيم الفارغة</li>";
        echo "</ul>";
        echo "<a href='?fix=yes' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>إصلاح المشاكل</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>
