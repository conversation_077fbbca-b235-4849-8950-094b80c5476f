<?php
ob_start();
$Title_page = 'المحافظات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
$EditId = 0;
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'cities' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){ 
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
                    <h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على <b>"<?php echo $ch[0]['name'];?>"</b></h4>
                    <a style="color:#fff" href="cities.php" class="btn btn-sm btn-gradient-danger waves-effect waves-light mrb_10 padrl20">رجوع</a>
				</div>
            </div>
        </div> 
    </div>

    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <input value="<?php echo $ch[0]['id'];?>" class="form-control" type="hidden" id="var0">
                        <div class="col-md-6">
                            <div class="form-group">
                            <label class="form-control-label">إسم المحافظة</label>
                                <input type="text" value="<?php echo $ch[0]['name'];?>" id="var1" class="form-control">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                            <label class="form-control-label">عنوان المحافظة</label>
                                <input type="text" value="<?php echo $ch[0]['title'];?>" id="var2" class="form-control">
                            </div>
                        </div>
                         
                        <div class="col-md-6">
                            <div class="form-group">
                            <label class="form-control-label">رابط المحافظة</label>
                                <input type="text" value="<?php echo $ch[0]['link'];?>" id="var3" class="form-control ltr">
                            </div>
                        </div>

                         

                        <div class="col-md-6">
                            <div class="form-group">
                            <label class="form-control-label">رابط صورة المحافظة  (700*1024)</label>
                                <input type="text" value="<?php echo $ch[0]['photo'];?>" id="var4" class="form-control ltr">
                            </div>
                        </div> 


                        <div class="col-sm-12 col-md-12">
                            <div class="form-group row">
                                <label class="col-sm-12 col-form-label text-left">الكلمات الدلالية</label>
                                <div class="col-sm-12">
                                <textarea class="form-control" type="text" id="var5"><?php echo $ch[0]['tags'];?></textarea>
                                </div>
                            </div>
                        </div>  
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12" id="non4"  >
            <label class="form-control-label">المحتوى</label>
            <div id="toolbar-container"></div>
            <div id="editor" name="editor" class="editor">
            <?php echo $ch[0]['descr'];?>
            </div>
        </div>
    </div>
    <script src="//cdn.ckeditor.com/4.14.1/full/ckeditor.js"></script>
    <script>
        let editorData;  
        CKEDITOR.editorConfig = function(config) {
            config.toolbarGroups = [
                { name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
                { name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
                { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
                { name: 'links', groups: [ 'links' ] },
                { name: 'insert', groups: [ 'insert' ] },
                '/',
                { name: 'styles', groups: [ 'styles' ] },
                { name: 'colors', groups: [ 'colors' ] },
                { name: 'tools', groups: [ 'tools' ] },
                { name: 'others', groups: [ 'others' ] },
                { name: 'about', groups: [ 'about' ] }
            ];
        }; 
        CKEDITOR.config.allowedContent = true;
        CKEDITOR.config.disallowedContent = 'span';
        CKEDITOR.config.removeButtons = 'Save,NewPage,Preview,Print,Templates,PasteText,PasteFromWord,Replace,Find,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,CopyFormatting,Outdent,Indent,Blockquote,CreateDiv,BidiLtr,BidiRtl,Language,Anchor,Flash,HorizontalRule,Smiley,SpecialChar,PageBreak,About,Maximize,ShowBlocks';

        CKEDITOR.config.contentsLangDirection = 'rtl';
        CKEDITOR.config.dialog_buttonsOrder = 'rtl';
        CKEDITOR.config.language = 'ar';
        CKEDITOR.config.height = 400;
        //CKEDITOR.config.allowedContent = 'u em strong ul li a h1 h2 h3 h4 h5 h6 p b table iframe img{width,height}';
        editorData = CKEDITOR.replace('editor'); 
    </script>
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <button onclick="SaveEdit()" id="saveBtn" class="btn btn-gradient-primary btn-round  waves-effect waves-light"><i class="icoz mdi mdi-file-document-box"></i> حفظ التعديلات </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> 
	<?php	
	}else{
		header('Location: cities.php'); exit();
    } 	
//---------------------------------------------------	
}else{ 
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
                <a onclick="AddNewCat()" style="color:#fff" class="btn btn-md btn-gradient-primary waves-effect waves-light">إضافة محافظة جديده</a>
			</div>
		</div><hr>
	</div>			
</div>
<div class="row">
    <div class="col-12">
        <div id="res"></div>
    </div>
    <!-- end col -->
</div>

 
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>

    function GetCatcities(){
        $('#res').html('');
        $.post("ajax.php", { action : 'Getcities' } ,function(data){ 
            $('#res').html(data);
        });
    } 
     
    GetCatcities(); 

    function AddNewCat(){
        Swal({
        title: 'هل انت متأكد؟',
        text: 'هل انت متاكد من انك تريد اضافة محافظة جديده؟',
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
            $.post("ajax.php", { action : 'AddNewCat' } ,function(datax){ 
                Swal({
                    title: 'تم الإجراء بنجاح',
                    text: "",
                    type: 'success',
                    confirmButtonColor: '#3085d6',
                    confirmButtonText: 'إنهاء',
                });
                if(datax != '' && datax > 0){
                    setTimeout(() => {
                        window.location.href = site_url + '/admincp/cities.php?action=edit&id='+datax;
                    }, 1000);
                }
            }); 
        }
      })
    }

    function ShowHidePageAction(pageid , type){
        if (type == 1){
            var txt = "برجاء العلم انه سيتم عرض المحافظة.";
        }else{
            var txt = "برجاء العلم انه سيتم إخفاء المحافظة.";
        }
        Swal({
        title: 'هل انت متأكد؟',
        text: txt,
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'ShowHidePageAction',pageid : pageid ,type : type} ,function(data){ 
            Swal({
              title: 'تم الإجراء بنجاح',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetCatcities();
          });  
          
        }
      })
    }

    function DelPageAction(pageid){
        Swal({
        title: 'هل أنت متأكد انك تريد حذف هذه المحافظة؟',
        text: "برجاء العلم انه سيتم الحذف بشكل نهائي",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'DelPageAction',pageid : pageid} ,function(data){ 
            Swal({
              title: 'تم الحذف',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetCatcities();
          });  
          
        }
      })
    }

    function SaveEdit(){
        $('#saveBtn').hide(100);
        var content = editorData.getData();
        var var0 = $('#var0').val();
        var var1 = $('#var1').val();
        var var2 = $('#var2').val();
        var var3 = $('#var3').val();
        var var4 = $('#var4').val();
        var var5 = $('#var5').val(); 

        $.post("ajax.php", { action : 'SaveEdit' , content : content , var0 : var0 , var1 : var1 , var2 : var2 , var3 : var3 , var4 : var4 , var5 : var5} ,function(data){
            location.reload();
        });
        
    }
    
    $( function() {
        $( "#res" ).sortable({
            update: function(event, ui) {
                var ids = "";
                $(".sorts").each(function(){
                    if($(this).attr('id') != undefined){
                        ids = ids + "," +$(this).attr('id');
                    }
                })
                if (ids != ""){
                    $.post("ajax.php", { action : 'Sortcities' , ids : ids } ,function(data){});
                }
            }
        });
        $( "#res" ).disableSelection(); 

        $( "#resx" ).sortable({
            update: function(event, ui) {
                 
            }
        });
        $( "#resx" ).disableSelection(); 
        
    }); 
   
</script>
