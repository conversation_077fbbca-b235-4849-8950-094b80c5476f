<?php
$Title_page = 'تأكيد حجز الفندق';

// فك تشفير بيانات الحجز
$booking_data = json_decode(urldecode($FURL[2]), true);

// التحقق من وجود البيانات
if (!$booking_data) {
    echo "<div style='padding: 20px; text-align: center;'>";
    echo "<h3>خطأ في بيانات الحجز</h3>";
    echo "<p>لم يتم العثور على بيانات الحجز أو أن البيانات تالفة.</p>";
    echo "<p><a href='".$Site_URL."/hotels-makkah' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة لقائمة الفنادق</a></p>";
    echo "</div>";
    exit();
}

// تشخيص البيانات (يمكن إزالته لاحقاً)
if (isset($_GET['debug'])) {
    echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px; border-radius: 5px;'>";
    echo "<h4>بيانات الحجز المستلمة:</h4>";
    echo "<pre>" . json_encode($booking_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    echo "</div>";
}

$hotel_id = $booking_data['hotel_id'];
$room_type_index = $booking_data['room_type_index'] ?? $booking_data['room_type'] ?? 0;

// جلب بيانات الفندق
$HotelData = getAllFrom('*', 'hotels_booking h JOIN hotel_city hc ON h.hotel_city_id = hc.id', 'WHERE h.id = "'.$hotel_id.'" AND h.status = 1', '');

if(count($HotelData) == 0) {
    http_response_code(404);
    header("Location:".$Site_URL."/error404");
    exit();
}

$hotel = $HotelData[0];
$room_types = json_decode($hotel['room_types'], true);
$selected_room = $room_types[$room_type_index];

// استخدام البيانات المرسلة من الصفحة السابقة
$room_name = $booking_data['room_name'];
$room_price = $booking_data['room_price'];
$check_in_date = $booking_data['check_in_date'];
$check_out_date = $booking_data['check_out_date'];
$nights = $booking_data['nights'];
$room_count = $booking_data['room_count'];
$guest_count = $booking_data['guest_count'];
$total_cost = $booking_data['total_cost'];

include('header.php');
include('navbar.php');

echo '
<section class="pt20 pb90 bgc-f7">
    <div class="container">
        <div class="row wow fadeInUp" data-wow-delay="100ms">
            <div class="col-lg-12">
                <div class="ps-widget bgc-white bdrs12 default-box-shadow2 p30 mb30 overflow-hidden position-relative">
                    <h3 class="title fz20 mb20">حجز فندق '.$hotel['name'].'</h3>
                    
                    <!-- معلومات الفندق -->
                    <div class="row mb30">
                        <div class="col-md-8">
                            <div class="boxhtel">
                                <div class="list-categories">
                                    <a href="#" class="categories-name">'.$hotel['city_name'].'</a>
                                </div>
                                <h6 class="y-title m0">
                                    <a href="#">'.$hotel['name'].'</a>
                                </h6>
                                <div class="description">
                                    <div class="list-meta">
                                        <a class="text fz15 mb-0 bdrr1 pl10 bdrrn-sm">
                                            <span class="icon fal fa-star" style="color: #ff9b00; padding: 3px;"></span>
                                            تصنيف: '.$hotel['star_rating'].' نجوم
                                        </a>
                                        <a class="text fz15 mb-0 bdrr1 pl10 bdrrn-sm">
                                            <span class="icon fal fa-map-marker-alt" style="color: #ff9b00; padding: 3px;"></span>
                                            المسافة: '.$hotel['distance_to_haram'].' كم
                                        </a>';
                                        
                                        if(!empty($hotel['phone'])) {
                                            echo '<a class="text fz15 mb-0 bdrr1 pl10 bdrrn-sm">
                                                    <span class="icon fal fa-phone" style="color: #ff9b00; padding: 3px;"></span>
                                                    '.$hotel['phone'].'
                                                  </a>';
                                        }
                                        
                                        echo '
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="hotel-image">
                                <img src="'.$Site_URL.'/'.(json_decode($hotel['photos'], true)[0] ?? 'img/default-hotel.jpg').'" 
                                     alt="'.$hotel['name'].'" class="w-100 bdrs12" style="height: 150px; object-fit: cover;">
                            </div>
                        </div>
                    </div>
                    
                    <!-- عرض تفاصيل الحجز المحددة -->
                    <div class="modal-body">
                        <h6>تفاصيل الحجز المحدد</h6>

                        <!-- عرض التواريخ المحددة -->
                        <div class="col-12 d-flex flex-wrap mt20">
                            <div class="col-4 mb-3 p5">
                                <div class="ui-content">
                                    <div class="form-style2">
                                        <label class="info-form-titel">تسجيل الدخول</label>
                                        <li class="date-box">'.date('d/m/Y', strtotime($check_in_date)).'</li>
                                        <p>'.date('l', strtotime($check_in_date)).'</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 mb-3 p5">
                                <div class="bus-line">
                                    <span class="line"></span>
                                    <i class="fas fa-bed" style="font-size: 16px; color: #ff8f00;"></i>
                                    <span class="line"></span>
                                </div>
                                <div class="text-center">
                                    <span>'.$nights.'</span> ليلة
                                </div>
                            </div>
                            <div class="col-4 mb-3 p5">
                                <div class="ui-content">
                                    <div class="form-style2">
                                        <label class="info-form-titel">تسجيل الخروج</label>
                                        <li class="date-box">'.date('d/m/Y', strtotime($check_out_date)).'</li>
                                        <p>'.date('l', strtotime($check_out_date)).'</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات الحجز المحددة -->
                    <div class="modal-body">
                        <div class="form-style1 mt15">
                            <label class="form-label dark-color">تفاصيل الحجز</label>
                            <li><a class="text-room-ofer dark-color fw600 mb-1 title">
                                <span class="fa fa-light fa-door-closed"></span>'.$room_name.'
                            </a></li>';

                            // عرض مميزات الغرفة
                            foreach($selected_room['info'] as $info) {
                                if(strpos($info['name'], 'ريال') === false) {
                                    echo '<a class="listing_room-ofer col-6 me-0" href="javascript:void(0)">
                                            <span class="fa '.$info['icon'].'"></span> '.$info['name'].'
                                          </a>';
                                }
                            }

                            echo '
                            <!-- عرض تفاصيل الحجز -->
                            <div class="priceoffer" style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 7; text-align: right;">
                                    <div class="booking-details">
                                        <p><strong>عدد الغرف:</strong> '.$room_count.'</p>
                                        <p><strong>عدد الضيوف:</strong> '.$guest_count.'</p>
                                        <p><strong>عدد الليالي:</strong> '.$nights.'</p>
                                    </div>
                                </div>

                                <div style="flex: 8; text-align: left;">
                                    <h6 style="margin: 0;font-size: 22px;"><strong>'.$room_price.' ريال</strong></h6>
                                    <p style="margin: 0; font-size: 14px; color: gray;">لكل ليلة</p>
                                    <p style="margin: 0; font-size: 14px; color: #008234;">
                                        <i class="fa fal fa-bed"></i> '.$room_name.'
                                    </p>
                                </div>
                            </div>

                            <hr>

                            <!-- عرض السعر الإجمالي -->
                            <div class="priceoffer" style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 4; text-align: right;">
                                    <h6 style="margin: 0;">السعر الإجمالي:</h6>
                                </div>
                                <div style="flex: 8; text-align: left;">
                                    <h6 style="margin: 0;font-size: 22px;"><strong>'.$total_cost.'</strong></h6>
                                    <li>
                                        <a class="text-roo dark-color fw600 mb-1 title">
                                            '.$room_count.' غرفة × '.$nights.' ليلة
                                        </a>
                                    </li>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نموذج بيانات الحجز -->
                    <div class="modal-body">
                        <h5>ادخل بيانات الحجز</h5>
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-style2 mb20">
                                    <label for="guest_name" class="form-label dark-color">اسم صاحب الحجز</label>
                                    <input id="guest_name" type="text" class="form-control" placeholder="الإسم الكامل">
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-style2 mb20">
                                    <label for="guest_phone" class="form-label dark-color">رقم واتساب</label>
                                    <input id="guest_phone" type="text" class="form-control" placeholder="رقم الجوال">
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-style2 mb20">
                                    <label for="guest_email" class="form-label dark-color">البريد الإلكتروني</label>
                                    <input id="guest_email" type="email" class="form-control" placeholder="البريد الإلكتروني">
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-style2 mb20">
                                    <label for="guest_count" class="form-label dark-color">عدد الضيوف</label>
                                    <input id="guest_count" type="number" class="form-control" value="1" min="1" max="20">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="infobooking">
                        <h6 style="color: #000;margin-bottom: 3px;">سياسة الإلغاء</h6>
                        <p>'.$hotel['cancellation_policy'].'</p>
                    </div>
                    
                    <!-- زر تأكيد الحجز -->
                    <div class="go-order mt20">
                        <button onclick="confirmHotelBooking()" class="ud-btn udx-btn btn-thm-border" id="confirm_booking_btn">
                            <i class="far fa-check-circle"></i> تأكيد الحجز
                        </button>
                    </div>

                    <!-- نتيجة الحجز -->
                    <div class="col-md-12 mt20" id="booking_result"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// بيانات الحجز المحددة مسبقاً
const bookingData = {
    hotel_id: '.$hotel_id.',
    room_type_index: '.$room_type_index.',
    room_name: "'.$room_name.'",
    room_price: '.$room_price.',
    check_in_date: "'.$check_in_date.'",
    check_out_date: "'.$check_out_date.'",
    nights: '.$nights.',
    room_count: '.$room_count.',
    guest_count: '.$guest_count.',
    total_cost: "'.$total_cost.'"
};

// تأكيد الحجز النهائي
function confirmHotelBooking() {
    const guestName = document.getElementById("guest_name").value.trim();
    const guestPhone = document.getElementById("guest_phone").value.trim();
    const guestEmail = document.getElementById("guest_email").value.trim();
    const guestCount = document.getElementById("guest_count").value;

    // التحقق من البيانات المطلوبة
    if (!guestName) {
        alert("يرجى إدخال الاسم الكامل");
        document.getElementById("guest_name").focus();
        return;
    }

    if (!guestPhone) {
        alert("يرجى إدخال رقم الهاتف");
        document.getElementById("guest_phone").focus();
        return;
    }

    // التحقق من صحة رقم الهاتف (أرقام سعودية)
    const phoneRegex = /^(05|5)[0-9]{8}$/;
    if (!phoneRegex.test(guestPhone.replace(/[^0-9]/g, ""))) {
        alert("يرجى إدخال رقم هاتف صحيح (مثال: 0501234567)");
        document.getElementById("guest_phone").focus();
        return;
    }

    // تعطيل الزر لمنع الضغط المتكرر
    const confirmBtn = document.getElementById("confirm_booking_btn");
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> جاري التأكيد...";

    // إضافة بيانات العميل للحجز
    const finalBookingData = {
        action: "create_booking",
        hotel_id: parseInt(bookingData.hotel_id),
        room_type_index: parseInt(bookingData.room_type_index),
        room_name: bookingData.room_name || "",
        room_price: parseFloat(bookingData.room_price) || 0,
        guest_name: guestName,
        guest_phone: guestPhone,
        guest_email: guestEmail || "",
        guest_count: parseInt(guestCount) || 1,
        check_in_date: bookingData.check_in_date,
        check_out_date: bookingData.check_out_date,
        nights: parseInt(bookingData.nights) || 1,
        room_count: parseInt(bookingData.room_count) || 1,
        total_cost: bookingData.total_cost
    };

    console.log("Sending booking data:", finalBookingData);

    // إرسال البيانات إلى API
    fetch("'.$Site_URL.'/booking-api.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(finalBookingData)
    })
    .then(response => response.json())
    .then(data => {
        console.log("API Response:", data);

        if (data.success) {
            // عرض رسالة النجاح
            document.getElementById("booking_result").innerHTML = `
                <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4><i class="fas fa-check-circle"></i> تم تأكيد الحجز بنجاح!</h4>
                    <p><strong>رقم الحجز:</strong> ${data.booking_reference}</p>
                    <hr>
                    <h5>تفاصيل الحجز:</h5>
                    <ul>
                        <li><strong>الفندق:</strong> ${data.data.hotel_name}</li>
                        <li><strong>نوع الغرفة:</strong> ${data.data.room_name}</li>
                        <li><strong>تاريخ الدخول:</strong> ${data.data.check_in_date}</li>
                        <li><strong>تاريخ الخروج:</strong> ${data.data.check_out_date}</li>
                        <li><strong>عدد الليالي:</strong> ${data.data.nights}</li>
                        <li><strong>عدد الغرف:</strong> ${data.data.room_count}</li>
                        <li><strong>عدد الضيوف:</strong> ${data.data.guest_count}</li>
                        <li><strong>إجمالي التكلفة:</strong> ${data.data.total_cost} ريال</li>
                    </ul>
                    <hr>
                    <p><strong>اسم النزيل:</strong> ${data.data.guest_name}</p>
                    <p><strong>رقم الهاتف:</strong> ${data.data.guest_phone}</p>
                    <hr>
                    <p><i class="fas fa-info-circle"></i> سيتم التواصل معك خلال 24 ساعة لتأكيد الحجز وترتيب الدفع.</p>
                    <p><strong>احتفظ برقم الحجز للمراجعة: ${data.booking_reference}</strong></p>
                </div>
            `;

            // إخفاء النموذج
            document.querySelector(".modal-body").style.display = "none";
            confirmBtn.style.display = "none";

        } else {
            // عرض رسالة الخطأ
            document.getElementById("booking_result").innerHTML = `
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h5><i class="fas fa-exclamation-triangle"></i> فشل في تأكيد الحجز</h5>
                    <p>${data.message}</p>
                    <p>يرجى المحاولة مرة أخرى أو التواصل معنا.</p>
                </div>
            `;

            // إعادة تفعيل الزر
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = "<i class=\"far fa-check-circle\"></i> تأكيد الحجز";
        }
    })
    .catch(error => {
        console.error("Error:", error);

        document.getElementById("booking_result").innerHTML = `
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h5><i class="fas fa-exclamation-triangle"></i> خطأ في الاتصال</h5>
                <p>حدث خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.</p>
            </div>
        `;

        // إعادة تفعيل الزر
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = "<i class=\"far fa-check-circle\"></i> تأكيد الحجز";
    });
}
</script>';

include('footer.php');
?>
