<?php
ob_start();
include('../webset.php'); 
include('../session.php');

 if ( is_dir ("../files/") === false ){
 mkdir("../files/");
}

  $path = '../files/' ;


function getExtension($str) 
{

         $i = strrpos($str,".");
         if (!$i) { return ""; } 

         $l = strlen($str) - $i;
         $ext = substr($str,$i+1,$l);
         return $ext;
 }

  $valid_formats = array("jpg", "png", "gif", "bmp","jpeg","PNG","JPG","JPEG","GIF","BMP","webp" , "mp4");
  if(isset($_POST) and $_SERVER['REQUEST_METHOD'] == "POST")
    {
      $name = $_FILES['photo']['name'];
      $size = $_FILES['photo']['size'];
    
  
      if(strlen($name))
        {
           $ext = getExtension($name);
          if(in_array($ext,$valid_formats))
          {
          if($size<(1024*1024)) 
            {
              $actual_image_name = $name;
              $tmp = $_FILES['photo']['tmp_name'];
              if(move_uploaded_file($tmp, $path.$actual_image_name))
                {
                
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'LOGO' ){
                    Update( 'Logo' , 'files/'.$actual_image_name );
                  } 
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'Logo_Light' ){
                    Update( 'Logo_Light' , 'files/'.$actual_image_name );
                  } 
                  
                }
              else
                echo "Fail upload folder with read access.";
              redirect_home ('back' , 0);
            }
            else
               redirect_home ('back' , 0);     
            }
            else
          redirect_home ('back' , 0);
        }
        
      else
      redirect_home ('back' , 0);
        
      exit;


    }


//redirect_home ('back' , 0);

ob_end_flush();
?>
