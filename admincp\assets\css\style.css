@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

body,p,span,a,h1,h2,h3,h4,h5,h6,li,b,strong,small{
    font-family: 'Cairo', sans-serif !important;
}
body.dark-topbar .left-sidenav .topbar-left .logo .logo-lg.logo-light {
    height: 25px;
}
.nav-user-name{
    color: #fff;
}
.auth-page {
    max-width: 460px;
    position: relative;
    margin: 0 auto;
}
.icos{
    text-align: center;
    display: inline-block;
    line-height: 35px;
    width: 100%;
}
.noti-date{
    display: inline-block;
    width: 100%;
    margin-bottom: 10px;
    font-size: 12px;
    color: #a2a2a2;
}
.icoz{
    line-height: 20px;
    margin-left: 5px;
    display: inline-block;
}
textarea{
    min-height: 70px;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>td:first-child, table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>th:first-child {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>th:first-child:before {
    top: 25px;
    right: unset !important;
    left: 30px !important;
    height: 14px;
    width: 14px;
    display: block;
    position: absolute;
    color: #fff;
    border: 2px solid #fff;
    border-radius: 14px;
    box-shadow: 0 0 3px #444;
    box-sizing: content-box;
    text-align: center;
    text-indent: 0!important;
    font-family: sans-serif;
    line-height: 15px;
    font-weight: normal;
    content: "+";
    background-color: #00B5B8;
}
table.dataTable.dtr-inline.collapsed>tbody>tr.parent>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr.parent>th:first-child:before {
    background-color: #FF7588;
    content: "-";
    line-height: 14px;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: 1px solid #efefef;
    padding: .5em 0;
    list-style: none;
}
.dtr-title {
    font-weight: bold;
    margin-left: 5px;
}
.dtr-title:after {
    line-height: 14px;
    content: ":";
}
.dataTables_filter label , .dataTables_length , .dataTables_length label{
    text-align: right;
    width: 100%;
}
.dataTables_filter input , .dataTables_length select{
    margin-top: 5px;
    margin-left: 0px !important;
    margin-right: 0px !important;
    width: 100%;
    margin-bottom: 15px;
}
.dataTables_info{
    width: 100%;
    margin-top: 20px;
}
.dataTables_paginate {
    width: 100%;
    margin-top: 20px;
}
@media(min-width:768px){
    .dataTables_paginate .pagination{
        float: left;
    }
}
span.badge{
    width: 100%;
    display:inline-block;
    padding: 10px 0px;
}
.file-box{
    border: 1px solid #f1f5fa;
    border-radius: 5px;
    padding: 20px;
    width: 100%;
    display: inline-block;
    margin-right: 5px;
    margin-bottom: 16px;
    background-color: #fff;
    -webkit-box-shadow: 0px 2px 4px rgba(31,30,47,0.1);
    box-shadow: 0px 2px 4px rgba(31,30,47,0.1);
}
.file-box i {
    font-size: 36px;
}
.notification-list .noti-icon-badge {
    right: auto;
    left: 8px;
    width: unset;
}
.swal2-title{
    font-size: 20px !important;
}
.swal2-actions button {
    padding: 5px 15px !important;
    font-size: 14px !important;
}
.mrb_10{
    margin-bottom: 15px;
}
@media(max-width:768px){
    table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>th:first-child:before {
        top: 13px;
    }
}
#ACEdit_Res{
    padding: 20px 10px;
    position: relative;
}
.closemod{
    position: absolute;
    left: 15px;
    top: 10px;
    color: #fd3c97 !important;
    display: inline-block;
    font-size: 20px;
    z-index: 1000;
    cursor: pointer;
}
.dis{
    pointer-events: none;
    opacity: 0.7;
}
@media(max-width:768px){
    table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role=row]>th:first-child:before {
        top: 13px;
    }
}
.sorts{
    cursor: grab;
}
.dropzone {
    min-height: 100px;
    border: 1px dashed #b3b3b3;
    background: #f3f3f33b;
    padding: 0;
    margin-bottom: 10px;
}
.dropzone h5{margin-top: 42px; color: #b3b3b3; font-weight: normal;display: inline-block;}

.postimg{
    width: 100%;
    height: 200px;
    padding: 2px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
}
.bwh{
    color: #fff !important;
    font-weight: bold;
    font-size: 16px;
}
.bwh .fa{
    margin-left: 5px;
}
textarea{
    min-height: 150px;
}
.editor{ background: #fff; color:#333;}
.editor strong , .editor h1 , .editor h2 , .editor h3 , .editor h4 , .editor h5 ,.editor h6 {font-weight: bold !important;} 
.tools{
    width: 100%;display: inline-block;background: #fff;padding: 5px 10px;border: 1px solid #ddd;border-bottom: 0;border-top-right-radius: 5px;border-top-left-radius: 5px;font-size:12px;
}
.status{
    width: 100%;display: inline-block;background: #f1f1f1;padding: 5px 5px 0px;color: #757575;font-weight: bold;font-size: 12px;
}
.tools a , .tools a:hover{
    color: #616161;margin: 5px;font-weight: bold;font-size: 12px;cursor: pointer;
}
.tools a i{
   margin-left: 5px;
}
.save_draft{
    font-size: 10px !important;position: absolute;left: 10px;top: 0px;font-weight: normal !important;color: #f90 !important;
}
.pad5{padding: 5px;}
.ck-editor__editable { min-height: 350px !important; max-height: 350px !important; border: 1px solid #c4c4c4 !important;
border-top: 0 !important;}
.ck-toolbar{ direction: rtl !important; }
.ck.ck-dropdown .ck-dropdown__panel { right: 0 !important; }
.ck.ck-list__item { text-align: right !important;font-family: "cairo", sans-serif !important; }
.ck.ck-dropdown .ck-dropdown__arrow { left: var(--ck-spacing-standard) !important; width: var(--ck-dropdown-arrow-size) !important; }
.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__button .ck-button__label {
width: 8em !important;
text-align: right !important;font-family: "cairo", sans-serif !important;
}
.ck-rounded-corners .ck.ck-tooltip .ck-tooltip__text, .ck.ck-tooltip .ck-tooltip__text.ck-rounded-corners { border-radius: var(--ck-border-radius); font-family: "cairo", sans-serif !important;}
.ck-rounded-corners .ck.ck-toolbar, .ck.ck-toolbar.ck-rounded-corners{border-radius: 0 !important;}
.ck-content a { color: #00adff !important;}
.ck-content{font-size: 18px;}
.ck-content .image>figcaption {color: #333;background-color: #f7f7f7;padding: .6em;font-size: .75em;outline-offset: -1px;display: none;}
#linkchecker{
    margin-right: 10px;
    color: #ff2b52;
    display: inline-block;
}
.padrl20{
    padding: 5px 30px;
    margin-top: 15px;
}
.ltr{
    text-align: left;
    direction: ltr;
}
.catphoto{
    width: 300px;
    max-width: 100%;
}
.showmoreless {
    position: absolute;
    left: 20px;
    top: 20px;
    font-size: 25px;
    cursor: pointer;
}
.dn{
    display: none;
}
.sh{
    display: block;
}
.orderinput{
    width: 80px;
    height: 30px;
    text-align: center;
    position: absolute;
    margin-right: 5px;
    margin-left: 5px;
}
.checkbox-div{
    display: inline-block;
    margin: 5px 10px;
}
.profile{
    width: 50px;
    height: 50px;
    display: inline-block;
    margin-left: 10px;
    border-radius: 50%;
    border: 1px solid #b5b5b5;
    padding: 1px;
}
.form-check-input {
    margin-left: 0;
    margin-right: -30px;
    width: 20px;
    height: 20px;
    margin-top: 10px;
}
.form-check {
    padding-right: 30px;
    padding-left: 0;
}
.mb-20{
    margin-bottom: 20px;
}
.m-05{
    margin: 5px;
}
.card {
    -webkit-box-shadow: 0px 2px 4px rgba(31,30,47,0.1);
    box-shadow: 0px 2px 15px rgb(31 30 47 / 15%);
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 0.25rem;
}
.service-section , .service-box , .service-data{
    width: 100%;
    display: inline-block;
}
.deleteSection {
    position: absolute;
    left: 60px;
    top: 22px;
    font-size: 20px;
    cursor: pointer;
    color: #E91E63 !important;
}
.text-end-flout{
    float: left;
    cursor: pointer;
    font-size: 26px;
}
h6 {
    font-size: 14px;
    font-weight: bold;
}
.pr , .prx{
    position: relative;
    padding-left: 20px;
}
.remibtn{
    position: absolute;
    left: 2px;
    top: 31px;
}
.remibtn .text-end-flout{
    font-size: 20px;
    color: #E91E63 !important;
}
.prx .text-end-flout{
    color: #FF5722  !important;
}