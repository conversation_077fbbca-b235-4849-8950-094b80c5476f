<?php
include('webset.php');

echo "<h2>اختبار الحجز الكامل</h2>";

try {
    // الخطوة 1: جلب رحلة للاختبار
    echo "<h3>الخطوة 1: جلب رحلة للاختبار</h3>";
    $stmt = $db->query("SELECT * FROM bus_trips WHERE trip_type = 'to_mecca' LIMIT 1");
    $trip = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$trip) {
        throw new Exception("لا توجد رحلات في قاعدة البيانات");
    }
    
    echo "تم العثور على رحلة: " . $trip['from_city'] . " → " . $trip['to_city'] . "<br>";
    echo "ID: " . $trip['id'] . "<br>";
    echo "السعر: " . $trip['seat_price'] . " ريال<br>";
    echo "المقاعد المتاحة: " . $trip['total_seats'] . "<br><br>";
    
    // الخطوة 2: محاكاة بيانات الحجز
    echo "<h3>الخطوة 2: محاكاة بيانات الحجز</h3>";
    $booking_data = [
        'trip_id' => $trip['id'],
        'travel_date' => date('Y-m-d', strtotime('+1 day')),
        'seats_count' => 2,
        'total_amount' => $trip['seat_price'] * 2,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'phone' => '0501234567',
        'email' => '<EMAIL>',
        'id_number' => '1234567890',
        'nationality' => 'سعودي',
        'notes' => 'اختبار الحجز'
    ];
    
    echo "بيانات الحجز:<br>";
    foreach ($booking_data as $key => $value) {
        echo "$key: $value<br>";
    }
    echo "<br>";
    
    // الخطوة 3: محاكاة عملية الحجز
    echo "<h3>الخطوة 3: محاكاة عملية الحجز</h3>";
    
    // بدء المعاملة
    $db->beginTransaction();
    
    // التحقق من توفر المقاعد
    $stmt = $db->prepare("SELECT COALESCE(SUM(seats_count), 0) as booked_seats
                         FROM bus_bookings
                         WHERE trip_id = ? AND travel_date = ? AND booking_status != 'cancelled'");
    $stmt->execute([$booking_data['trip_id'], $booking_data['travel_date']]);
    $booked_seats = $stmt->fetchColumn();
    
    $available_seats = $trip['total_seats'] - $booked_seats;
    echo "المقاعد المحجوزة: $booked_seats<br>";
    echo "المقاعد المتاحة: $available_seats<br>";
    
    if ($available_seats < $booking_data['seats_count']) {
        throw new Exception('لا توجد مقاعد كافية متاحة');
    }
    
    // إنشاء رقم مرجعي
    $booking_reference = 'BUS' . date('Ymd') . sprintf('%04d', rand(1000, 9999));
    echo "رقم الحجز المرجعي: $booking_reference<br>";
    
    // إدراج الحجز
    $passenger_name = $booking_data['first_name'] . ' ' . $booking_data['last_name'];
    $stmt = $db->prepare("INSERT INTO bus_bookings
                         (trip_id, passenger_name, passenger_phone, passenger_email,
                          travel_date, seats_count, total_amount, notes, booking_reference, booking_status)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed')");

    $result = $stmt->execute([
        $booking_data['trip_id'],
        $passenger_name,
        $booking_data['phone'],
        $booking_data['email'],
        $booking_data['travel_date'],
        $booking_data['seats_count'],
        $booking_data['total_amount'],
        $booking_data['notes'],
        $booking_reference
    ]);
    
    if ($result) {
        $booking_id = $db->lastInsertId();
        echo "تم إدراج الحجز بنجاح! ID: $booking_id<br>";
        
        // تأكيد المعاملة
        $db->commit();
        echo "<div style='color: green; font-weight: bold;'>✅ تم الحجز بنجاح!</div><br>";
        
        // الخطوة 4: التحقق من الحجز
        echo "<h3>الخطوة 4: التحقق من الحجز</h3>";
        $stmt = $db->prepare("SELECT * FROM bus_bookings WHERE id = ?");
        $stmt->execute([$booking_id]);
        $saved_booking = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($saved_booking) {
            echo "تم العثور على الحجز في قاعدة البيانات:<br>";
            echo "رقم الحجز: " . $saved_booking['booking_reference'] . "<br>";
            echo "اسم المسافر: " . $saved_booking['passenger_name'] . "<br>";
            echo "تاريخ السفر: " . $saved_booking['travel_date'] . "<br>";
            echo "عدد المقاعد: " . $saved_booking['seats_count'] . "<br>";
            echo "المبلغ الإجمالي: " . $saved_booking['total_amount'] . " ريال<br>";
            echo "حالة الحجز: " . $saved_booking['booking_status'] . "<br>";
            
            echo "<br><a href='booking-confirmation.php?ref=" . $saved_booking['booking_reference'] . "' target='_blank' class='btn btn-primary'>عرض صفحة التأكيد</a>";
        }
        
    } else {
        throw new Exception('فشل في إدراج الحجز');
    }
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if ($db->inTransaction()) {
        $db->rollback();
    }
    echo "<div style='color: red; font-weight: bold;'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
.btn { 
    display: inline-block; 
    padding: 10px 20px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px; 
    margin-top: 10px;
}
</style>
