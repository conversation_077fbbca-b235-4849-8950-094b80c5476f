<?php
include('webset.php');

// تعيين header للاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// تسجيل الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 0); // لا نريد عرض الأخطاء في JSON

// دالة لتسجيل الأخطاء
function logError($message, $data = null) {
    $log_entry = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $log_entry .= " - Data: " . json_encode($data);
    }
    error_log($log_entry . "\n", 3, "booking_errors.log");
}

// دالة لإنشاء رقم مرجعي للحجز
function generateBookingReference() {
    return 'HTL' . date('Ymd') . rand(1000, 9999);
}

// دالة لتسجيل العمليات
function logBookingAction($booking_id, $action, $details = null) {
    try {
        global $db;

        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        // التحقق من وجود الجدول أولاً
        $table_check = $db->query("SHOW TABLES LIKE 'booking_logs'")->rowCount();
        if ($table_check > 0) {
            $sql = "INSERT INTO booking_logs (booking_id, action, details, created_by_ip) VALUES (?, ?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$booking_id, $action, $details, $ip]);
        }
    } catch (Exception $e) {
        // تجاهل أخطاء التسجيل
        logError("Log action failed: " . $e->getMessage());
    }
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        exit;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'create_booking':
            try {
                logError("Booking request received", $input);

                // التحقق من البيانات المطلوبة
                $required_fields = ['hotel_id', 'room_type_index', 'room_name', 'room_price',
                                  'guest_name', 'guest_phone', 'check_in_date', 'check_out_date',
                                  'nights', 'room_count', 'total_cost'];

                foreach ($required_fields as $field) {
                    if (!isset($input[$field]) || $input[$field] === '' || $input[$field] === null) {
                        logError("Missing required field: " . $field, $input);
                        throw new Exception("الحقل {$field} مطلوب");
                    }
                }
                
                // التحقق من صحة التواريخ
                $check_in = new DateTime($input['check_in_date']);
                $check_out = new DateTime($input['check_out_date']);
                $today = new DateTime();
                
                if ($check_in < $today) {
                    throw new Exception("تاريخ تسجيل الدخول لا يمكن أن يكون في الماضي");
                }
                
                if ($check_out <= $check_in) {
                    throw new Exception("تاريخ تسجيل الخروج يجب أن يكون بعد تاريخ الدخول");
                }
                
                // التحقق من وجود الفندق
                $hotel_sql = "SELECT * FROM hotels_booking WHERE id = ? AND status = 1 AND booking_enabled = 1";
                $hotel_stmt = $db->prepare($hotel_sql);
                $hotel_stmt->execute([$input['hotel_id']]);
                $hotel_check = $hotel_stmt->fetchAll(PDO::FETCH_ASSOC);

                if (count($hotel_check) == 0) {
                    throw new Exception("الفندق غير متاح للحجز");
                }
                
                // إنشاء رقم مرجعي فريد
                do {
                    $booking_reference = generateBookingReference();
                    $ref_sql = "SELECT id FROM hotel_bookings WHERE booking_reference = ?";
                    $ref_stmt = $db->prepare($ref_sql);
                    $ref_stmt->execute([$booking_reference]);
                    $ref_check = $ref_stmt->fetchAll();
                } while (count($ref_check) > 0);
                
                // حساب التكلفة للتأكد
                $calculated_cost = floatval($input['room_price']) * intval($input['nights']) * intval($input['room_count']);
                $submitted_cost_text = $input['total_cost'];
                $submitted_cost = floatval(preg_replace('/[^0-9.]/', '', $submitted_cost_text));

                logError("Cost calculation", [
                    'room_price' => $input['room_price'],
                    'nights' => $input['nights'],
                    'room_count' => $input['room_count'],
                    'calculated_cost' => $calculated_cost,
                    'submitted_cost_text' => $submitted_cost_text,
                    'submitted_cost' => $submitted_cost
                ]);

                // السماح بفرق بسيط في التكلفة (تقريب)
                if (abs($calculated_cost - $submitted_cost) > 5) {
                    throw new Exception("خطأ في حساب التكلفة الإجمالية. المحسوب: {$calculated_cost}, المرسل: {$submitted_cost}");
                }
                
                // إدراج الحجز في قاعدة البيانات
                $sql = "INSERT INTO hotel_bookings (
                    booking_reference, hotel_id, room_type_index, room_name, room_price,
                    guest_name, guest_phone, guest_email, guest_count,
                    check_in_date, check_out_date, nights, room_count, total_cost,
                    booking_status, payment_status, special_requests, created_by_ip
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending', ?, ?)";
                
                $stmt = $db->prepare($sql);
                $result = $stmt->execute([
                    $booking_reference,
                    $input['hotel_id'],
                    $input['room_type_index'],
                    $input['room_name'],
                    $input['room_price'],
                    $input['guest_name'],
                    $input['guest_phone'],
                    $input['guest_email'] ?? null,
                    $input['guest_count'] ?? 1,
                    $input['check_in_date'],
                    $input['check_out_date'],
                    $input['nights'],
                    $input['room_count'],
                    $calculated_cost,
                    $input['special_requests'] ?? null,
                    $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                
                if ($result) {
                    $booking_id = $db->lastInsertId();
                    
                    // تسجيل العملية
                    logBookingAction($booking_id, 'booking_created', 'حجز جديد تم إنشاؤه');
                    
                    // إرسال استجابة نجح
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم إنشاء الحجز بنجاح',
                        'booking_id' => $booking_id,
                        'booking_reference' => $booking_reference,
                        'data' => [
                            'hotel_name' => $hotel_check[0]['name'],
                            'room_name' => $input['room_name'],
                            'check_in_date' => $input['check_in_date'],
                            'check_out_date' => $input['check_out_date'],
                            'nights' => $input['nights'],
                            'room_count' => $input['room_count'],
                            'guest_count' => $input['guest_count'],
                            'total_cost' => $calculated_cost,
                            'guest_name' => $input['guest_name'],
                            'guest_phone' => $input['guest_phone']
                        ]
                    ]);
                } else {
                    throw new Exception("فشل في حفظ الحجز");
                }
                
            } catch (Exception $e) {
                logError("Booking creation failed", [
                    'error' => $e->getMessage(),
                    'input_data' => $input
                ]);

                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'error_code' => 'BOOKING_FAILED'
                ]);
            } catch (PDOException $e) {
                logError("Database error in booking", [
                    'error' => $e->getMessage(),
                    'input_data' => $input
                ]);

                echo json_encode([
                    'success' => false,
                    'message' => 'خطأ في قاعدة البيانات، يرجى المحاولة مرة أخرى',
                    'error_code' => 'DATABASE_ERROR'
                ]);
            }
            break;
            
        case 'get_booking':
            try {
                $booking_reference = $input['booking_reference'] ?? '';
                
                if (empty($booking_reference)) {
                    throw new Exception("رقم الحجز مطلوب");
                }
                
                $booking = getAllFrom('hb.*, h.name as hotel_name, hc.city_name', 
                                    'hotel_bookings hb 
                                     JOIN hotels_booking h ON hb.hotel_id = h.id 
                                     JOIN hotel_city hc ON h.hotel_city_id = hc.id', 
                                    'WHERE hb.booking_reference = ?', '', [$booking_reference]);
                
                if (count($booking) == 0) {
                    throw new Exception("الحجز غير موجود");
                }
                
                echo json_encode([
                    'success' => true,
                    'data' => $booking[0]
                ]);
                
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'عملية غير مدعومة'
            ]);
            break;
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة'
    ]);
}
?>
