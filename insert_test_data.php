<?php
include('webset.php');

try {
    // حذف البيانات القديمة
    $db->exec("DELETE FROM bus_trips");
    
    // إضافة الأعمدة الجديدة إذا لم تكن موجودة
    try {
        $db->exec("ALTER TABLE bus_trips ADD COLUMN departure_location TEXT AFTER from_station");
    } catch (Exception $e) {
        // العمود موجود بالفعل
    }

    try {
        $db->exec("ALTER TABLE bus_trips ADD COLUMN departure_address TEXT AFTER departure_location");
    } catch (Exception $e) {
        // العمود موجود بالفعل
    }

    try {
        $db->exec("ALTER TABLE bus_trips ADD COLUMN arrival_address TEXT AFTER to_station");
    } catch (Exception $e) {
        // العمود موجود بالفعل
    }

    // إدراج بيانات تجريبية باللغة الإنجليزية للاختبار
    $trips = [
        // رحلات الذهاب إلى مكة
        ['Riyadh', 'Al-Batha Station', 'https://maps.google.com/?q=24.7136,46.6753', 'Al-Batha Bus Terminal, King Abdulaziz Road, Al-Batha District, Riyadh 12632', 'Makkah', 'Ibrahim Al-Khalil Street', 'Makkah Central Bus Terminal, Ibrahim Al-Khalil Street, Makkah 24231', '13:30:00', '17:30:00', 100.00, 50, 'to_mecca', '0', 1],
        ['Riyadh', 'Al-Batha Station', 'https://maps.google.com/?q=24.7136,46.6753', 'Al-Batha Bus Terminal, King Abdulaziz Road, Al-Batha District, Riyadh 12632', 'Makkah', 'Ibrahim Al-Khalil Street', 'Makkah Central Bus Terminal, Ibrahim Al-Khalil Street, Makkah 24231', '20:00:00', '00:00:00', 100.00, 50, 'to_mecca', '0', 1],
        ['Jeddah', 'Jeddah Central Station', 'https://maps.google.com/?q=21.4858,39.1925', 'Jeddah Central Bus Station, Prince Majed Road, Al-Salamah District, Jeddah 23436', 'Makkah', 'Ibrahim Al-Khalil Street', 'Makkah Central Bus Terminal, Ibrahim Al-Khalil Street, Makkah 24231', '14:00:00', '15:30:00', 50.00, 45, 'to_mecca', '0', 1],
        ['Dammam', 'Dammam Central Station', 'https://maps.google.com/?q=26.4207,50.0888', 'Dammam Bus Terminal, King Fahd Road, Al-Faisaliyah District, Dammam 32245', 'Makkah', 'Ibrahim Al-Khalil Street', 'Makkah Central Bus Terminal, Ibrahim Al-Khalil Street, Makkah 24231', '10:00:00', '16:00:00', 150.00, 40, 'to_mecca', '0', 1],

        // رحلات العودة من مكة
        ['Makkah', 'Mahbas Al-Jin Station', 'https://maps.google.com/?q=21.3891,39.8579', 'Mahbas Al-Jin Bus Station, near Concord Hotel, Makkah 24231', 'Riyadh', 'Al-Batha Station', 'Al-Batha Bus Terminal, King Abdulaziz Road, Al-Batha District, Riyadh 12632', '13:30:00', '17:30:00', 100.00, 50, 'from_mecca', '0', 1],
        ['Makkah', 'Ibrahim Al-Khalil Street', 'https://maps.google.com/?q=21.3891,39.8579', 'Makkah Central Bus Terminal, Ibrahim Al-Khalil Street, Makkah 24231', 'Jeddah', 'Jeddah Central Station', 'Jeddah Central Bus Station, Prince Majed Road, Al-Salamah District, Jeddah 23436', '15:00:00', '16:30:00', 50.00, 45, 'from_mecca', '0', 1],
        ['Makkah', 'Ibrahim Al-Khalil Street', 'https://maps.google.com/?q=21.3891,39.8579', 'Makkah Central Bus Terminal, Ibrahim Al-Khalil Street, Makkah 24231', 'Dammam', 'Dammam Central Station', 'Dammam Bus Terminal, King Fahd Road, Al-Faisaliyah District, Dammam 32245', '16:00:00', '22:00:00', 150.00, 40, 'from_mecca', '0', 1]
    ];
    
    $stmt = $db->prepare("INSERT INTO bus_trips (from_city, from_station, departure_location, departure_address, to_city, to_station, arrival_address, departure_time, arrival_time, seat_price, total_seats, trip_type, available_days, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($trips as $trip) {
        $stmt->execute($trip);
    }
    
    echo "تم إدراج البيانات التجريبية بنجاح!<br>";
    echo "عدد الرحلات المدرجة: " . count($trips) . "<br>";
    
    // التحقق من البيانات
    $stmt = $db->query("SELECT COUNT(*) as total FROM bus_trips");
    $total = $stmt->fetchColumn();
    echo "إجمالي الرحلات في قاعدة البيانات: " . $total . "<br>";
    
    // عرض بعض البيانات للتأكد
    $stmt = $db->query("SELECT id, from_city, from_station, to_city, trip_type, status FROM bus_trips LIMIT 5");
    echo "<h3>عينة من البيانات:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>From City</th><th>From Station</th><th>To City</th><th>Trip Type</th><th>Status</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['from_city'] . "</td>";
        echo "<td>" . $row['from_station'] . "</td>";
        echo "<td>" . $row['to_city'] . "</td>";
        echo "<td>" . $row['trip_type'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
