<?php
ob_start();
$Title_page = 'الحملات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
$info = '[]';
$price = '[]';
$aprice = '[]';

//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])){

    $hotels = getAllFrom('*' , 'hotels' , '', '');

	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){ 
        $hot = $ch[0]['hotelid'] != null && $ch[0]['hotelid'] != '' ? json_decode($ch[0]['hotelid']) : '[]';
        $info =  $ch[0]['info'] != null && $ch[0]['info'] != '' ? $ch[0]['info'] : '[]';
        $price =  $ch[0]['price'] != null && $ch[0]['price'] != '' ? $ch[0]['price'] : '[]';
        $aprice = $ch[0]['aprice'] != null &&  $ch[0]['aprice'] != '' ? $ch[0]['aprice'] : '[]';
        
        $days = $ch[0]['days'] != null &&  $ch[0]['days'] != '' ? json_decode($ch[0]['days']) : [];
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
                    <h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على <b>"<?php echo $ch[0]['title'];?>"</b></h4>
                    <a style="color:#fff" href="offers.php" class="btn btn-sm btn-gradient-danger waves-effect waves-light mrb_10 padrl20">رجوع</a>
				</div>
            </div>
        </div> 
    </div>

    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <input value="<?php echo $ch[0]['id'];?>" class="form-control" type="hidden" id="var0">
                        <div class="col-md-4">
                            <div class="form-group">
                            <label class="form-control-label">عنوان الحملة</label>
                                <input type="text" value="<?php echo $ch[0]['title'];?>" id="var1" class="form-control">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                            <label class="form-control-label">المحافظة</label>
                            <select id="var2" class="form-control">
                                <option value="0">إختر المحافظة</option>
                            <?php
                            for ($i=0; $i < count($AllCities) ; $i++) { 
                                $sel = $ch[0]['catid'] == $AllCities[$i]['id'] ? 'selected' : '';
                                echo '<option value="'.$AllCities[$i]['id'].'" '.$sel.'>'.$AllCities[$i]['name'].'</option>';
                            }
                            ?>
                            </select> 
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                            <label class="form-control-label">النوع</label>
                            <select id="var3" class="form-control">
                                <option value="0" <?php if($ch[0]['vip'] == '0'){ echo 'selected';}?>>اقتصادية</option>
                                <option value="1" <?php if($ch[0]['vip'] == '1'){ echo 'selected';}?>>VIP</option>
                             
                            </select> 
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                            <label class="form-control-label">المدة</label>
                                <input type="number" min="1" value="<?php echo $ch[0]['duration'];?>" id="var4" class="form-control">
                            </div>
                        </div>
                         
                        <div class="col-md-4">
                            <div class="form-group">
                            <label class="form-control-label">فندق مكة</label>
                            <select id="var5" class="form-control">
                                <option value="0">بدون فندق</option>
                            <?php
                            for ($i=0; $i < count($hotels) ; $i++) { 
                                $sel = $hot->k == $hotels[$i]['id'] ? 'selected' : '';
                                echo '<option value="'.$hotels[$i]['id'].'" '.$sel.'>'.$hotels[$i]['name'].'</option>';
                            }
                            ?>
                            </select> 
                            </div>
                        </div>


                        <div class="col-md-4">
                            <div class="form-group">
                            <label class="form-control-label">فندق المدينة</label>
                            <select id="var6" class="form-control">
                                <option value="0">بدون فندق</option>
                            <?php
                            for ($i=0; $i < count($hotels) ; $i++) { 
                                $sel = $hot->d == $hotels[$i]['id'] ? 'selected' : '';
                                echo '<option value="'.$hotels[$i]['id'].'" '.$sel.'>'.$hotels[$i]['name'].'</option>';
                            }
                            ?>
                            </select> 
                            </div>
                        </div>
 
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-control-label">ايام الحملة</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" <?php if(in_array(6 , $days)){echo 'checked';} ?> value="6" name="days" id="chb6">
                                <label class="form-check-label" for="chb6">
                                    السبت   
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" <?php if(in_array(0 , $days)){echo 'checked';} ?> value="0" name="days" id="chb0">
                                <label class="form-check-label" for="chb0">
                                    الاحد
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" <?php if(in_array(1 , $days)){echo 'checked';} ?> value="1" name="days" id="chb1">
                                <label class="form-check-label" for="chb1">
                                    الاثنين
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" <?php if(in_array(2 , $days)){echo 'checked';} ?> value="2" name="days" id="chb2">
                                <label class="form-check-label" for="chb2">
                                    الثلاثاء
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" <?php if(in_array(3 , $days)){echo 'checked';} ?> value="3" name="days" id="chb3">
                                <label class="form-check-label" for="chb3">
                                    الاربعاء
                                </label>
                            </div>
                        
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" <?php if(in_array(4 , $days)){echo 'checked';} ?> value="4" name="days" id="chb4">
                                <label class="form-check-label" for="chb4">
                                    الخميس
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" <?php if(in_array(5 , $days)){echo 'checked';} ?> value="5" name="days" id="chb5">
                                <label class="form-check-label" for="chb5">
                                    الجمعه
                                </label>
                            </div>
                            </div>

 
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
                    <h6 class="mb-20">المعلومات <a onclick="AddInput()"><span class="text-end-flout"><i class="icoz mdi mdi-plus-box"></i></span></a></h6>	
                    <div class="info-res"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
                    <h6 class="mb-20">السعر <a onclick="AddPriceInput()"><span class="text-end-flout"><i class="icoz mdi mdi-plus-box"></i></span></a></h6>	
                    <div class="price-res"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
                    <h6 class="mb-20">الأسعار <a onclick="AddAPriceInput()"><span class="text-end-flout"><i class="icoz mdi mdi-plus-box"></i></span></a></h6>	
                    <div class="aprice-res"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <button onclick="SaveEdit()" id="saveBtn" class="btn btn-gradient-primary btn-round  waves-effect waves-light"><i class="icoz mdi mdi-file-document-box"></i> حفظ التعديلات </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> 
	<?php	
	}else{
		header('Location: offers.php'); exit();
    } 	
//---------------------------------------------------	
}else{ 
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
                <a onclick="AddNewOffer()" style="color:#fff" class="btn btn-md btn-gradient-primary waves-effect waves-light">إضافة حملة جديده</a>
			</div>
		</div><hr>
	</div>			
</div>
<div class="row">
    <div class="col-12">
        <div id="res"></div>
    </div>
    <!-- end col -->
</div>

 
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>
    var infoJson = JSON.parse(`<?php echo $info;?>`);
    var priceJson = JSON.parse(`<?php echo $price;?>`);
    var apriceJson = JSON.parse(`<?php echo $aprice;?>`);

    function GetCatoffers(){
        $('#res').html('');
        $.post("ajax.php", { action : 'Getoffers' } ,function(data){ 
            $('#res').html(data);
        });
    }  
    GetCatoffers(); 

    function AddNewOffer(){
        Swal({
        title: 'هل انت متأكد؟',
        text: 'هل انت متاكد من انك تريد اضافة حملة جديده؟',
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
            $.post("ajax.php", { action : 'AddNewOffer' } ,function(datax){ 
                Swal({
                    title: 'تم الإجراء بنجاح',
                    text: "",
                    type: 'success',
                    confirmButtonColor: '#3085d6',
                    confirmButtonText: 'إنهاء',
                });
                if(datax != '' && datax > 0){
                    setTimeout(() => {
                        window.location.href = site_url + '/admincp/offers.php?action=edit&id='+datax;
                    }, 1000);
                }
            }); 
        }
      })
    }

    function ShowHideOfferAction(pageid , type){
        if (type == 1){
            var txt = "برجاء العلم انه سيتم عرض الحملة.";
        }else{
            var txt = "برجاء العلم انه سيتم إخفاء الحملة.";
        }
        Swal({
        title: 'هل انت متأكد؟',
        text: txt,
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'ShowHideOfferAction',pageid : pageid ,type : type} ,function(data){ 
            Swal({
              title: 'تم الإجراء بنجاح',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetCatoffers();
          });  
          
        }
      })
    }

    function DelOfferAction(pageid){
        Swal({
        title: 'هل أنت متأكد انك تريد حذف هذه الحملة؟',
        text: "برجاء العلم انه سيتم الحذف بشكل نهائي",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
      }).then((result) => {
        if (result.value) {
          $.post("ajax.php", { action : 'DelOfferAction',pageid : pageid} ,function(data){ 
            Swal({
              title: 'تم الحذف',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إنهاء',
            })
            GetCatoffers();
          });  
          
        }
      })
    }

    function getInfoInput(){
        if(infoJson.length <= 0){
            $('.info-res').html('<div class="alert alert-info role="alert">لا يوجد اى معلومات عن الرحلة</div></div>');
        }else{
            var html = '';
            for (let i = 0; i < infoJson.length; i++) {
                html += `
                <div class="row pr" elm="${infoJson[i].key}">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-control-label">العنوان</label>
                            <input onchange="SetInputValue()" type="text" value="${infoJson[i].key}" name="key" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-control-label">القيمه</label>
                            <input onchange="SetInputValue()" type="text" value="${infoJson[i].val}" name="val" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-control-label">الايقونة</label>
                            <input onchange="SetInputValue()" type="text" value="${infoJson[i].ico}" name="ico" class="form-control ltr">
                        </div>
                    </div>
                     
                    <a onclick="RemoveInput(this)" class="remibtn"><span class="text-end-flout"><i class="icoz mdi mdi-delete"></i></span></a> 
                </div>
                `;
            }
            $('.info-res').html(html);
        } 
    }
    getInfoInput();

    function RemoveInput(elm){
        infoJson = $.grep(infoJson, function(item) {
            return item.key !== $(elm).closest('.pr').attr('elm');
        });
		getInfoInput();
	}

    function AddInput(){
        infoJson.push({key:'' , val:'' , ico: 'fa fa-kaaba'});
        getInfoInput();
    }

    function getPriceInput(){
        if(priceJson.length <= 0){
            $('.price-res').html('<div class="alert alert-info role="alert">لا يوجد اى اسعار</div></div>');
        }else{
            var html = '';
            for (let i = 0; i < priceJson.length; i++) {
                html += `
                <div class="row pr" elm="${priceJson[i].name}">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-control-label">العنوان</label>
                            <input onchange="SetInputValue()" type="text" value="${priceJson[i].name}" name="name" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-control-label">القيمه</label>
                            <input onchange="SetInputValue()" type="text" value="${priceJson[i].descr}" name="descr" class="form-control">
                        </div>
                    </div> 
                    <a onclick="RemovePriceInput(this)" class="remibtn"><span class="text-end-flout"><i class="icoz mdi mdi-delete"></i></span></a> 
                </div>
                `;
            }
            $('.price-res').html(html);
        } 
    }
    getPriceInput();

    function RemovePriceInput(elm){
        priceJson = $.grep(priceJson, function(item) {
            return item.name !== $(elm).closest('.pr').attr('elm');
        });
		getPriceInput();
	}

    function AddPriceInput(){
        priceJson.push({name:'' , descr:''});
        getPriceInput();
    }



    function getaPriceInput(){
        if(apriceJson.length <= 0){
            $('.aprice-res').html('<div class="alert alert-info role="alert">لا يوجد اى اسعار</div></div>');
        }else{
            var html = '';
            for (let i = 0; i < apriceJson.length; i++) {
                var apinfo = '<br>'; 
                if(apriceJson[i].info != undefined){
                    for (let x = 0; x < apriceJson[i].info.length; x++) {
                        apinfo += `
                        <div class="row prx" index="${i}" elmx="${apriceJson[i].info[x].name}">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-control-label">الإسم</label>
                                    <input onchange="SetInputValue()" type="text" value="${apriceJson[i].info[x].name}" name="name" class="form-control">
                                </div>
                            </div> 
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-control-label">الأيقونة</label>
                                    <input onchange="SetInputValue()" type="text" value="${apriceJson[i].info[x].icon}" name="icon" class="form-control ltr">
                                </div>
                            </div>
                        
                        <a onclick="RemoveAPriceInfoInput(this)" class="remibtn"><span class="text-end-flout"><i class="icoz mdi mdi-delete"></i></span></a>  
                        </div>
                        `;
                    }
                }


                html += `
                <div class="row pr" elm="${apriceJson[i].title}">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-control-label"><a onclick="AddAPriceInfoInput(${i})"><i class="icoz mdi mdi-plus-box"></i></a> العنوان</label>
                            <input onchange="SetInputValue()" type="text" value="${apriceJson[i].title}" name="title" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-control-label">القيمه</label>
                            <input onchange="SetInputValue()" type="text" value="${apriceJson[i].desc}" name="desc" class="form-control">
                        </div>
                    </div> 
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-control-label">الأيقونة</label>
                            <input onchange="SetInputValue()" type="text" value="${apriceJson[i].icon}" name="icon" class="form-control ltr">
                        </div>
                    </div>
                    <a onclick="RemoveAPriceInput(this)" class="remibtn"><span class="text-end-flout"><i class="icoz mdi mdi-delete"></i></span></a>  
                </div>
                ${apinfo}
                <hr>
                `;
            }
            $('.aprice-res').html(html);
        } 
    }
    getaPriceInput();

    function RemoveAPriceInput(elm){
        apriceJson = $.grep(apriceJson, function(item) {
            return item.title !== $(elm).closest('.pr').attr('elm');
        });
		getaPriceInput();
	}

    function AddAPriceInput(){
        apriceJson.push({title:'' , desc:'' , icon: 'fa fa-bus-alt' , info:[]});
        getaPriceInput();
    } 

    function RemoveAPriceInfoInput(elm){
        var index = $(elm).closest('.prx').attr('index');
        apriceJson[index].info = $.grep(apriceJson[index].info, function(item) {
            return item.name !== $(elm).closest('.prx').attr('elmx');
        }); 
        getaPriceInput(); 
    }

    function AddAPriceInfoInput(index){
         
        apriceJson[index].info.push({name:'' , icon: 'fa fa-certificate'});
        getaPriceInput();
    } 
    
    function SetInputValue(){
        var tmp_i = []; 
        var tmp_p = [];
        var tmp_a = [];
        $('.info-res').find('.pr').each(function(i,elm){
            tmp_i.push({
                key : $(elm).find('input[name="key"]').val(),
                val : $(elm).find('input[name="val"]').val(),
                ico : $(elm).find('input[name="ico"]').val(),
            })
        });
        $('.price-res').find('.pr').each(function(i,elm){
            tmp_p.push({
                name : $(elm).find('input[name="name"]').val(),
                descr : $(elm).find('input[name="descr"]').val(), 
            })
        });
        $('.aprice-res').find('.pr').each(function(i,elm){
            var pinfo = [];

            $('.prx').each(function(x,elmx){
                var j = $(elmx).attr('index');
                if(j == i){
                    pinfo.push({
                        name : $(elmx).find('input[name="name"]').val(),
                        icon : $(elmx).find('input[name="icon"]').val(), 
                    });
                }
            });

            tmp_a.push({
                title : $(elm).find('input[name="title"]').val(),
                desc : $(elm).find('input[name="desc"]').val(), 
                icon : $(elm).find('input[name="icon"]').val(), 
                info : pinfo
            }); 
        });
        infoJson = tmp_i;
        priceJson = tmp_p;
        apriceJson = tmp_a;
    }
    


    function SaveEdit(){
        SetInputValue();
        $('#saveBtn').hide(100);
        var var0 = $('#var0').val(); 

        var catid = $('#var2').val();
        var title = $('#var1').val();
        var hotelid = {k: $('#var5').val(), d: $('#var6').val()};
        var duration = $('#var4').val();
        var vip = $('#var3').val();
        var days = [];
        
        $('input[name="days"]:checked').each(function(){
            days.push(parseInt($(this).val()));
        }); 
        $.post("ajax.php", { action : 'SaveOfferEdit' , var0 : var0 , catid : catid , title : title , hotelid : hotelid , duration : duration , info : infoJson , price : priceJson , aprice : apriceJson , vip : vip , days:days } ,function(data){
            //console.log(data);
           location.reload();
        });
        
        
    }
    
    $( function() {
        $( "#res" ).sortable({
            update: function(event, ui) {
                var ids = "";
                $(".sorts").each(function(){
                    if($(this).attr('id') != undefined){
                        ids = ids + "," +$(this).attr('id');
                    }
                })
                if (ids != ""){
                    $.post("ajax.php", { action : 'Sortoffers' , ids : ids } ,function(data){});
                }
            }
        });
        $( "#res" ).disableSelection(); 

        $( "#resx" ).sortable({
            update: function(event, ui) {
                 
            }
        });
        $( "#resx" ).disableSelection(); 
        
    }); 
   
</script>
