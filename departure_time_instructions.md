# تعليمات استخدام حقل ساعة المغادرة الجديد

## نظرة عامة
تم تحديث حقل `departure_time` في جدول `bus_trips` ليصبح حقل نص قابل للكتابة يدوياً بدلاً من حقل TIME المحدد.

## التغييرات المطبقة

### 1. هيكل قاعدة البيانات
- **الحقل القديم**: `departure_time TIME NOT NULL`
- **الحقل الجديد**: `departure_time VARCHAR(50) NOT NULL`
- **حقل إضافي**: `departure_sort_time TIME` (للترتيب)

### 2. أمثلة على الأوقات الجديدة
يمكنك الآن كتابة أوقات المغادرة بأي شكل تريده:

```
1.5 ظهراً
2:30 عصراً
10:00 صباحاً
8 مساءً
منتصف الليل
الفجر
بعد صلاة المغرب
```

## كيفية التحديث

### 1. تنفيذ التحديث
```bash
# افتح المتصفح واذهب إلى:
http://yoursite.com/update_departure_time.php

# اتبع التعليمات واضغط "تأكيد التحديث"
```

### 2. تحرير الأوقات في قاعدة البيانات
```sql
-- مثال على تحديث وقت المغادرة
UPDATE bus_trips 
SET departure_time = '1.5 ظهراً', 
    departure_sort_time = '13:30:00' 
WHERE id = 1;

-- مثال آخر
UPDATE bus_trips 
SET departure_time = 'بعد صلاة العصر', 
    departure_sort_time = '15:30:00' 
WHERE id = 2;
```

### 3. إضافة رحلة جديدة
```sql
INSERT INTO bus_trips (
    from_city, from_station, to_city, to_station, 
    departure_time, departure_sort_time, arrival_time, 
    seat_price, total_seats, trip_type
) VALUES (
    'الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل',
    '1.5 ظهراً', '13:30:00', '17:30:00',
    100.00, 50, 'to_mecca'
);
```

## الملفات المحدثة

### 1. bus-api.php
- تم تحديث استعلامات الترتيب لاستخدام `departure_sort_time`
- الآن يتم ترتيب الرحلات حسب الوقت الفعلي وليس النص

### 2. bus-booking.php
- تم تحديث دالة `formatTime()` لعرض النص مباشرة
- إذا كان النص يحتوي على كلمات عربية، يعرض كما هو
- إذا كان بصيغة TIME القديمة، يحوله تلقائياً

### 3. ملفات جديدة
- `update_departure_time_field.sql`: ملف SQL للتحديث
- `update_departure_time.php`: واجهة تنفيذ التحديث
- `departure_time_instructions.md`: هذا الملف

## نصائح مهمة

### 1. حقل الترتيب
- استخدم `departure_sort_time` للترتيب الصحيح للرحلات
- هذا الحقل يجب أن يحتوي على وقت بصيغة TIME صحيحة
- مثال: إذا كان `departure_time = "1.5 ظهراً"` فيجب أن يكون `departure_sort_time = "13:30:00"`

### 2. أمثلة على التوقيتات الشائعة
```
النص العربي          | وقت الترتيب
---------------------|-------------
"12:00 ظهراً"       | "12:00:00"
"1:30 ظهراً"        | "13:30:00"
"2:00 عصراً"         | "14:00:00"
"3:30 عصراً"         | "15:30:00"
"6:00 مساءً"         | "18:00:00"
"8:30 مساءً"         | "20:30:00"
"10:00 صباحاً"       | "10:00:00"
"منتصف الليل"        | "00:00:00"
```

### 3. نسخ احتياطية
- تأكد من عمل نسخة احتياطية قبل تنفيذ التحديث
- يمكنك استخدام: `mysqldump -u username -p database_name > backup.sql`

## استكشاف الأخطاء

### 1. إذا لم تظهر الأوقات بشكل صحيح
- تأكد من أن حقل `departure_time` يحتوي على نص صحيح
- تحقق من ترميز UTF-8 في قاعدة البيانات

### 2. إذا لم يتم ترتيب الرحلات بشكل صحيح
- تأكد من أن حقل `departure_sort_time` يحتوي على أوقات صحيحة
- استخدم صيغة TIME المعيارية: "HH:MM:SS"

### 3. إذا ظهرت أخطاء في JavaScript
- تأكد من أن النصوص العربية محاطة بعلامات اقتباس مزدوجة
- تحقق من ترميز الملف (UTF-8)

## أمثلة عملية

### تحديث جميع الرحلات الصباحية
```sql
UPDATE bus_trips 
SET departure_time = CONCAT(HOUR(departure_sort_time), ':00 صباحاً')
WHERE HOUR(departure_sort_time) BETWEEN 6 AND 11;
```

### تحديث جميع الرحلات المسائية
```sql
UPDATE bus_trips 
SET departure_time = CONCAT(HOUR(departure_sort_time) - 12, ':00 مساءً')
WHERE HOUR(departure_sort_time) BETWEEN 18 AND 23;
```

### البحث عن رحلات بوقت معين
```sql
-- البحث بالنص
SELECT * FROM bus_trips WHERE departure_time LIKE '%ظهراً%';

-- البحث بالوقت للترتيب
SELECT * FROM bus_trips WHERE departure_sort_time BETWEEN '12:00:00' AND '15:00:00';
```

## الدعم
إذا واجهت أي مشاكل، تأكد من:
1. تنفيذ التحديث بشكل صحيح
2. تحديث جميع الملفات المطلوبة
3. مراجعة ملفات الأخطاء في المتصفح
4. التأكد من صحة بيانات قاعدة البيانات
