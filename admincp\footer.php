<footer class="footer text-center text-sm-left"><?php echo $Site_Name;?> &copy; <?php echo date("Y") ;?> <span class="text-muted d-none d-sm-inline-block float-right">برمجة وتصميم <i class="mdi mdi-heart text-danger"></i> <a href="https://wiilx.com/">Wiilx.com</a></span></footer>
<!--end footer-->
</div>
<!-- end page content -->
</div>
<!-- end page-wrapper --><!-- jQuery  -->
<script src="assets/js/jquery.min.js"></script>
<script src="assets/js/jquery-ui.min.js"></script>
<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/metismenu.min.js"></script>
<script src="assets/js/waves.js"></script>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/jquery.slimscroll.min.js"></script>
<script src="plugins/apexcharts/apexcharts.min.js"></script>
<script src="assets/js/jquery.dataTables.min.js"></script>
<script src="assets/js/sweetalert2.js"></script>
<script src="assets/js/jquery.uploader.min.js"></script>
<!-- App js -->
<script src="assets/js/app.js"></script>

<?php
if (isset($_SESSION['userData'])){
?>
<script>	
	var site_url = "<?php echo $Site_URL;?>";
	$(document).ready(function() {
		var navhigh = $(window).height() - $('.topbar-left').height()- $('.leftbar-profile').height() - 50;
		$('.metismenu').css('height',navhigh);
    });
	 
	function ShowMoreLess(t){
		var xx = $(t).parent('.card').find('.card-footer').attr('style');
		 
		if(xx == undefined || xx.includes('none')){
			$('.showmoreless').html('<i class="fa fa-chevron-down" aria-hidden="true"></i>');
			$(t).html('<i class="fa fa-chevron-up" aria-hidden="true"></i>');

			$('.card-footer').hide(100);
			$(t).parent('.card').find('.card-footer').show(200);
		}else{
			$('.showmoreless').html('<i class="fa fa-chevron-down" aria-hidden="true"></i>');
			$('.card-footer').hide(100); 
		}
		
	} 
</script>

<?php
}
?>

</body>
</html>