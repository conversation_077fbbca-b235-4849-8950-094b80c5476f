<?php
ob_start();
include('../webset.php'); 
include('../session.php'); 
$Title_page  = "تسجيل الدخول";
?>
<style>
.login_body{
    background: radial-gradient(circle at 39% 47%, rgba(107,107,107,0.08) 0%, rgba(107,107,107,0.08) 33.333%, rgba(72,72,72,0.08) 33.333%, rgba(72,72,72,0.08) 66.666%, rgba(36,36,36,0.08) 66.666%, rgba(36,36,36,0.08) 99.999%),radial-gradient(circle at 53% 74%, rgba(182,182,182,0.08) 0%, rgba(182,182,182,0.08) 33.333%, rgba(202,202,202,0.08) 33.333%, rgba(202,202,202,0.08) 66.666%, rgba(221,221,221,0.08) 66.666%, rgba(221,221,221,0.08) 99.999%),radial-gradient(circle at 14% 98%, rgba(184,184,184,0.08) 0%, rgba(184,184,184,0.08) 33.333%, rgba(96,96,96,0.08) 33.333%, rgba(96,96,96,0.08) 66.666%, rgba(7,7,7,0.08) 66.666%, rgba(7,7,7,0.08) 99.999%),linear-gradient(45deg, #f79f9b, #0f4069);
}
.login_body .footer {
    border-top: 0;
    color: #fff;
}
.login_body footer .text-muted {
    color: #ffffff !important;
}
[class^="dripicons-"]:before, [class*=" dripicons-"]:before {
    font-family: "dripicons-v2" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    line-height: 30px !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
</style>
<?php
if (isset($_SESSION['userData'])){
	header("Location:".$Site_URL.'/index.php'); exit();
}
include('header.php'); 
?>

 <div class="container">
   <div class="row vh-100">
      <div class="col-12 align-self-center">
         <div class="auth-page">
            <div class="card auth-card shadow-lg" style="border-radius: 15px;">
               <div class="card-body">
                  <div class="px-3">
                    
                     <!--end auth-logo-box-->
                     <div class="text-center auth-logo-text">
                        <h4 class="mt-0 mb-3 mt-5"><?php echo $Site_Name;?></h4>
                        <p class="text-muted mb-0">تسجيل الدخول</p>
                     </div>
                     <!--end auth-logo-text-->
                    <?php
                        if (isset($_POST['login'])){
                            $var1  = trim($_POST['username']) ;
                            $var2  = trim($_POST['userpassword']) ;
                            if (empty($var1) || empty($var2)){
                                echo '<br>'. Show_Alert("danger" , "يجب ملئ جميع الحقول") ;
                            }else{
                                
                                if ($var1 == GetTableSet('AdminUser') && $var2 == GetTableSet('AdminPass')){
                                    $_SESSION['userData'] = GetTableSet('AdminUser');
                                    header("Location:".$Site_URL.'/admincp/index.php'); exit();
                                }else{
                                    echo '<br>'. Show_Alert("danger" , "بيانات الدخول غير صحيحه") ;
                                }
                            }
                        }
                    ?>
                     <form method="post" class="form-horizontal auth-form my-4">
                        <div class="form-group">
                           <label for="username">رقم الجوال</label>
                           <div class="input-group mb-3"><span class="auth-form-icon"><i class="mdi mdi-cellphone-android"></i> </span><input type="text" class="form-control" id="username" name="username" placeholder="رقم الجوال"></div>
                        </div>
                        <!--end form-group-->
                        <div class="form-group">
                           <label for="userpassword">كلمة المرور</label>
                           <div class="input-group mb-3"><span class="auth-form-icon"><i class="mdi mdi-lock"></i> </span><input type="password" class="form-control" id="userpassword" name="userpassword" placeholder="كلمة المرور"></div>
                        </div>
                        <!--end form-group-->
                        
                        <!--end form-group-->
                        <div class="form-group mb-0 row">
                           <div class="col-12 mt-2"><button class="btn btn-gradient-primary btn-round btn-block waves-effect waves-light" name="login" type="submit">تسجيل الدخول <i class="fas fa-sign-in-alt ml-1"></i></button></div>
                           <!--end col-->
                        </div>
                        <!--end form-group-->
                        

                     </form>
                     <!--end form-->
                  </div>
                  
               </div>
               <!--end card-body-->
            </div>
            
            <!--end account-social-->
         </div>
         <!--end auth-page-->
         </div>
<?php
include('footer.php');
?>