<?php
include('webset.php');

echo "<h2>إضافة رحلات تجريبية بأوقات نصية</h2>";

try {
    // بيانات الرحلات التجريبية بأوقات نصية
    $sample_trips = [
        // رحلات من الرياض إلى مكة
        ['الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '1.5 ظهراً', '13:30:00', '17:30:00', 100.00, 50, 'to_mecca', '0', 1],
        ['الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '8 مساءً', '20:00:00', '00:00:00', 100.00, 50, 'to_mecca', '0', 1],
        ['الرياض', 'محطة الملك فهد', 'مكة المكرمة', 'شارع ابراهيم الخليل', '3 عصراً', '15:00:00', '19:00:00', 120.00, 45, 'to_mecca', '0', 1],
        ['الرياض', 'محطة الملك فهد', 'مكة المكرمة', 'شارع ابراهيم الخليل', '10:30 صباحاً', '10:30:00', '14:30:00', 120.00, 45, 'to_mecca', '0', 1],
        
        // رحلات من جدة إلى مكة
        ['جدة', 'محطة جدة المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '2 ظهراً', '14:00:00', '15:30:00', 50.00, 45, 'to_mecca', '0', 1],
        ['جدة', 'محطة جدة المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', 'بعد صلاة العصر', '15:30:00', '17:00:00', 50.00, 45, 'to_mecca', '0', 1],
        ['جدة', 'محطة الحرمين', 'مكة المكرمة', 'شارع ابراهيم الخليل', '6:30 مساءً', '18:30:00', '20:00:00', 60.00, 40, 'to_mecca', '0', 1],
        
        // رحلات من الدمام إلى مكة
        ['الدمام', 'محطة الدمام المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '11 صباحاً', '11:00:00', '18:00:00', 150.00, 40, 'to_mecca', '0', 1],
        ['الدمام', 'محطة الدمام المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '9:30 مساءً', '21:30:00', '04:30:00', 150.00, 40, 'to_mecca', '0', 1],
        
        // رحلات من مكة إلى المدن الأخرى
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الرياض', 'محطة البطحاء', 'بعد صلاة الفجر', '05:30:00', '09:30:00', 100.00, 50, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الرياض', 'محطة البطحاء', '2:30 ظهراً', '14:30:00', '18:30:00', 100.00, 50, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الرياض', 'محطة الملك فهد', '7 مساءً', '19:00:00', '23:00:00', 120.00, 45, 'from_mecca', '0', 1],
        
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'جدة', 'محطة جدة المركزية', '10 صباحاً', '10:00:00', '11:30:00', 50.00, 45, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'جدة', 'محطة جدة المركزية', '4:30 عصراً', '16:30:00', '18:00:00', 50.00, 45, 'from_mecca', '0', 1],
        
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الدمام', 'محطة الدمام المركزية', '4 عصراً', '16:00:00', '23:00:00', 150.00, 40, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الطائف', 'محطة الطائف', '6 مساءً', '18:00:00', '19:30:00', 40.00, 35, 'from_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'المدينة المنورة', 'محطة المدينة', 'بعد صلاة المغرب', '19:30:00', '23:30:00', 80.00, 50, 'from_mecca', '0', 1],
        
        // رحلات خاصة بأوقات مميزة
        ['الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', 'منتصف الليل', '00:00:00', '04:00:00', 110.00, 50, 'to_mecca', '0', 1],
        ['جدة', 'محطة الحرمين', 'مكة المكرمة', 'شارع ابراهيم الخليل', 'الفجر', '05:00:00', '06:30:00', 55.00, 40, 'to_mecca', '0', 1],
        ['مكة المكرمة', 'شارع ابراهيم الخليل', 'الرياض', 'محطة البطحاء', 'بعد صلاة الظهر', '13:00:00', '17:00:00', 105.00, 50, 'from_mecca', '0', 1]
    ];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>سيتم إضافة " . count($sample_trips) . " رحلة تجريبية بأوقات نصية مثل:</h4>";
    echo "<ul>";
    echo "<li>1.5 ظهراً</li>";
    echo "<li>8 مساءً</li>";
    echo "<li>بعد صلاة العصر</li>";
    echo "<li>منتصف الليل</li>";
    echo "<li>الفجر</li>";
    echo "</ul>";
    echo "</div>";
    
    if (isset($_GET['confirm']) && $_GET['confirm'] == 'yes') {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>جاري إضافة الرحلات...</h4>";
        
        // التحقق من وجود الحقول الجديدة
        $check_columns = $db->query("SHOW COLUMNS FROM bus_trips LIKE 'departure_sort_time'");
        if ($check_columns->rowCount() == 0) {
            echo "<p style='color: red;'>❌ يجب تنفيذ تحديث قاعدة البيانات أولاً باستخدام update_departure_time.php</p>";
            echo "</div>";
            return;
        }
        
        // حذف البيانات التجريبية القديمة (اختياري)
        if (isset($_GET['clear']) && $_GET['clear'] == 'yes') {
            $db->exec("DELETE FROM bus_trips WHERE seat_price IN (100, 120, 50, 150, 40, 80, 110, 55, 105, 60)");
            echo "<p style='color: orange;'>🗑️ تم حذف البيانات التجريبية القديمة</p>";
        }
        
        $stmt = $db->prepare("INSERT INTO bus_trips (from_city, from_station, to_city, to_station, departure_time, departure_sort_time, arrival_time, seat_price, total_seats, trip_type, available_days, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $added_count = 0;
        foreach ($sample_trips as $trip) {
            try {
                $stmt->execute($trip);
                $added_count++;
                echo "<p style='color: green;'>✅ تم إضافة رحلة: {$trip[0]} → {$trip[2]} في {$trip[4]}</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة رحلة: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "</div>";
        
        echo "<div style='color: green; font-weight: bold; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "✅ تم إضافة $added_count رحلة بنجاح!";
        echo "</div>";
        
        // عرض عينة من البيانات المضافة
        echo "<h3>عينة من الرحلات المضافة:</h3>";
        $stmt = $db->query("SELECT id, from_city, to_city, departure_time, departure_sort_time, seat_price FROM bus_trips ORDER BY id DESC LIMIT 10");
        $new_trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($new_trips) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>من المدينة</th>";
            echo "<th style='padding: 8px;'>إلى المدينة</th>";
            echo "<th style='padding: 8px;'>وقت المغادرة النصي</th>";
            echo "<th style='padding: 8px;'>وقت الترتيب</th>";
            echo "<th style='padding: 8px;'>السعر</th>";
            echo "</tr>";
            
            foreach ($new_trips as $trip) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $trip['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['from_city'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['to_city'] . "</td>";
                echo "<td style='padding: 8px; font-weight: bold; color: #28a745;'>" . $trip['departure_time'] . "</td>";
                echo "<td style='padding: 8px; color: #666;'>" . $trip['departure_sort_time'] . "</td>";
                echo "<td style='padding: 8px;'>" . $trip['seat_price'] . " ريال</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>الخطوات التالية:</h4>";
        echo "<ul>";
        echo "<li>اذهب إلى <a href='bus-booking.php' target='_blank'>صفحة حجز الباصات</a> لاختبار النظام</li>";
        echo "<li>جرب البحث عن رحلات مختلفة</li>";
        echo "<li>تأكد من ظهور الأوقات النصية بشكل صحيح</li>";
        echo "<li>يمكنك تعديل الأوقات مباشرة في قاعدة البيانات</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        // عرض أزرار التأكيد
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ تأكيد الإضافة:</h4>";
        echo "<p>هل تريد إضافة الرحلات التجريبية؟</p>";
        echo "<div style='margin-top: 15px;'>";
        echo "<a href='?confirm=yes' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-left: 10px;'>إضافة الرحلات</a>";
        echo "<a href='?confirm=yes&clear=yes' style='background: #ff9500; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>حذف القديم وإضافة الجديد</a>";
        echo "</div>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>
