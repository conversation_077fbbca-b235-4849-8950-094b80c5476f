var options={chart:{height:355,type:"line",stacked:!0,toolbar:{show:!1,autoSelected:"zoom"},dropShadow:{enabled:!0,top:12,left:0,bottom:0,right:0,blur:2,color:"#45404a2e",opacity:.35}},colors:["#2a77f4","#1ccab8","#f02fc2"],dataLabels:{enabled:!1},stroke:{curve:"smooth",width:[4,4],dashArray:[0,3]},grid:{borderColor:"#45404a2e",padding:{left:0,right:0},strokeDashArray:4},markers:{size:0,hover:{size:0}},series:[{name:"General Patients",data:[0,60,20,90,45,110,55,130,44,110,75,200]},{name:"OPD",data:[0,45,10,75,35,94,40,115,30,105,65,190]}],xaxis:{type:"month",categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],axisBorder:{show:!0,color:"#45404a2e"},axisTicks:{show:!0,color:"#45404a2e"}},fill:{type:"gradient",gradient:{gradientToColors:["#F55555","#B5AC49","#6094ea"]}},tooltip:{x:{format:"dd/MM/yy HH:mm"}},legend:{position:"top",horizontalAlign:"right"}};(chart=new ApexCharts(document.querySelector("#hospital_survey"),options)).render();options={chart:{height:200,type:"bar",toolbar:{show:!1}},plotOptions:{bar:{horizontal:!1,endingShape:"rounded",columnWidth:"25%"}},dataLabels:{enabled:!1},stroke:{show:!0,width:2,colors:["transparent"]},colors:["#7367f0","#dfdeec"],series:[{name:"Male",data:[68,44,55,57,56,61,58]},{name:"Female",data:[51,76,85,101,98,87,105]}],xaxis:{categories:["Sun","Mon","Tue","Wed","Thu","Fri","Set"],axisBorder:{show:!0,color:"#bec7e0"},axisTicks:{show:!0,color:"#bec7e0"}},legend:{show:!1,position:"top",horizontalAlign:"right"},fill:{opacity:1},grid:{row:{colors:["transparent","transparent"],opacity:.2},borderColor:"#f1f3fa",strokeDashArray:4},tooltip:{y:{formatter:function(e){return""+e}}}};(chart=new ApexCharts(document.querySelector("#patient_dash_report"),options)).render();var chart;options={chart:{height:235,type:"donut"},plotOptions:{pie:{donut:{size:"80%"}}},dataLabels:{enabled:!1},stroke:{show:!0,width:2,colors:["transparent"]},series:[10,65,25],legend:{show:!0,position:"bottom",horizontalAlign:"center",verticalAlign:"middle",floating:!1,fontSize:"14px",offsetX:0,offsetY:5},labels:["Syrup","Tablets","Injections"],colors:["#fa7901","#18b2ce","#ec523f"],responsive:[{breakpoint:600,options:{plotOptions:{donut:{customScale:.2}},chart:{height:240},legend:{show:!1}}}],tooltip:{y:{formatter:function(e){return e+" %"}}}};(chart=new ApexCharts(document.querySelector("#dash_medicine"),options)).render(),$("#datatable").DataTable();